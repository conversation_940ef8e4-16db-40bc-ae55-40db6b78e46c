<?php

declare(strict_types=1);

// Database test
header('Content-Type: application/json; charset=utf-8');

// Composer autoloader
require_once dirname(__DIR__, 2) . '/vendor/autoload.php';

use EticSimple\Core\Database\DatabaseManager;

try {
    // Environment yükle
    $dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__, 2));
    $dotenv->load();
    
    // Database Manager oluştur
    $db = new DatabaseManager([
        'host' => $_ENV['DB_HOST'],
        'port' => (int)$_ENV['DB_PORT'],
        'database' => $_ENV['DB_DATABASE'],
        'username' => $_ENV['DB_USERNAME'],
        'password' => $_ENV['DB_PASSWORD'],
        'charset' => $_ENV['DB_CHARSET'] ?? 'utf8mb4',
        'collation' => $_ENV['DB_COLLATION'] ?? 'utf8mb4_unicode_ci',
    ]);
    
    // Bağlantı test
    $testResult = $db->fetchOne("SELECT 1 as test");
    
    echo json_encode([
        'success' => true,
        'message' => 'Database test başarılı',
        'data' => [
            'test_result' => $testResult,
            'database' => $_ENV['DB_DATABASE']
        ]
    ], JSON_UNESCAPED_UNICODE);
    
} catch (\Throwable $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Database test hatası: ' . $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ], JSON_UNESCAPED_UNICODE);
}
