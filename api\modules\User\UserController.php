<?php

declare(strict_types=1);

namespace EticSimple\Modules\User;

use EticSimple\Core\Application;
use EticSimple\Core\Auth\AuthManager;

/**
 * User Controller
 * 
 * Kullanıcı authentication ve profil yönetimi
 */
class UserController
{
    private UserRepository $userRepository;
    private UserAddressRepository $addressRepository;
    private AuthManager $auth;
    
    public function __construct()
    {
        $this->userRepository = new UserRepository();
        $this->addressRepository = new UserAddressRepository();
        $this->auth = Application::getInstance()->get('auth');
    }
    
    /**
     * Kullanıcı kaydı
     */
    public function register(): array
    {
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            
            // Validation
            $errors = $this->validateRegistration($input);
            if (!empty($errors)) {
                return error_response('Kayıt bilgileri hatalı', $errors, 400);
            }
            
            // T.C. Kimlik No doğrulama (opsiyonel)
            if (!empty($input['tc_kimlik_no'])) {
                if (!TcKimlikValidator::validate($input['tc_kimlik_no'])) {
                    return error_response('Geçersiz T.C. Kimlik No', ['tc_kimlik_no' => 'T.C. Kimlik No geçersiz'], 400);
                }
                
                // T.C. Kimlik No benzersizlik kontrolü
                if (!$this->userRepository->isTcKimlikNoUnique($input['tc_kimlik_no'])) {
                    return error_response('Bu T.C. Kimlik No zaten kayıtlı', ['tc_kimlik_no' => 'Bu T.C. Kimlik No zaten kullanılıyor'], 400);
                }
            }
            
            // Email benzersizlik kontrolü
            if (!$this->userRepository->isEmailUnique($input['email'])) {
                return error_response('Bu email adresi zaten kayıtlı', ['email' => 'Bu email adresi zaten kullanılıyor'], 400);
            }
            
            // User oluştur
            $user = new User();
            $user->setEmail($input['email'])
                 ->setPasswordHash(hash_password($input['password']))
                 ->setFirstName($input['first_name'])
                 ->setLastName($input['last_name'])
                 ->setPhone($input['phone'] ?? null)
                 ->setTcKimlikNo($input['tc_kimlik_no'] ?? null)
                 ->setBirthDate($input['birth_date'] ?? null)
                 ->setGender($input['gender'] ?? null)
                 ->setPreferredLanguage($input['preferred_language'] ?? 'tr')
                 ->setPreferredTheme($input['preferred_theme'] ?? 'light')
                 ->setNewsletterSubscribed($input['newsletter_subscribed'] ?? false)
                 ->setMarketingEmails($input['marketing_emails'] ?? true);
            
            $createdUser = $this->userRepository->create($user);
            
            if (!$createdUser) {
                return error_response('Kullanıcı kaydı başarısız', [], 500);
            }
            
            // JWT token oluştur
            $token = $this->auth->generateToken([
                'user_id' => $createdUser->getId(),
                'email' => $createdUser->getEmail(),
                'type' => 'access'
            ]);
            
            $refreshToken = $this->auth->generateRefreshToken([
                'user_id' => $createdUser->getId(),
                'email' => $createdUser->getEmail()
            ]);
            
            return response([
                'user' => $createdUser->toPublicArray(),
                'access_token' => $token,
                'refresh_token' => $refreshToken,
                'token_type' => 'Bearer'
            ], 'Kayıt başarılı');
            
        } catch (\Exception $e) {
            Application::getInstance()->get('logger')->error('User registration failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return error_response('Kayıt işlemi başarısız', [], 500);
        }
    }
    
    /**
     * Kullanıcı girişi
     */
    public function login(): array
    {
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            
            // Validation
            if (empty($input['email']) || empty($input['password'])) {
                return error_response('Email ve şifre gerekli', [], 400);
            }
            
            // Kullanıcıyı bul
            $user = $this->userRepository->findByEmail($input['email']);
            
            if (!$user) {
                $this->userRepository->incrementFailedLoginAttempts($input['email']);
                return error_response('Geçersiz email veya şifre', [], 401);
            }
            
            // Kullanıcı kilitli mi kontrol et
            if ($user->isLocked()) {
                return error_response('Hesabınız geçici olarak kilitlenmiştir', [], 423);
            }
            
            // Kullanıcı aktif mi kontrol et
            if (!$user->isActive()) {
                return error_response('Hesabınız aktif değil', [], 403);
            }
            
            // Şifre kontrolü
            if (!verify_password($input['password'], $user->getPasswordHash())) {
                $this->userRepository->incrementFailedLoginAttempts($input['email']);
                
                // Çok fazla başarısız deneme varsa kilitle
                if ($user->getFailedLoginAttempts() >= 5) {
                    $this->userRepository->lockUser($user->getId(), 30);
                }
                
                return error_response('Geçersiz email veya şifre', [], 401);
            }
            
            // Başarılı giriş
            $this->userRepository->updateLastLogin($user->getId());
            
            // JWT token oluştur
            $token = $this->auth->generateToken([
                'user_id' => $user->getId(),
                'email' => $user->getEmail(),
                'type' => 'access'
            ]);
            
            $refreshToken = $this->auth->generateRefreshToken([
                'user_id' => $user->getId(),
                'email' => $user->getEmail()
            ]);
            
            return response([
                'user' => $user->toPublicArray(),
                'access_token' => $token,
                'refresh_token' => $refreshToken,
                'token_type' => 'Bearer'
            ], 'Giriş başarılı');
            
        } catch (\Exception $e) {
            Application::getInstance()->get('logger')->error('User login failed', [
                'email' => $input['email'] ?? 'unknown',
                'error' => $e->getMessage()
            ]);
            
            return error_response('Giriş işlemi başarısız', [], 500);
        }
    }
    
    /**
     * Kullanıcı profili
     */
    public function profile(): array
    {
        try {
            $currentUser = $this->auth->getCurrentUser();
            
            if (!$currentUser) {
                return error_response('Yetkisiz erişim', [], 401);
            }
            
            $user = $this->userRepository->findById($currentUser['user_id']);
            
            if (!$user) {
                return error_response('Kullanıcı bulunamadı', [], 404);
            }
            
            return response($user->toPublicArray(), 'Profil bilgileri');
            
        } catch (\Exception $e) {
            return error_response('Profil bilgileri alınamadı', [], 500);
        }
    }
    
    /**
     * Profil güncelleme
     */
    public function updateProfile(): array
    {
        try {
            $currentUser = $this->auth->getCurrentUser();
            
            if (!$currentUser) {
                return error_response('Yetkisiz erişim', [], 401);
            }
            
            $user = $this->userRepository->findById($currentUser['user_id']);
            
            if (!$user) {
                return error_response('Kullanıcı bulunamadı', [], 404);
            }
            
            $input = json_decode(file_get_contents('php://input'), true);
            
            // Validation
            $errors = $this->validateProfileUpdate($input, $user->getId());
            if (!empty($errors)) {
                return error_response('Profil bilgileri hatalı', $errors, 400);
            }
            
            // T.C. Kimlik No doğrulama (değiştirilmişse)
            if (isset($input['tc_kimlik_no']) && $input['tc_kimlik_no'] !== $user->getTcKimlikNo()) {
                if (!empty($input['tc_kimlik_no'])) {
                    if (!TcKimlikValidator::validate($input['tc_kimlik_no'])) {
                        return error_response('Geçersiz T.C. Kimlik No', ['tc_kimlik_no' => 'T.C. Kimlik No geçersiz'], 400);
                    }
                    
                    if (!$this->userRepository->isTcKimlikNoUnique($input['tc_kimlik_no'], $user->getId())) {
                        return error_response('Bu T.C. Kimlik No zaten kayıtlı', ['tc_kimlik_no' => 'Bu T.C. Kimlik No zaten kullanılıyor'], 400);
                    }
                }
            }
            
            // Profil güncelle
            if (isset($input['first_name'])) $user->setFirstName($input['first_name']);
            if (isset($input['last_name'])) $user->setLastName($input['last_name']);
            if (isset($input['phone'])) $user->setPhone($input['phone']);
            if (isset($input['tc_kimlik_no'])) $user->setTcKimlikNo($input['tc_kimlik_no']);
            if (isset($input['birth_date'])) $user->setBirthDate($input['birth_date']);
            if (isset($input['gender'])) $user->setGender($input['gender']);
            if (isset($input['preferred_language'])) $user->setPreferredLanguage($input['preferred_language']);
            if (isset($input['preferred_theme'])) $user->setPreferredTheme($input['preferred_theme']);
            if (isset($input['newsletter_subscribed'])) $user->setNewsletterSubscribed($input['newsletter_subscribed']);
            if (isset($input['marketing_emails'])) $user->setMarketingEmails($input['marketing_emails']);
            
            $success = $this->userRepository->update($user);
            
            if (!$success) {
                return error_response('Profil güncellenemedi', [], 500);
            }
            
            return response($user->toPublicArray(), 'Profil başarıyla güncellendi');
            
        } catch (\Exception $e) {
            return error_response('Profil güncelleme başarısız', [], 500);
        }
    }
    
    /**
     * Şifre değiştirme
     */
    public function changePassword(): array
    {
        try {
            $currentUser = $this->auth->getCurrentUser();
            
            if (!$currentUser) {
                return error_response('Yetkisiz erişim', [], 401);
            }
            
            $user = $this->userRepository->findById($currentUser['user_id']);
            
            if (!$user) {
                return error_response('Kullanıcı bulunamadı', [], 404);
            }
            
            $input = json_decode(file_get_contents('php://input'), true);
            
            // Validation
            if (empty($input['current_password']) || empty($input['new_password'])) {
                return error_response('Mevcut şifre ve yeni şifre gerekli', [], 400);
            }
            
            // Mevcut şifre kontrolü
            if (!verify_password($input['current_password'], $user->getPasswordHash())) {
                return error_response('Mevcut şifre hatalı', [], 400);
            }
            
            // Yeni şifre validation
            if (strlen($input['new_password']) < 8) {
                return error_response('Yeni şifre en az 8 karakter olmalıdır', [], 400);
            }
            
            // Şifre güncelle
            $success = $this->userRepository->updatePassword($user->getId(), hash_password($input['new_password']));
            
            if (!$success) {
                return error_response('Şifre güncellenemedi', [], 500);
            }
            
            return response(null, 'Şifre başarıyla değiştirildi');
            
        } catch (\Exception $e) {
            return error_response('Şifre değiştirme başarısız', [], 500);
        }
    }
    
    /**
     * Kayıt validation
     */
    private function validateRegistration(array $data): array
    {
        $errors = [];
        
        // Zorunlu alanlar
        $required = ['email', 'password', 'first_name', 'last_name'];
        foreach ($required as $field) {
            if (empty($data[$field])) {
                $errors[$field] = ucfirst(str_replace('_', ' ', $field)) . ' alanı zorunludur';
            }
        }
        
        // Email format
        if (!empty($data['email']) && !is_valid_email($data['email'])) {
            $errors['email'] = 'Geçerli bir email adresi giriniz';
        }
        
        // Şifre uzunluğu
        if (!empty($data['password']) && strlen($data['password']) < 8) {
            $errors['password'] = 'Şifre en az 8 karakter olmalıdır';
        }
        
        // Telefon formatı
        if (!empty($data['phone']) && !preg_match('/^(\+90|0)?[5][0-9]{9}$/', preg_replace('/[^0-9+]/', '', $data['phone']))) {
            $errors['phone'] = 'Geçerli bir telefon numarası giriniz';
        }
        
        return $errors;
    }
    
    /**
     * Profil güncelleme validation
     */
    private function validateProfileUpdate(array $data, int $userId): array
    {
        $errors = [];
        
        // Email format (değiştirilmişse)
        if (isset($data['email'])) {
            if (!is_valid_email($data['email'])) {
                $errors['email'] = 'Geçerli bir email adresi giriniz';
            } elseif (!$this->userRepository->isEmailUnique($data['email'], $userId)) {
                $errors['email'] = 'Bu email adresi zaten kullanılıyor';
            }
        }
        
        // Telefon formatı
        if (isset($data['phone']) && !empty($data['phone'])) {
            if (!preg_match('/^(\+90|0)?[5][0-9]{9}$/', preg_replace('/[^0-9+]/', '', $data['phone']))) {
                $errors['phone'] = 'Geçerli bir telefon numarası giriniz';
            }
        }
        
        return $errors;
    }
}
