<?php

declare(strict_types=1);

/*
 * This file is part of PHP CS Fixer.
 *
 * (c) Fabien Potencier <<EMAIL>>
 *     <PERSON><PERSON> <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled
 * with this source code in the file LICENSE.
 */

namespace PhpCs<PERSON>ixer\Fixer\Comment;

use PhpCsF<PERSON>er\AbstractFixer;
use PhpCsFixer\Fixer\ConfigurableFixerInterface;
use PhpCsFixer\Fixer\ConfigurableFixerTrait;
use PhpCsFixer\FixerConfiguration\AllowedValueSubset;
use PhpCsFixer\FixerConfiguration\FixerConfigurationResolver;
use PhpCsFixer\FixerConfiguration\FixerConfigurationResolverInterface;
use PhpCsFixer\FixerConfiguration\FixerOptionBuilder;
use PhpCsFixer\FixerDefinition\CodeSample;
use PhpCsFixer\FixerDefinition\FixerDefinition;
use PhpCs<PERSON><PERSON><PERSON>\FixerDefinition\FixerDefinitionInterface;
use PhpCs<PERSON><PERSON><PERSON>\Preg;
use PhpCsFixer\Tokenizer\Token;
use PhpCsFixer\Tokenizer\Tokens;

/**
 * <AUTHOR> Tessarotto <<EMAIL>>
 *
 * @implements ConfigurableFixerInterface<_AutogeneratedInputConfiguration, _AutogeneratedComputedConfiguration>
 *
 * @phpstan-type _AutogeneratedInputConfiguration array{
 *  comment_types?: list<'asterisk'|'hash'>,
 * }
 * @phpstan-type _AutogeneratedComputedConfiguration array{
 *  comment_types: list<'asterisk'|'hash'>,
 * }
 */
final class SingleLineCommentStyleFixer extends AbstractFixer implements ConfigurableFixerInterface
{
    /** @use ConfigurableFixerTrait<_AutogeneratedInputConfiguration, _AutogeneratedComputedConfiguration> */
    use ConfigurableFixerTrait;

    private bool $asteriskEnabled;

    private bool $hashEnabled;

    public function getDefinition(): FixerDefinitionInterface
    {
        return new FixerDefinition(
            'Single-line comments and multi-line comments with only one line of actual content should use the `//` syntax.',
            [
                new CodeSample(
                    '<?php
/* asterisk comment */
$a = 1;

# hash comment
$b = 2;

/*
 * multi-line
 * comment
 */
$c = 3;
'
                ),
                new CodeSample(
                    '<?php
/* first comment */
$a = 1;

/*
 * second comment
 */
$b = 2;

/*
 * third
 * comment
 */
$c = 3;
',
                    ['comment_types' => ['asterisk']]
                ),
                new CodeSample(
                    "<?php # comment\n",
                    ['comment_types' => ['hash']]
                ),
            ]
        );
    }

    /**
     * {@inheritdoc}
     *
     * Must run after HeaderCommentFixer, NoUselessReturnFixer, PhpdocToCommentFixer.
     */
    public function getPriority(): int
    {
        return -31;
    }

    public function isCandidate(Tokens $tokens): bool
    {
        return $tokens->isTokenKindFound(T_COMMENT);
    }

    protected function configurePostNormalisation(): void
    {
        $this->asteriskEnabled = \in_array('asterisk', $this->configuration['comment_types'], true);
        $this->hashEnabled = \in_array('hash', $this->configuration['comment_types'], true);
    }

    protected function applyFix(\SplFileInfo $file, Tokens $tokens): void
    {
        foreach ($tokens as $index => $token) {
            if (!$token->isGivenKind(T_COMMENT)) {
                continue;
            }

            $content = $token->getContent();

            /** @TODO PHP 8.0 - no more need for `?: ''` */
            $commentContent = substr($content, 2, -2) ?: ''; // @phpstan-ignore-line

            if ($this->hashEnabled && str_starts_with($content, '#')) {
                if (isset($content[1]) && '[' === $content[1]) {
                    continue; // This might be an attribute on PHP8, do not change
                }

                $tokens[$index] = new Token([$token->getId(), '//'.substr($content, 1)]);

                continue;
            }

            if (
                !$this->asteriskEnabled
                || str_contains($commentContent, '?>')
                || !str_starts_with($content, '/*')
                || Preg::match('/[^\s\*].*\R.*[^\s\*]/s', $commentContent)
            ) {
                continue;
            }

            $nextTokenIndex = $index + 1;
            if (isset($tokens[$nextTokenIndex])) {
                $nextToken = $tokens[$nextTokenIndex];
                if (!$nextToken->isWhitespace() || !Preg::match('/\R/', $nextToken->getContent())) {
                    continue;
                }

                $tokens[$nextTokenIndex] = new Token([$nextToken->getId(), ltrim($nextToken->getContent(), " \t")]);
            }

            $content = '//';
            if (Preg::match('/[^\s\*]/', $commentContent)) {
                $content = '// '.Preg::replace('/[\s\*]*([^\s\*](?:.+[^\s\*])?)[\s\*]*/', '\1', $commentContent);
            }
            $tokens[$index] = new Token([$token->getId(), $content]);
        }
    }

    protected function createConfigurationDefinition(): FixerConfigurationResolverInterface
    {
        return new FixerConfigurationResolver([
            (new FixerOptionBuilder('comment_types', 'List of comment types to fix.'))
                ->setAllowedTypes(['string[]'])
                ->setAllowedValues([new AllowedValueSubset(['asterisk', 'hash'])])
                ->setDefault(['asterisk', 'hash'])
                ->getOption(),
        ]);
    }
}
