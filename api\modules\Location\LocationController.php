<?php

declare(strict_types=1);

namespace EticSimple\Modules\Location;

use EticSimple\Core\Application;
use EticSimple\Core\Database\DatabaseManager;

/**
 * Location Controller
 * 
 * Türkiye il/ilçe lokasyon yönetimi
 */
class LocationController
{
    private DatabaseManager $db;
    
    public function __construct()
    {
        $this->db = Application::getInstance()->get('db');
    }
    
    /**
     * Tüm illeri listele
     */
    public function getCities(): array
    {
        try {
            $sql = "SELECT id, name, plate_code, region, latitude, longitude, population 
                    FROM cities 
                    ORDER BY name ASC";
            
            $cities = $this->db->fetchAll($sql);
            
            return response($cities, 'İller listelendi');
            
        } catch (\Exception $e) {
            Application::getInstance()->get('logger')->error('Cities listing failed', [
                'error' => $e->getMessage()
            ]);
            
            return error_response('İller listelenemedi', [], 500);
        }
    }
    
    /**
     * Bel<PERSON>li bir ili getir
     */
    public function getCity(int $cityId): array
    {
        try {
            $sql = "SELECT * FROM cities WHERE id = ?";
            $city = $this->db->fetchOne($sql, [$cityId]);
            
            if (!$city) {
                return error_response('İl bulunamadı', [], 404);
            }
            
            return response($city, 'İl bilgileri');
            
        } catch (\Exception $e) {
            return error_response('İl bilgileri alınamadı', [], 500);
        }
    }
    
    /**
     * Plaka koduna göre il getir
     */
    public function getCityByPlateCode(int $plateCode): array
    {
        try {
            $sql = "SELECT * FROM cities WHERE plate_code = ?";
            $city = $this->db->fetchOne($sql, [$plateCode]);
            
            if (!$city) {
                return error_response('İl bulunamadı', [], 404);
            }
            
            return response($city, 'İl bilgileri');
            
        } catch (\Exception $e) {
            return error_response('İl bilgileri alınamadı', [], 500);
        }
    }
    
    /**
     * Bölgeye göre illeri listele
     */
    public function getCitiesByRegion(string $region): array
    {
        try {
            $validRegions = [
                'Marmara', 'Ege', 'Akdeniz', 'İç Anadolu', 
                'Karadeniz', 'Doğu Anadolu', 'Güneydoğu Anadolu'
            ];
            
            if (!in_array($region, $validRegions)) {
                return error_response('Geçersiz bölge', ['region' => 'Geçerli bölgeler: ' . implode(', ', $validRegions)], 400);
            }
            
            $sql = "SELECT id, name, plate_code, region, latitude, longitude, population 
                    FROM cities 
                    WHERE region = ? 
                    ORDER BY name ASC";
            
            $cities = $this->db->fetchAll($sql, [$region]);
            
            return response($cities, $region . ' bölgesi illeri');
            
        } catch (\Exception $e) {
            return error_response('Bölge illeri listelenemedi', [], 500);
        }
    }
    
    /**
     * Belirli bir ile ait ilçeleri listele
     */
    public function getDistricts(int $cityId): array
    {
        try {
            // Önce ilin var olup olmadığını kontrol et
            $cityCheck = $this->db->fetchOne("SELECT name FROM cities WHERE id = ?", [$cityId]);
            
            if (!$cityCheck) {
                return error_response('İl bulunamadı', [], 404);
            }
            
            $sql = "SELECT d.id, d.name, d.population, d.area_km2, d.postal_code_start, d.postal_code_end,
                           c.name as city_name, c.plate_code
                    FROM districts d
                    JOIN cities c ON d.city_id = c.id
                    WHERE d.city_id = ?
                    ORDER BY d.name ASC";
            
            $districts = $this->db->fetchAll($sql, [$cityId]);
            
            return response([
                'city' => $cityCheck,
                'districts' => $districts,
                'count' => count($districts)
            ], $cityCheck['name'] . ' ilçeleri listelendi');
            
        } catch (\Exception $e) {
            Application::getInstance()->get('logger')->error('Districts listing failed', [
                'city_id' => $cityId,
                'error' => $e->getMessage()
            ]);
            
            return error_response('İlçeler listelenemedi', [], 500);
        }
    }
    
    /**
     * Belirli bir ilçeyi getir
     */
    public function getDistrict(int $districtId): array
    {
        try {
            $sql = "SELECT d.*, c.name as city_name, c.plate_code
                    FROM districts d
                    JOIN cities c ON d.city_id = c.id
                    WHERE d.id = ?";
            
            $district = $this->db->fetchOne($sql, [$districtId]);
            
            if (!$district) {
                return error_response('İlçe bulunamadı', [], 404);
            }
            
            return response($district, 'İlçe bilgileri');
            
        } catch (\Exception $e) {
            return error_response('İlçe bilgileri alınamadı', [], 500);
        }
    }
    
    /**
     * İl/ilçe arama
     */
    public function search(): array
    {
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (empty($input['query'])) {
                return error_response('Arama terimi gerekli', [], 400);
            }
            
            $query = '%' . trim($input['query']) . '%';
            $limit = isset($input['limit']) ? min((int)$input['limit'], 50) : 20;
            
            // İl arama
            $citySql = "SELECT id, name, plate_code, region, 'city' as type
                        FROM cities 
                        WHERE name LIKE ? 
                        ORDER BY name ASC 
                        LIMIT ?";
            
            $cities = $this->db->fetchAll($citySql, [$query, $limit]);
            
            // İlçe arama
            $districtSql = "SELECT d.id, d.name, c.name as city_name, c.plate_code, 'district' as type
                           FROM districts d
                           JOIN cities c ON d.city_id = c.id
                           WHERE d.name LIKE ? 
                           ORDER BY d.name ASC 
                           LIMIT ?";
            
            $districts = $this->db->fetchAll($districtSql, [$query, $limit]);
            
            $results = array_merge($cities, $districts);
            
            return response([
                'query' => trim($input['query']),
                'results' => $results,
                'count' => count($results)
            ], 'Arama sonuçları');
            
        } catch (\Exception $e) {
            return error_response('Arama başarısız', [], 500);
        }
    }
    
    /**
     * Posta koduna göre lokasyon bul
     */
    public function getLocationByPostalCode(string $postalCode): array
    {
        try {
            if (strlen($postalCode) !== 5 || !ctype_digit($postalCode)) {
                return error_response('Geçersiz posta kodu formatı', [], 400);
            }
            
            $sql = "SELECT d.id, d.name as district_name, c.id as city_id, c.name as city_name, 
                           c.plate_code, d.postal_code_start, d.postal_code_end
                    FROM districts d
                    JOIN cities c ON d.city_id = c.id
                    WHERE ? BETWEEN d.postal_code_start AND d.postal_code_end";
            
            $location = $this->db->fetchOne($sql, [$postalCode]);
            
            if (!$location) {
                return error_response('Posta koduna ait lokasyon bulunamadı', [], 404);
            }
            
            return response($location, 'Lokasyon bulundu');
            
        } catch (\Exception $e) {
            return error_response('Posta kodu araması başarısız', [], 500);
        }
    }
    
    /**
     * Lokasyon istatistikleri
     */
    public function getStatistics(): array
    {
        try {
            // İl sayısı
            $cityCount = $this->db->fetchColumn("SELECT COUNT(*) FROM cities");
            
            // İlçe sayısı
            $districtCount = $this->db->fetchColumn("SELECT COUNT(*) FROM districts");
            
            // Bölge bazında il sayıları
            $regionStats = $this->db->fetchAll(
                "SELECT region, COUNT(*) as city_count 
                 FROM cities 
                 GROUP BY region 
                 ORDER BY city_count DESC"
            );
            
            // En kalabalık 10 il
            $topCities = $this->db->fetchAll(
                "SELECT name, plate_code, population 
                 FROM cities 
                 WHERE population IS NOT NULL 
                 ORDER BY population DESC 
                 LIMIT 10"
            );
            
            // En büyük 10 il (yüzölçümü)
            $largestCities = $this->db->fetchAll(
                "SELECT name, plate_code, area_km2 
                 FROM cities 
                 WHERE area_km2 IS NOT NULL 
                 ORDER BY area_km2 DESC 
                 LIMIT 10"
            );
            
            return response([
                'total_cities' => (int)$cityCount,
                'total_districts' => (int)$districtCount,
                'region_statistics' => $regionStats,
                'most_populated_cities' => $topCities,
                'largest_cities_by_area' => $largestCities
            ], 'Lokasyon istatistikleri');
            
        } catch (\Exception $e) {
            return error_response('İstatistikler alınamadı', [], 500);
        }
    }
    
    /**
     * Tüm bölgeleri listele
     */
    public function getRegions(): array
    {
        try {
            $sql = "SELECT region, COUNT(*) as city_count, 
                           SUM(population) as total_population,
                           SUM(area_km2) as total_area
                    FROM cities 
                    GROUP BY region 
                    ORDER BY region ASC";
            
            $regions = $this->db->fetchAll($sql);
            
            return response($regions, 'Bölgeler listelendi');
            
        } catch (\Exception $e) {
            return error_response('Bölgeler listelenemedi', [], 500);
        }
    }
}
