<?php

declare(strict_types=1);

// Composer autoloader
require_once dirname(__DIR__, 2) . '/vendor/autoload.php';

use EticSimple\Core\Application;

// Application'ı başlat
$app = Application::getInstance();

// Router'ı al
$router = $app->get('router');

// Basit routes - group olmadan
$router->get('/api/health', function () {
    return response([
        'status' => 'OK',
        'timestamp' => date('c'),
        'version' => '1.0.0'
    ], 'Sistem çalışıyor');
});

$router->get('/api/test', function () {
    return response([
        'message' => 'Test endpoint çalışıyor'
    ], 'Test başarılı');
});

// 404 handler
$router->get('/{path:.*}', function () {
    return error_response('Endpoint bulunamadı', [], 404);
});

// İsteği işle
$app->handleRequest();
