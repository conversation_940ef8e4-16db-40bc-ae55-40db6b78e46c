<?php

declare(strict_types=1);

/*
 * This file is part of PHP CS Fixer.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *     <PERSON><PERSON> <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled
 * with this source code in the file LICENSE.
 */

namespace PhpCs<PERSON>ixer\Fixer\PhpUnit;

use PhpCs<PERSON><PERSON>er\Fixer\AbstractPhpUnitFixer;
use PhpCsFixer\FixerDefinition\CodeSample;
use PhpCsFixer\FixerDefinition\FixerDefinition;
use PhpCsFixer\FixerDefinition\FixerDefinitionInterface;
use PhpCsFixer\Tokenizer\Token;
use Php<PERSON><PERSON>ixer\Tokenizer\Tokens;

/**
 * <AUTHOR> <krz<PERSON><PERSON><EMAIL>>
 */
final class PhpUnitAssertNewNamesFixer extends AbstractPhpUnitFixer
{
    public function isRisky(): bool
    {
        return true;
    }

    public function getDefinition(): FixerDefinitionInterface
    {
        return new FixerDefinition(
            'Rename deprecated PHPUnit assertions like `assertFileNotExists` to new methods like `assertFileDoesNotExist`.',
            [
                new CodeSample(
                    '<?php
final class MyTest extends \PHPUnit_Framework_TestCase
{
    public function testSomeTest()
    {
        $this->assertFileNotExists("test.php");
        $this->assertNotIsWritable("path.php");
    }
}
'
                ),
            ],
            null,
            'Fixer could be risky if one is overriding PHPUnit\'s native methods.'
        );
    }

    /**
     * {@inheritdoc}
     *
     * Must run after PhpUnitDedicateAssertFixer.
     */
    public function getPriority(): int
    {
        return -10;
    }

    protected function applyPhpUnitClassFix(Tokens $tokens, int $startIndex, int $endIndex): void
    {
        foreach ($this->getPreviousAssertCall($tokens, $startIndex, $endIndex) as $assertCall) {
            $this->fixAssertNewNames($tokens, $assertCall);
        }
    }

    /**
     * @param array{
     *     index: int,
     *     loweredName: string,
     *     openBraceIndex: int,
     *     closeBraceIndex: int,
     * } $assertCall
     */
    private function fixAssertNewNames(Tokens $tokens, array $assertCall): void
    {
        $replacements = [
            'assertnotisreadable' => 'assertIsNotReadable',
            'assertnotiswritable' => 'assertIsNotWritable',
            'assertdirectorynotexists' => 'assertDirectoryDoesNotExist',
            'assertfilenotexists' => 'assertFileDoesNotExist',
            'assertdirectorynotisreadable' => 'assertDirectoryIsNotReadable',
            'assertdirectorynotiswritable' => 'assertDirectoryIsNotWriteable',
            'assertfilenotisreadable' => 'assertFileIsNotReadable',
            'assertfilenotiswritable' => 'assertFileIsNotWriteable',
            'assertregexp' => 'assertMatchesRegularExpression',
            'assertnotregexp' => 'assertDoesNotMatchRegularExpression',
        ];
        $replacement = $replacements[$assertCall['loweredName']] ?? null;

        if (null === $replacement) {
            return;
        }

        $tokens[$assertCall['index']] = new Token([
            T_STRING,
            $replacement,
        ]);
    }
}
