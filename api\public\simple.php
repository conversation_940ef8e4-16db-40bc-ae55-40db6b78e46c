<?php

declare(strict_types=1);

// Basit Application test
header('Content-Type: application/json; charset=utf-8');

// Composer autoloader
require_once dirname(__DIR__, 2) . '/vendor/autoload.php';

use EticSimple\Core\Application;

try {
    
    // Application'ı başlat
    $app = Application::getInstance();
    
    echo json_encode([
        'success' => true,
        'message' => 'Application başarıyla başlatıldı',
        'data' => [
            'php_version' => PHP_VERSION,
            'timestamp' => date('c'),
            'app_env' => $_ENV['APP_ENV'] ?? 'unknown',
            'app_debug' => $_ENV['APP_DEBUG'] ?? 'unknown'
        ]
    ], JSON_UNESCAPED_UNICODE);
    
} catch (\Throwable $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Application başlatma hatası: ' . $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'trace' => $e->getTraceAsString()
    ], JSON_UNESCAPED_UNICODE);
}
