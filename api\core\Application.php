<?php

declare(strict_types=1);

namespace EticSimple\Core;

use EticSimple\Core\Database\DatabaseManager;
use EticSimple\Core\Router\Router;
use EticSimple\Core\Auth\AuthManager;
use EticSimple\Core\Cache\CacheManager;
use EticSimple\Core\Theme\ThemeManager;
use Dotenv\Dotenv;
use Monolog\Logger;
use Monolog\Handler\StreamHandler;
use Monolog\Handler\RotatingFileHandler;

/**
 * EticSimple Application Core
 * 
 * Ana uygulama sınıfı - Dependency Injection Container ve Service Provider
 */
class Application
{
    private static ?Application $instance = null;
    private array $services = [];
    private array $singletons = [];
    private bool $booted = false;
    
    private function __construct()
    {
        $this->loadEnvironment();
        $this->registerCoreServices();
    }
    
    public static function getInstance(): Application
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        
        return self::$instance;
    }
    
    /**
     * Environment dosyasını yükle
     */
    private function loadEnvironment(): void
    {
        $dotenv = Dotenv::createImmutable(dirname(__DIR__, 2));
        $dotenv->load();
        
        // Timezone ayarla
        date_default_timezone_set($_ENV['APP_TIMEZONE'] ?? 'Europe/Istanbul');
        
        // Locale ayarla
        setlocale(LC_ALL, $_ENV['APP_LOCALE'] ?? 'tr_TR.UTF-8');
    }
    
    /**
     * Core servisleri kaydet
     */
    private function registerCoreServices(): void
    {
        // Logger
        $this->singleton('logger', function () {
            $logger = new Logger('eticsimple');
            
            if ($_ENV['APP_ENV'] === 'production') {
                $logger->pushHandler(new RotatingFileHandler(
                    'storage/logs/app.log',
                    (int)($_ENV['LOG_MAX_FILES'] ?? 14),
                    Logger::toMonologLevel($_ENV['LOG_LEVEL'] ?? 'info')
                ));
            } else {
                $logger->pushHandler(new StreamHandler('php://stdout', Logger::DEBUG));
            }
            
            return $logger;
        });
        
        // Database Manager
        $this->singleton('db', function () {
            return new DatabaseManager([
                'host' => $_ENV['DB_HOST'],
                'port' => (int)$_ENV['DB_PORT'],
                'database' => $_ENV['DB_DATABASE'],
                'username' => $_ENV['DB_USERNAME'],
                'password' => $_ENV['DB_PASSWORD'],
                'charset' => $_ENV['DB_CHARSET'] ?? 'utf8mb4',
                'collation' => $_ENV['DB_COLLATION'] ?? 'utf8mb4_unicode_ci',
            ]);
        });
        
        // Cache Manager
        $this->singleton('cache', function () {
            return new CacheManager([
                'driver' => $_ENV['CACHE_DRIVER'] ?? 'file',
                'redis' => [
                    'host' => $_ENV['REDIS_HOST'],
                    'port' => (int)$_ENV['REDIS_PORT'],
                    'password' => $_ENV['REDIS_PASSWORD'] ?? null,
                    'database' => (int)($_ENV['REDIS_DATABASE'] ?? 0),
                ],
                'prefix' => $_ENV['CACHE_PREFIX'] ?? 'eticsimple_',
                'ttl' => (int)($_ENV['CACHE_TTL'] ?? 3600),
            ]);
        });
        
        // Router
        $this->singleton('router', function () {
            return new Router();
        });
        
        // Auth Manager
        $this->singleton('auth', function () {
            return new AuthManager([
                'jwt_secret' => $_ENV['JWT_SECRET'],
                'jwt_algorithm' => $_ENV['JWT_ALGORITHM'] ?? 'HS256',
                'jwt_ttl' => (int)($_ENV['JWT_TTL'] ?? 3600),
                'jwt_refresh_ttl' => (int)($_ENV['JWT_REFRESH_TTL'] ?? 20160),
            ]);
        });
        
        // Theme Manager
        $this->singleton('theme', function () {
            return new ThemeManager([
                'default_theme' => $_ENV['DEFAULT_THEME'] ?? 'light',
                'cache_enabled' => filter_var($_ENV['THEME_CACHE_ENABLED'] ?? true, FILTER_VALIDATE_BOOLEAN),
                'cache_ttl' => (int)($_ENV['THEME_CACHE_TTL'] ?? 86400),
            ]);
        });
    }
    
    /**
     * Servisi singleton olarak kaydet
     */
    public function singleton(string $name, callable $factory): void
    {
        $this->singletons[$name] = $factory;
    }
    
    /**
     * Servisi kaydet
     */
    public function bind(string $name, callable $factory): void
    {
        $this->services[$name] = $factory;
    }
    
    /**
     * Servis al
     */
    public function get(string $name): mixed
    {
        // Singleton kontrolü
        if (isset($this->singletons[$name])) {
            if (!isset($this->services[$name])) {
                $this->services[$name] = $this->singletons[$name]();
            }
            return $this->services[$name];
        }
        
        // Normal servis
        if (isset($this->services[$name])) {
            if (is_callable($this->services[$name])) {
                return $this->services[$name]();
            }
            return $this->services[$name];
        }
        
        throw new \InvalidArgumentException("Service '{$name}' not found");
    }
    
    /**
     * Uygulamayı başlat
     */
    public function boot(): void
    {
        if ($this->booted) {
            return;
        }
        
        // Error handling
        $this->setupErrorHandling();
        
        // CORS headers
        $this->setupCors();
        
        // Boot edildi olarak işaretle
        $this->booted = true;
        
        $this->get('logger')->info('Application booted successfully');
    }
    
    /**
     * HTTP isteğini işle
     */
    public function handleRequest(): void
    {
        try {
            $this->boot();
            
            $router = $this->get('router');
            $response = $router->dispatch();
            
            $this->sendResponse($response);
            
        } catch (\Throwable $e) {
            $this->handleException($e);
        }
    }
    
    /**
     * Error handling kurulumu
     */
    private function setupErrorHandling(): void
    {
        error_reporting(E_ALL);
        
        if ($_ENV['APP_DEBUG'] === 'true') {
            ini_set('display_errors', '1');
        } else {
            ini_set('display_errors', '0');
        }
        
        set_error_handler([$this, 'handleError']);
        set_exception_handler([$this, 'handleException']);
    }
    
    /**
     * CORS kurulumu
     */
    private function setupCors(): void
    {
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
        
        if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
            http_response_code(200);
            exit;
        }
    }
    
    /**
     * Response gönder
     */
    private function sendResponse(array $response): void
    {
        header('Content-Type: application/json; charset=utf-8');
        http_response_code($response['status'] ?? 200);
        echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    }
    
    /**
     * Error handler
     */
    public function handleError(int $severity, string $message, string $file, int $line): void
    {
        throw new \ErrorException($message, 0, $severity, $file, $line);
    }
    
    /**
     * Exception handler
     */
    public function handleException(\Throwable $e): void
    {
        $this->get('logger')->error('Unhandled exception', [
            'message' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString(),
        ]);
        
        $response = [
            'success' => false,
            'message' => $_ENV['APP_DEBUG'] === 'true' ? $e->getMessage() : 'Internal Server Error',
            'errors' => [],
        ];
        
        if ($_ENV['APP_DEBUG'] === 'true') {
            $response['debug'] = [
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTrace(),
            ];
        }
        
        $this->sendResponse($response);
    }
}
