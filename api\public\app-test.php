<?php

declare(strict_types=1);

// Application test
header('Content-Type: application/json; charset=utf-8');

// Composer autoloader
require_once dirname(__DIR__, 2) . '/vendor/autoload.php';

use EticSimple\Core\Application;

try {
    // Application'ı başlat
    $app = Application::getInstance();
    
    // Servisleri test et
    $services = [];
    
    try {
        $db = $app->get('db');
        $services['db'] = 'OK';
    } catch (\Exception $e) {
        $services['db'] = 'ERROR: ' . $e->getMessage();
    }
    
    try {
        $cache = $app->get('cache');
        $services['cache'] = 'OK';
    } catch (\Exception $e) {
        $services['cache'] = 'ERROR: ' . $e->getMessage();
    }
    
    try {
        $router = $app->get('router');
        $services['router'] = 'OK';
    } catch (\Exception $e) {
        $services['router'] = 'ERROR: ' . $e->getMessage();
    }
    
    try {
        $auth = $app->get('auth');
        $services['auth'] = 'OK';
    } catch (\Exception $e) {
        $services['auth'] = 'ERROR: ' . $e->getMessage();
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'Application test başarılı',
        'data' => [
            'services' => $services
        ]
    ], JSON_UNESCAPED_UNICODE);
    
} catch (\Throwable $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Application test hatası: ' . $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'trace' => explode("\n", $e->getTraceAsString())
    ], JSON_UNESCAPED_UNICODE);
}
