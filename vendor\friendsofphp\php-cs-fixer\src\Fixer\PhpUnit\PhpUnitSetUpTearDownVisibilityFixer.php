<?php

declare(strict_types=1);

/*
 * This file is part of PHP CS Fixer.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *     <PERSON><PERSON> <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled
 * with this source code in the file LICENSE.
 */

namespace PhpCs<PERSON>ixer\Fixer\PhpUnit;

use PhpCsF<PERSON>er\Fixer\AbstractPhpUnitFixer;
use PhpCsFixer\FixerDefinition\CodeSample;
use PhpCsFixer\FixerDefinition\FixerDefinition;
use PhpCsFixer\FixerDefinition\FixerDefinitionInterface;
use PhpCsFixer\Tokenizer\Token;
use Php<PERSON><PERSON>ixer\Tokenizer\Tokens;
use PhpCsFixer\Tokenizer\TokensAnalyzer;

/**
 * <AUTHOR> de Pagter
 */
final class PhpUnitSetUpTearDownVisibilityFixer extends AbstractPhpUnitFixer
{
    public function getDefinition(): FixerDefinitionInterface
    {
        return new FixerDefinition(
            'Changes the visibility of the `setUp()` and `tearDown()` functions of PHPUnit to `protected`, to match the PHPUnit TestCase.',
            [
                new CodeSample(
                    '<?php
final class MyTest extends \PHPUnit_Framework_TestCase
{
    private $hello;
    public function setUp()
    {
        $this->hello = "hello";
    }

    public function tearDown()
    {
        $this->hello = null;
    }
}
'
                ),
            ],
            null,
            'This fixer may change functions named `setUp()` or `tearDown()` outside of PHPUnit tests, '
            .'when a class is wrongly seen as a PHPUnit test.'
        );
    }

    public function isRisky(): bool
    {
        return true;
    }

    protected function applyPhpUnitClassFix(Tokens $tokens, int $startIndex, int $endIndex): void
    {
        $counter = 0;
        $tokensAnalyzer = new TokensAnalyzer($tokens);

        $slicesToInsert = [];

        for ($index = $startIndex + 1; $index < $endIndex; ++$index) {
            if (2 === $counter) {
                break; // we've seen both methods we are interested in, so stop analyzing this class
            }

            if ($tokens[$index]->equals('{')) {
                $index = $tokens->findBlockEnd(Tokens::BLOCK_TYPE_CURLY_BRACE, $index);

                continue;
            }

            if (!$tokens[$index]->isGivenKind(T_FUNCTION)) {
                continue;
            }

            $functionNameIndex = $tokens->getNextMeaningfulToken($index);
            $functionName = strtolower($tokens[$functionNameIndex]->getContent());

            if ('setup' !== $functionName && 'teardown' !== $functionName) {
                continue;
            }

            ++$counter;

            $visibility = $tokensAnalyzer->getMethodAttributes($index)['visibility'];

            if (T_PUBLIC === $visibility) {
                $visibilityIndex = $tokens->getPrevTokenOfKind($index, [[T_PUBLIC]]);
                $tokens[$visibilityIndex] = new Token([T_PROTECTED, 'protected']);

                continue;
            }

            if (null === $visibility) {
                $slicesToInsert[$index] = [new Token([T_PROTECTED, 'protected']), new Token([T_WHITESPACE, ' '])];
            }
        }

        $tokens->insertSlices($slicesToInsert);
    }
}
