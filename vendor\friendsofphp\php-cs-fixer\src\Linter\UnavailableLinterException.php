<?php

declare(strict_types=1);

/*
 * This file is part of PHP CS Fixer.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *     <PERSON><PERSON> <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled
 * with this source code in the file LICENSE.
 */

namespace PhpCsFixer\Linter;

/**
 * Exception that is thrown when the chosen linter is not available on the environment.
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @final
 *
 * @TODO 4.0 make class "final"
 */
class UnavailableLinterException extends \RuntimeException {}
