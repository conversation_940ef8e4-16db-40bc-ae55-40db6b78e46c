<?php

declare(strict_types=1);

namespace EticSimple\Modules\Product;

use EticSimple\Core\Database\DatabaseManager;
use EticSimple\Core\Application;

/**
 * Product Repository
 * 
 * Ürün veritabanı işlemleri
 */
class ProductRepository
{
    private DatabaseManager $db;
    private CategoryRepository $categoryRepository;
    
    public function __construct()
    {
        $this->db = Application::getInstance()->get('db');
        $this->categoryRepository = new CategoryRepository();
    }
    
    /**
     * ID ile ürün bul
     */
    public function findById(int $id, bool $withRelations = false): ?Product
    {
        $sql = "SELECT * FROM products WHERE id = ?";
        $productData = $this->db->fetchOne($sql, [$id]);
        
        if (!$productData) {
            return null;
        }
        
        $product = new Product($productData);
        
        if ($withRelations) {
            $this->loadRelations($product);
        }
        
        return $product;
    }
    
    /**
     * Slug ile ürün bul
     */
    public function findBySlug(string $slug, bool $withRelations = false): ?Product
    {
        $sql = "SELECT * FROM products WHERE slug = ? AND is_active = 1";
        $productData = $this->db->fetchOne($sql, [$slug]);
        
        if (!$productData) {
            return null;
        }
        
        $product = new Product($productData);
        
        if ($withRelations) {
            $this->loadRelations($product);
        }
        
        return $product;
    }
    
    /**
     * SKU ile ürün bul
     */
    public function findBySku(string $sku): ?Product
    {
        $sql = "SELECT * FROM products WHERE sku = ?";
        $productData = $this->db->fetchOne($sql, [$sku]);
        
        return $productData ? new Product($productData) : null;
    }
    
    /**
     * Ürün listesi (sayfalama ile)
     */
    public function findAll(int $page = 1, int $perPage = 20, array $filters = []): array
    {
        $offset = ($page - 1) * $perPage;
        
        $sql = "SELECT p.*, c.name as category_name 
                FROM products p 
                LEFT JOIN categories c ON p.category_id = c.id 
                WHERE 1=1";
        $params = [];
        
        // Filtreler
        if (!empty($filters['category_id'])) {
            $sql .= " AND p.category_id = ?";
            $params[] = $filters['category_id'];
        }
        
        if (!empty($filters['is_active'])) {
            $sql .= " AND p.is_active = ?";
            $params[] = $filters['is_active'];
        }
        
        if (!empty($filters['is_featured'])) {
            $sql .= " AND p.is_featured = ?";
            $params[] = $filters['is_featured'];
        }
        
        if (!empty($filters['brand'])) {
            $sql .= " AND p.brand = ?";
            $params[] = $filters['brand'];
        }
        
        if (!empty($filters['min_price'])) {
            $sql .= " AND p.price >= ?";
            $params[] = $filters['min_price'];
        }
        
        if (!empty($filters['max_price'])) {
            $sql .= " AND p.price <= ?";
            $params[] = $filters['max_price'];
        }
        
        if (!empty($filters['in_stock'])) {
            $sql .= " AND p.stock_quantity > 0";
        }
        
        if (!empty($filters['search'])) {
            $sql .= " AND (MATCH(p.name, p.short_description, p.description, p.sku, p.brand) AGAINST(? IN NATURAL LANGUAGE MODE))";
            $params[] = $filters['search'];
        }
        
        // Sıralama
        $orderBy = $filters['order_by'] ?? 'created_at';
        $orderDir = $filters['order_dir'] ?? 'DESC';
        
        $allowedOrderBy = ['name', 'price', 'created_at', 'rating_average', 'sale_count', 'view_count'];
        if (!in_array($orderBy, $allowedOrderBy)) {
            $orderBy = 'created_at';
        }
        
        $orderDir = strtoupper($orderDir) === 'ASC' ? 'ASC' : 'DESC';
        
        $sql .= " ORDER BY p.{$orderBy} {$orderDir}";
        $sql .= " LIMIT ? OFFSET ?";
        $params[] = $perPage;
        $params[] = $offset;
        
        $productsData = $this->db->fetchAll($sql, $params);
        
        $products = [];
        foreach ($productsData as $productData) {
            $product = new Product($productData);
            
            // Kategori bilgisini ekle
            if ($productData['category_name']) {
                $category = new Category([
                    'id' => $productData['category_id'],
                    'name' => $productData['category_name']
                ]);
                $product->setCategory($category);
            }
            
            $products[] = $product;
        }
        
        return $products;
    }
    
    /**
     * Öne çıkan ürünleri getir
     */
    public function findFeatured(int $limit = 10): array
    {
        $sql = "SELECT p.*, c.name as category_name 
                FROM products p 
                LEFT JOIN categories c ON p.category_id = c.id 
                WHERE p.is_featured = 1 AND p.is_active = 1 
                ORDER BY p.created_at DESC 
                LIMIT ?";
        
        $productsData = $this->db->fetchAll($sql, [$limit]);
        
        $products = [];
        foreach ($productsData as $productData) {
            $product = new Product($productData);
            
            if ($productData['category_name']) {
                $category = new Category([
                    'id' => $productData['category_id'],
                    'name' => $productData['category_name']
                ]);
                $product->setCategory($category);
            }
            
            $products[] = $product;
        }
        
        return $products;
    }
    
    /**
     * Kategoriye göre ürünleri getir
     */
    public function findByCategory(int $categoryId, int $page = 1, int $perPage = 20, array $filters = []): array
    {
        $filters['category_id'] = $categoryId;
        return $this->findAll($page, $perPage, $filters);
    }
    
    /**
     * Ürün arama
     */
    public function search(string $query, int $page = 1, int $perPage = 20, array $filters = []): array
    {
        $filters['search'] = $query;
        return $this->findAll($page, $perPage, $filters);
    }
    
    /**
     * Toplam ürün sayısı
     */
    public function count(array $filters = []): int
    {
        $sql = "SELECT COUNT(*) FROM products p WHERE 1=1";
        $params = [];
        
        // Filtreler (findAll ile aynı)
        if (!empty($filters['category_id'])) {
            $sql .= " AND p.category_id = ?";
            $params[] = $filters['category_id'];
        }
        
        if (!empty($filters['is_active'])) {
            $sql .= " AND p.is_active = ?";
            $params[] = $filters['is_active'];
        }
        
        if (!empty($filters['is_featured'])) {
            $sql .= " AND p.is_featured = ?";
            $params[] = $filters['is_featured'];
        }
        
        if (!empty($filters['brand'])) {
            $sql .= " AND p.brand = ?";
            $params[] = $filters['brand'];
        }
        
        if (!empty($filters['min_price'])) {
            $sql .= " AND p.price >= ?";
            $params[] = $filters['min_price'];
        }
        
        if (!empty($filters['max_price'])) {
            $sql .= " AND p.price <= ?";
            $params[] = $filters['max_price'];
        }
        
        if (!empty($filters['in_stock'])) {
            $sql .= " AND p.stock_quantity > 0";
        }
        
        if (!empty($filters['search'])) {
            $sql .= " AND (MATCH(p.name, p.short_description, p.description, p.sku, p.brand) AGAINST(? IN NATURAL LANGUAGE MODE))";
            $params[] = $filters['search'];
        }
        
        return (int) $this->db->fetchColumn($sql, $params);
    }
    
    /**
     * İlişkili verileri yükle
     */
    private function loadRelations(Product $product): void
    {
        // Kategori bilgisini yükle
        if ($product->getCategoryId()) {
            $category = $this->categoryRepository->findById($product->getCategoryId());
            $product->setCategory($category);
        }
        
        // Resimleri yükle
        $images = $this->getProductImages($product->getId());
        $product->setImages($images);
        
        // Özellikleri yükle
        $attributes = $this->getProductAttributes($product->getId());
        $product->setAttributes($attributes);
    }
    
    /**
     * Ürün resimlerini getir
     */
    public function getProductImages(int $productId): array
    {
        $sql = "SELECT * FROM product_images 
                WHERE product_id = ? 
                ORDER BY is_primary DESC, sort_order ASC";
        
        return $this->db->fetchAll($sql, [$productId]);
    }
    
    /**
     * Ürün özelliklerini getir
     */
    public function getProductAttributes(int $productId): array
    {
        $sql = "SELECT * FROM product_attributes 
                WHERE product_id = ? 
                ORDER BY sort_order ASC, attribute_name ASC";
        
        return $this->db->fetchAll($sql, [$productId]);
    }
    
    /**
     * Görüntülenme sayısını artır
     */
    public function incrementViewCount(int $productId): bool
    {
        try {
            $this->db->execute(
                "UPDATE products SET view_count = view_count + 1 WHERE id = ?",
                [$productId]
            );
            
            return true;
            
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * Stok güncelle
     */
    public function updateStock(int $productId, int $quantity): bool
    {
        try {
            $affectedRows = $this->db->update(
                'products',
                ['stock_quantity' => $quantity],
                ['id' => $productId]
            );
            
            return $affectedRows > 0;
            
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * Marka listesi
     */
    public function getBrands(): array
    {
        $sql = "SELECT DISTINCT brand FROM products 
                WHERE brand IS NOT NULL AND brand != '' AND is_active = 1 
                ORDER BY brand ASC";
        
        $brands = $this->db->fetchAll($sql);
        
        return array_column($brands, 'brand');
    }
    
    /**
     * Fiyat aralığı
     */
    public function getPriceRange(): array
    {
        $sql = "SELECT MIN(price) as min_price, MAX(price) as max_price 
                FROM products 
                WHERE is_active = 1";
        
        $result = $this->db->fetchOne($sql);
        
        return [
            'min' => (float) ($result['min_price'] ?? 0),
            'max' => (float) ($result['max_price'] ?? 0)
        ];
    }
}
