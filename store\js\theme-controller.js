/**
 * EticSimple Tema Controller
 * 
 * Dinamik tema değişimi ve yönetimi
 */
class ThemeController {
    constructor() {
        this.apiBase = '/api';
        this.currentTheme = this.getStoredTheme() || 'light';
        this.availableThemes = [];
        this.userId = this.getCurrentUserId();
        
        this.init();
    }
    
    /**
     * Controller'ı başlat
     */
    async init() {
        try {
            // Mevcut temaları yükle
            await this.loadAvailableThemes();
            
            // Aktif temayı uygula
            this.applyTheme(this.currentTheme);
            
            // Event listener'ları ekle
            this.setupEventListeners();
            
            // Tema seçici varsa güncelle
            this.updateThemeSelector();
            
            console.log('ThemeController initialized successfully');
            
        } catch (error) {
            console.error('ThemeController initialization failed:', error);
        }
    }
    
    /**
     * Mevcut temaları API'den yükle
     */
    async loadAvailableThemes() {
        try {
            const response = await fetch(`${this.apiBase}/themes`);
            const data = await response.json();
            
            if (data.success) {
                this.availableThemes = data.data;
            }
            
        } catch (error) {
            console.error('Failed to load themes:', error);
        }
    }
    
    /**
     * Temayı uygula
     */
    async applyTheme(themeName, saveToServer = true) {
        try {
            // DOM'a tema attribute'unu ekle
            document.documentElement.setAttribute('data-theme', themeName);
            
            // Local storage'a kaydet
            localStorage.setItem('selected-theme', themeName);
            
            // Mevcut temayı güncelle
            this.currentTheme = themeName;
            
            // Sunucuya kaydet (kullanıcı giriş yapmışsa)
            if (saveToServer && this.userId) {
                await this.saveThemeToServer(themeName);
            }
            
            // Tema değişikliği event'ini tetikle
            this.dispatchThemeChangeEvent(themeName);
            
            // Tema seçiciyi güncelle
            this.updateThemeSelector();
            
            console.log(`Theme applied: ${themeName}`);
            
        } catch (error) {
            console.error('Failed to apply theme:', error);
        }
    }
    
    /**
     * Temayı sunucuya kaydet
     */
    async saveThemeToServer(themeName) {
        try {
            const response = await fetch(`${this.apiBase}/themes/activate`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    theme_slug: themeName,
                    user_id: this.userId
                })
            });
            
            const data = await response.json();
            
            if (!data.success) {
                console.warn('Failed to save theme to server:', data.message);
            }
            
        } catch (error) {
            console.error('Failed to save theme to server:', error);
        }
    }
    
    /**
     * Tema önizlemesi
     */
    previewTheme(themeName) {
        // Geçici olarak temayı uygula (sunucuya kaydetme)
        document.documentElement.setAttribute('data-theme', themeName);
        
        // Önizleme event'ini tetikle
        this.dispatchEvent('theme:preview', { theme: themeName });
    }
    
    /**
     * Önizlemeyi iptal et
     */
    cancelPreview() {
        // Orijinal temayı geri yükle
        this.applyTheme(this.currentTheme, false);
        
        // Önizleme iptal event'ini tetikle
        this.dispatchEvent('theme:preview:cancel');
    }
    
    /**
     * Tema özelleştirmesi kaydet
     */
    async saveCustomization(key, value) {
        try {
            const themeId = this.getCurrentThemeId();
            
            const response = await fetch(`${this.apiBase}/themes/customize`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    theme_id: themeId,
                    key: key,
                    value: value,
                    user_id: this.userId
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                // CSS değişkenini güncelle
                document.documentElement.style.setProperty(`--${key}`, value);
                
                // Özelleştirme event'ini tetikle
                this.dispatchEvent('theme:customization:saved', { key, value });
                
                return true;
            } else {
                console.error('Failed to save customization:', data.message);
                return false;
            }
            
        } catch (error) {
            console.error('Failed to save customization:', error);
            return false;
        }
    }
    
    /**
     * Event listener'ları kur
     */
    setupEventListeners() {
        // Tema seçici butonları
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-theme-selector]')) {
                const themeName = e.target.getAttribute('data-theme-selector');
                this.applyTheme(themeName);
            }
            
            if (e.target.matches('[data-theme-preview]')) {
                const themeName = e.target.getAttribute('data-theme-preview');
                this.previewTheme(themeName);
            }
            
            if (e.target.matches('[data-theme-cancel-preview]')) {
                this.cancelPreview();
            }
        });
        
        // Sistem tema değişikliği
        if (window.matchMedia) {
            window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
                if (this.getStoredTheme() === null) {
                    // Kullanıcı tema seçmemişse sistem temasını kullan
                    this.applyTheme(e.matches ? 'dark' : 'light');
                }
            });
        }
    }
    
    /**
     * Tema seçiciyi güncelle
     */
    updateThemeSelector() {
        const selectors = document.querySelectorAll('[data-theme-selector]');
        
        selectors.forEach(selector => {
            const themeName = selector.getAttribute('data-theme-selector');
            
            if (themeName === this.currentTheme) {
                selector.classList.add('active');
                selector.setAttribute('aria-pressed', 'true');
            } else {
                selector.classList.remove('active');
                selector.setAttribute('aria-pressed', 'false');
            }
        });
    }
    
    /**
     * Tema değişikliği event'ini tetikle
     */
    dispatchThemeChangeEvent(themeName) {
        this.dispatchEvent('theme:changed', { 
            theme: themeName,
            previousTheme: this.currentTheme 
        });
    }
    
    /**
     * Custom event tetikle
     */
    dispatchEvent(eventName, detail = {}) {
        const event = new CustomEvent(eventName, { 
            detail,
            bubbles: true,
            cancelable: true 
        });
        
        document.dispatchEvent(event);
    }
    
    /**
     * Stored tema al
     */
    getStoredTheme() {
        return localStorage.getItem('selected-theme');
    }
    
    /**
     * Sistem tema tercihini al
     */
    getSystemTheme() {
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            return 'dark';
        }
        return 'light';
    }
    
    /**
     * Mevcut kullanıcı ID'sini al
     */
    getCurrentUserId() {
        // Bu fonksiyon auth sistemi ile entegre edilecek
        return window.user?.id || null;
    }
    
    /**
     * Mevcut tema ID'sini al
     */
    getCurrentThemeId() {
        const theme = this.availableThemes.find(t => t.slug === this.currentTheme);
        return theme?.id || 1;
    }
    
    /**
     * Mevcut temayı al
     */
    getCurrentTheme() {
        return this.currentTheme;
    }
    
    /**
     * Mevcut temaları al
     */
    getAvailableThemes() {
        return this.availableThemes;
    }
    
    /**
     * Tema var mı kontrol et
     */
    hasTheme(themeName) {
        return this.availableThemes.some(theme => theme.slug === themeName);
    }
    
    /**
     * Tema bilgilerini al
     */
    getThemeInfo(themeName) {
        return this.availableThemes.find(theme => theme.slug === themeName);
    }
}

// Global olarak kullanılabilir hale getir
window.ThemeController = ThemeController;

// DOM yüklendiğinde otomatik başlat
document.addEventListener('DOMContentLoaded', () => {
    window.themeController = new ThemeController();
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ThemeController;
}
