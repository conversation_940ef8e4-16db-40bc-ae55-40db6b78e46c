<?php

declare(strict_types=1);

/*
 * This file is part of PHP CS Fixer.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *     <PERSON><PERSON> <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled
 * with this source code in the file LICENSE.
 */

namespace PhpCs<PERSON>ixer\Linter;

use PhpCsFixer\Hasher;

/**
 * <AUTHOR> <<EMAIL>>
 *
 * @internal
 */
final class CachingLinter implements LinterInterface
{
    private LinterInterface $sublinter;

    /**
     * @var array<string, LintingResultInterface>
     */
    private array $cache = [];

    public function __construct(LinterInterface $linter)
    {
        $this->sublinter = $linter;
    }

    public function isAsync(): bool
    {
        return $this->sublinter->isAsync();
    }

    public function lintFile(string $path): LintingResultInterface
    {
        $checksum = Hasher::calculate(file_get_contents($path));

        return $this->cache[$checksum] ??= $this->sublinter->lintFile($path);
    }

    public function lintSource(string $source): LintingResultInterface
    {
        $checksum = Hasher::calculate($source);

        return $this->cache[$checksum] ??= $this->sublinter->lintSource($source);
    }
}
