<?php

declare(strict_types=1);

namespace EticSimple\Modules\Product;

use EticSimple\Core\Database\DatabaseManager;
use EticSimple\Core\Application;

/**
 * Category Repository
 * 
 * Kategori veritabanı işlemleri
 */
class CategoryRepository
{
    private DatabaseManager $db;
    
    public function __construct()
    {
        $this->db = Application::getInstance()->get('db');
    }
    
    /**
     * ID ile kategori bul
     */
    public function findById(int $id): ?Category
    {
        $sql = "SELECT * FROM categories WHERE id = ?";
        $categoryData = $this->db->fetchOne($sql, [$id]);
        
        return $categoryData ? new Category($categoryData) : null;
    }
    
    /**
     * Slug ile kategori bul
     */
    public function findBySlug(string $slug): ?Category
    {
        $sql = "SELECT * FROM categories WHERE slug = ? AND is_active = 1";
        $categoryData = $this->db->fetchOne($sql, [$slug]);
        
        return $categoryData ? new Category($categoryData) : null;
    }
    
    /**
     * Tüm kategorileri getir
     */
    public function findAll(bool $activeOnly = true): array
    {
        $sql = "SELECT * FROM categories";
        $params = [];
        
        if ($activeOnly) {
            $sql .= " WHERE is_active = 1";
        }
        
        $sql .= " ORDER BY sort_order ASC, name ASC";
        
        $categoriesData = $this->db->fetchAll($sql, $params);
        
        $categories = [];
        foreach ($categoriesData as $categoryData) {
            $categories[] = new Category($categoryData);
        }
        
        return $categories;
    }
    
    /**
     * Ana kategorileri getir
     */
    public function findRootCategories(bool $activeOnly = true): array
    {
        $sql = "SELECT * FROM categories WHERE parent_id IS NULL";
        $params = [];
        
        if ($activeOnly) {
            $sql .= " AND is_active = 1";
        }
        
        $sql .= " ORDER BY sort_order ASC, name ASC";
        
        $categoriesData = $this->db->fetchAll($sql, $params);
        
        $categories = [];
        foreach ($categoriesData as $categoryData) {
            $categories[] = new Category($categoryData);
        }
        
        return $categories;
    }
    
    /**
     * Alt kategorileri getir
     */
    public function findChildren(int $parentId, bool $activeOnly = true): array
    {
        $sql = "SELECT * FROM categories WHERE parent_id = ?";
        $params = [$parentId];
        
        if ($activeOnly) {
            $sql .= " AND is_active = 1";
        }
        
        $sql .= " ORDER BY sort_order ASC, name ASC";
        
        $categoriesData = $this->db->fetchAll($sql, $params);
        
        $categories = [];
        foreach ($categoriesData as $categoryData) {
            $categories[] = new Category($categoryData);
        }
        
        return $categories;
    }
    
    /**
     * Öne çıkan kategorileri getir
     */
    public function findFeatured(int $limit = 10): array
    {
        $sql = "SELECT * FROM categories 
                WHERE is_featured = 1 AND is_active = 1 
                ORDER BY sort_order ASC, name ASC 
                LIMIT ?";
        
        $categoriesData = $this->db->fetchAll($sql, [$limit]);
        
        $categories = [];
        foreach ($categoriesData as $categoryData) {
            $categories[] = new Category($categoryData);
        }
        
        return $categories;
    }
    
    /**
     * Hiyerarşik kategori ağacı oluştur
     */
    public function buildTree(bool $activeOnly = true): array
    {
        $allCategories = $this->findAll($activeOnly);
        $categoryMap = [];
        $rootCategories = [];
        
        // Kategorileri ID'ye göre map'le
        foreach ($allCategories as $category) {
            $categoryMap[$category->getId()] = $category;
        }
        
        // Hiyerarşiyi oluştur
        foreach ($allCategories as $category) {
            if ($category->getParentId() === null) {
                // Ana kategori
                $rootCategories[] = $category;
            } else {
                // Alt kategori
                $parentId = $category->getParentId();
                if (isset($categoryMap[$parentId])) {
                    $categoryMap[$parentId]->addChild($category);
                }
            }
        }
        
        return $rootCategories;
    }
    
    /**
     * Kategori oluştur
     */
    public function create(Category $category): ?Category
    {
        try {
            $data = [
                'parent_id' => $category->getParentId(),
                'name' => $category->getName(),
                'slug' => $category->getSlug(),
                'description' => $category->getDescription(),
                'image_url' => $category->getImageUrl(),
                'icon' => $category->getIcon(),
                'sort_order' => $category->getSortOrder(),
                'is_active' => $category->isActive() ? 1 : 0,
                'is_featured' => $category->isFeatured() ? 1 : 0,
                'meta_title' => $category->getMetaTitle(),
                'meta_description' => $category->getMetaDescription(),
                'meta_keywords' => $category->getMetaKeywords(),
            ];
            
            // Null değerleri filtrele
            $data = array_filter($data, fn($value) => $value !== null);
            
            $categoryId = $this->db->insert('categories', $data);
            
            return $this->findById($categoryId);
            
        } catch (\Exception $e) {
            Application::getInstance()->get('logger')->error('Category creation failed', [
                'name' => $category->getName(),
                'error' => $e->getMessage()
            ]);
            
            return null;
        }
    }
    
    /**
     * Kategori güncelle
     */
    public function update(Category $category): bool
    {
        if (!$category->getId()) {
            return false;
        }
        
        try {
            $data = [
                'parent_id' => $category->getParentId(),
                'name' => $category->getName(),
                'slug' => $category->getSlug(),
                'description' => $category->getDescription(),
                'image_url' => $category->getImageUrl(),
                'icon' => $category->getIcon(),
                'sort_order' => $category->getSortOrder(),
                'is_active' => $category->isActive() ? 1 : 0,
                'is_featured' => $category->isFeatured() ? 1 : 0,
                'meta_title' => $category->getMetaTitle(),
                'meta_description' => $category->getMetaDescription(),
                'meta_keywords' => $category->getMetaKeywords(),
            ];
            
            // Null değerleri filtrele
            $data = array_filter($data, fn($value) => $value !== null);
            
            $affectedRows = $this->db->update('categories', $data, ['id' => $category->getId()]);
            
            return $affectedRows > 0;
            
        } catch (\Exception $e) {
            Application::getInstance()->get('logger')->error('Category update failed', [
                'category_id' => $category->getId(),
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }
    
    /**
     * Kategori sil
     */
    public function delete(int $categoryId): bool
    {
        try {
            // Alt kategorileri kontrol et
            $children = $this->findChildren($categoryId, false);
            if (!empty($children)) {
                throw new \Exception('Bu kategorinin alt kategorileri var, önce onları silin');
            }
            
            // Ürün kontrolü
            $productCount = $this->getProductCount($categoryId);
            if ($productCount > 0) {
                throw new \Exception('Bu kategoride ürünler var, önce ürünleri başka kategoriye taşıyın');
            }
            
            $affectedRows = $this->db->delete('categories', ['id' => $categoryId]);
            
            return $affectedRows > 0;
            
        } catch (\Exception $e) {
            Application::getInstance()->get('logger')->error('Category deletion failed', [
                'category_id' => $categoryId,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }
    
    /**
     * Kategorideki ürün sayısını getir
     */
    public function getProductCount(int $categoryId): int
    {
        $sql = "SELECT COUNT(*) FROM products WHERE category_id = ? AND is_active = 1";
        return (int) $this->db->fetchColumn($sql, [$categoryId]);
    }
    
    /**
     * Slug benzersiz mi kontrol et
     */
    public function isSlugUnique(string $slug, ?int $excludeCategoryId = null): bool
    {
        $sql = "SELECT COUNT(*) FROM categories WHERE slug = ?";
        $params = [$slug];
        
        if ($excludeCategoryId) {
            $sql .= " AND id != ?";
            $params[] = $excludeCategoryId;
        }
        
        $count = $this->db->fetchColumn($sql, $params);
        
        return $count == 0;
    }
    
    /**
     * Kategori arama
     */
    public function search(string $query, int $limit = 20): array
    {
        $searchTerm = '%' . $query . '%';
        
        $sql = "SELECT * FROM categories 
                WHERE (name LIKE ? OR description LIKE ?) 
                AND is_active = 1 
                ORDER BY name ASC 
                LIMIT ?";
        
        $categoriesData = $this->db->fetchAll($sql, [$searchTerm, $searchTerm, $limit]);
        
        $categories = [];
        foreach ($categoriesData as $categoryData) {
            $categories[] = new Category($categoryData);
        }
        
        return $categories;
    }
    
    /**
     * Kategori breadcrumb'ını getir
     */
    public function getBreadcrumb(int $categoryId): array
    {
        $category = $this->findById($categoryId);
        
        if (!$category) {
            return [];
        }
        
        return $this->buildBreadcrumb($category);
    }
    
    /**
     * Breadcrumb oluştur (recursive)
     */
    private function buildBreadcrumb(Category $category): array
    {
        $breadcrumb = [];
        
        if ($category->getParentId()) {
            $parent = $this->findById($category->getParentId());
            if ($parent) {
                $breadcrumb = $this->buildBreadcrumb($parent);
            }
        }
        
        $breadcrumb[] = [
            'id' => $category->getId(),
            'name' => $category->getName(),
            'slug' => $category->getSlug(),
            'url' => $category->getUrl()
        ];
        
        return $breadcrumb;
    }
}
