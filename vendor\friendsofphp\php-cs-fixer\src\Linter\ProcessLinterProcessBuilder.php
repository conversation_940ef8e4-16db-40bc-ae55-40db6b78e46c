<?php

declare(strict_types=1);

/*
 * This file is part of PHP CS Fixer.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *     <PERSON><PERSON> <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled
 * with this source code in the file LICENSE.
 */

namespace PhpCsFixer\Linter;

use Symfony\Component\Process\Process;

/**
 * <AUTHOR> <<EMAIL>>
 *
 * @readonly
 *
 * @internal
 */
final class ProcessLinterProcessBuilder
{
    private string $executable;

    /**
     * @param string $executable PHP executable
     */
    public function __construct(string $executable)
    {
        $this->executable = $executable;
    }

    public function build(string $path): Process
    {
        return new Process([
            $this->executable,
            '-l',
            $path,
        ]);
    }
}
