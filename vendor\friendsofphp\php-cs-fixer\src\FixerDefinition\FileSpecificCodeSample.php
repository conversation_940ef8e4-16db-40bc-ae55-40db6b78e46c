<?php

declare(strict_types=1);

/*
 * This file is part of PHP CS Fixer.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *     <PERSON><PERSON> <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled
 * with this source code in the file LICENSE.
 */

namespace PhpCsFixer\FixerDefinition;

/**
 * <AUTHOR> <<EMAIL>>
 *
 * @readonly
 *
 * @internal
 */
final class FileSpecificCodeSample implements FileSpecificCodeSampleInterface
{
    private CodeSampleInterface $codeSample;

    private \SplFileInfo $splFileInfo;

    /**
     * @param null|array<string, mixed> $configuration
     */
    public function __construct(
        string $code,
        \SplFileInfo $splFileInfo,
        ?array $configuration = null
    ) {
        $this->codeSample = new CodeSample($code, $configuration);
        $this->splFileInfo = $splFileInfo;
    }

    public function getCode(): string
    {
        return $this->codeSample->getCode();
    }

    public function getConfiguration(): ?array
    {
        return $this->codeSample->getConfiguration();
    }

    public function getSplFileInfo(): \SplFileInfo
    {
        return $this->splFileInfo;
    }
}
