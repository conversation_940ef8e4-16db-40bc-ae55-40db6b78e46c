<?php

declare(strict_types=1);

namespace EticSimple\Core\Router;

/**
 * Route Sınıfı
 * 
 * Tekil route tanımı ve eşleştirme
 */
class Route
{
    private string $method;
    private string $path;
    private callable|array $handler;
    private array $middleware = [];
    private ?string $name = null;
    private array $parameters = [];
    
    public function __construct(string $method, string $path, callable|array $handler)
    {
        $this->method = strtoupper($method);
        $this->path = $this->normalizePath($path);
        $this->handler = $handler;
    }
    
    /**
     * Path'i normalize et
     */
    private function normalizePath(string $path): string
    {
        $path = trim($path, '/');
        return $path === '' ? '/' : '/' . $path;
    }
    
    /**
     * Route'un isteği karşılayıp karşılamadığını kontrol et
     */
    public function matches(string $method, string $uri): bool
    {
        // Method kontrolü
        if ($this->method !== strtoupper($method)) {
            return false;
        }
        
        // Path kontrolü
        $pattern = $this->getPattern();
        return preg_match($pattern, $uri) === 1;
    }
    
    /**
     * Route pattern'ini al (regex)
     */
    private function getPattern(): string
    {
        $pattern = $this->path;
        
        // {id} gibi parametreleri regex'e çevir
        $pattern = preg_replace('/\{([^}]+)\}/', '(?P<$1>[^/]+)', $pattern);
        
        // Başlangıç ve bitiş anchor'ları ekle
        return '#^' . $pattern . '$#';
    }
    
    /**
     * URI'den parametreleri çıkar
     */
    public function getParameters(string $uri): array
    {
        $pattern = $this->getPattern();
        
        if (preg_match($pattern, $uri, $matches)) {
            // Sadece named capture group'ları al
            $params = [];
            foreach ($matches as $key => $value) {
                if (is_string($key)) {
                    $params[$key] = $value;
                }
            }
            return $params;
        }
        
        return [];
    }
    
    /**
     * Middleware ekle
     */
    public function middleware(array|string $middleware): self
    {
        if (is_string($middleware)) {
            $this->middleware[] = $middleware;
        } else {
            $this->middleware = array_merge($this->middleware, $middleware);
        }
        
        return $this;
    }
    
    /**
     * Route adı ver
     */
    public function name(string $name): self
    {
        $this->name = $name;
        return $this;
    }
    
    /**
     * Route adını al
     */
    public function getName(): ?string
    {
        return $this->name;
    }
    
    /**
     * Method'u al
     */
    public function getMethod(): string
    {
        return $this->method;
    }
    
    /**
     * Path'i al
     */
    public function getPath(): string
    {
        return $this->path;
    }
    
    /**
     * Handler'ı al
     */
    public function getHandler(): callable|array
    {
        return $this->handler;
    }
    
    /**
     * Middleware'leri al
     */
    public function getMiddleware(): array
    {
        return $this->middleware;
    }
    
    /**
     * Route URL'ini oluştur
     */
    public function url(array $parameters = []): string
    {
        $url = $this->path;
        
        foreach ($parameters as $key => $value) {
            $url = str_replace('{' . $key . '}', (string) $value, $url);
        }
        
        return $url;
    }
    
    /**
     * Route bilgilerini array olarak al
     */
    public function toArray(): array
    {
        return [
            'method' => $this->method,
            'path' => $this->path,
            'name' => $this->name,
            'middleware' => $this->middleware,
            'handler' => is_array($this->handler) ? $this->handler : 'Closure'
        ];
    }
}
