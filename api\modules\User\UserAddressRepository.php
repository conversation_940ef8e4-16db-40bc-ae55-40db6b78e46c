<?php

declare(strict_types=1);

namespace EticSimple\Modules\User;

use EticSimple\Core\Database\DatabaseManager;
use EticSimple\Core\Application;

/**
 * UserAddress Repository
 * 
 * Kullanıcı adres veritabanı işlemleri
 */
class UserAddressRepository
{
    private DatabaseManager $db;
    
    public function __construct()
    {
        $this->db = Application::getInstance()->get('db');
    }
    
    /**
     * Adres oluştur
     */
    public function create(UserAddress $address): ?UserAddress
    {
        try {
            // Eğer bu adres varsayılan olarak işaretlenmişse, diğerlerini varsayılan olmaktan çıkar
            if ($address->isDefault()) {
                $this->clearDefaultAddresses($address->getUserId(), $address->getType());
            }
            
            $data = [
                'user_id' => $address->getUserId(),
                'title' => $address->getTitle(),
                'first_name' => $address->getFirstName(),
                'last_name' => $address->getLastName(),
                'company' => $address->getCompany(),
                'address_line_1' => $address->getAddressLine1(),
                'address_line_2' => $address->getAddressLine2(),
                'city' => $address->getCity(),
                'district' => $address->getDistrict(),
                'neighborhood' => $address->getNeighborhood(),
                'postal_code' => $address->getPostalCode(),
                'country' => $address->getCountry(),
                'phone' => $address->getPhone(),
                'is_default' => $address->isDefault() ? 1 : 0,
                'type' => $address->getType(),
            ];
            
            // Null değerleri filtrele
            $data = array_filter($data, fn($value) => $value !== null);
            
            $addressId = $this->db->insert('user_addresses', $data);
            
            return $this->findById($addressId);
            
        } catch (\Exception $e) {
            Application::getInstance()->get('logger')->error('Address creation failed', [
                'user_id' => $address->getUserId(),
                'error' => $e->getMessage()
            ]);
            
            return null;
        }
    }
    
    /**
     * ID ile adres bul
     */
    public function findById(int $id): ?UserAddress
    {
        $sql = "SELECT * FROM user_addresses WHERE id = ?";
        $addressData = $this->db->fetchOne($sql, [$id]);
        
        return $addressData ? new UserAddress($addressData) : null;
    }
    
    /**
     * Kullanıcının adreslerini getir
     */
    public function findByUserId(int $userId): array
    {
        $sql = "SELECT * FROM user_addresses WHERE user_id = ? ORDER BY is_default DESC, created_at DESC";
        $addressesData = $this->db->fetchAll($sql, [$userId]);
        
        $addresses = [];
        foreach ($addressesData as $addressData) {
            $addresses[] = new UserAddress($addressData);
        }
        
        return $addresses;
    }
    
    /**
     * Kullanıcının varsayılan adresini getir
     */
    public function findDefaultByUserId(int $userId, string $type = 'both'): ?UserAddress
    {
        $sql = "SELECT * FROM user_addresses WHERE user_id = ? AND is_default = 1";
        $params = [$userId];
        
        if ($type !== 'both') {
            $sql .= " AND (type = ? OR type = 'both')";
            $params[] = $type;
        }
        
        $sql .= " LIMIT 1";
        
        $addressData = $this->db->fetchOne($sql, $params);
        
        return $addressData ? new UserAddress($addressData) : null;
    }
    
    /**
     * Adres güncelle
     */
    public function update(UserAddress $address): bool
    {
        if (!$address->getId()) {
            return false;
        }
        
        try {
            // Eğer bu adres varsayılan olarak işaretlenmişse, diğerlerini varsayılan olmaktan çıkar
            if ($address->isDefault()) {
                $this->clearDefaultAddresses($address->getUserId(), $address->getType(), $address->getId());
            }
            
            $data = [
                'title' => $address->getTitle(),
                'first_name' => $address->getFirstName(),
                'last_name' => $address->getLastName(),
                'company' => $address->getCompany(),
                'address_line_1' => $address->getAddressLine1(),
                'address_line_2' => $address->getAddressLine2(),
                'city' => $address->getCity(),
                'district' => $address->getDistrict(),
                'neighborhood' => $address->getNeighborhood(),
                'postal_code' => $address->getPostalCode(),
                'country' => $address->getCountry(),
                'phone' => $address->getPhone(),
                'is_default' => $address->isDefault() ? 1 : 0,
                'type' => $address->getType(),
            ];
            
            // Null değerleri filtrele
            $data = array_filter($data, fn($value) => $value !== null);
            
            $affectedRows = $this->db->update('user_addresses', $data, ['id' => $address->getId()]);
            
            return $affectedRows > 0;
            
        } catch (\Exception $e) {
            Application::getInstance()->get('logger')->error('Address update failed', [
                'address_id' => $address->getId(),
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }
    
    /**
     * Adres sil
     */
    public function delete(int $addressId): bool
    {
        try {
            $affectedRows = $this->db->delete('user_addresses', ['id' => $addressId]);
            
            return $affectedRows > 0;
            
        } catch (\Exception $e) {
            Application::getInstance()->get('logger')->error('Address deletion failed', [
                'address_id' => $addressId,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }
    
    /**
     * Kullanıcının belirli bir adresini varsayılan yap
     */
    public function setAsDefault(int $addressId, int $userId): bool
    {
        try {
            // Önce adresi bul
            $address = $this->findById($addressId);
            if (!$address || $address->getUserId() !== $userId) {
                return false;
            }
            
            // Diğer varsayılan adresleri temizle
            $this->clearDefaultAddresses($userId, $address->getType(), $addressId);
            
            // Bu adresi varsayılan yap
            $affectedRows = $this->db->update(
                'user_addresses',
                ['is_default' => 1],
                ['id' => $addressId]
            );
            
            return $affectedRows > 0;
            
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * Kullanıcının adres sayısını getir
     */
    public function countByUserId(int $userId): int
    {
        $sql = "SELECT COUNT(*) FROM user_addresses WHERE user_id = ?";
        return (int) $this->db->fetchColumn($sql, [$userId]);
    }
    
    /**
     * Kullanıcının varsayılan adreslerini temizle
     */
    private function clearDefaultAddresses(int $userId, string $type, ?int $excludeAddressId = null): void
    {
        $sql = "UPDATE user_addresses SET is_default = 0 WHERE user_id = ?";
        $params = [$userId];
        
        if ($type !== 'both') {
            $sql .= " AND (type = ? OR type = 'both')";
            $params[] = $type;
        }
        
        if ($excludeAddressId) {
            $sql .= " AND id != ?";
            $params[] = $excludeAddressId;
        }
        
        $this->db->execute($sql, $params);
    }
    
    /**
     * Adres doğrulama
     */
    public function validateAddress(array $data): array
    {
        $errors = [];
        
        // Zorunlu alanlar
        $required = ['title', 'first_name', 'last_name', 'address_line_1', 'city', 'district', 'postal_code'];
        
        foreach ($required as $field) {
            if (empty($data[$field])) {
                $errors[$field] = ucfirst(str_replace('_', ' ', $field)) . ' alanı zorunludur';
            }
        }
        
        // Posta kodu formatı (Türkiye için 5 haneli)
        if (!empty($data['postal_code']) && !preg_match('/^\d{5}$/', $data['postal_code'])) {
            $errors['postal_code'] = 'Posta kodu 5 haneli olmalıdır';
        }
        
        // Telefon formatı
        if (!empty($data['phone']) && !preg_match('/^(\+90|0)?[5][0-9]{9}$/', preg_replace('/[^0-9+]/', '', $data['phone']))) {
            $errors['phone'] = 'Geçerli bir telefon numarası giriniz';
        }
        
        // Tip kontrolü
        if (!empty($data['type']) && !in_array($data['type'], ['billing', 'shipping', 'both'])) {
            $errors['type'] = 'Geçersiz adres tipi';
        }
        
        return $errors;
    }
    
    /**
     * Türkiye şehir listesi
     */
    public function getTurkishCities(): array
    {
        return [
            'Adana', 'Adıyaman', 'Afyonkarahisar', 'Ağrı', 'Amasya', 'Ankara', 'Antalya', 'Artvin',
            'Aydın', 'Balıkesir', 'Bilecik', 'Bingöl', 'Bitlis', 'Bolu', 'Burdur', 'Bursa',
            'Çanakkale', 'Çankırı', 'Çorum', 'Denizli', 'Diyarbakır', 'Edirne', 'Elazığ', 'Erzincan',
            'Erzurum', 'Eskişehir', 'Gaziantep', 'Giresun', 'Gümüşhane', 'Hakkâri', 'Hatay', 'Isparta',
            'Mersin', 'İstanbul', 'İzmir', 'Kars', 'Kastamonu', 'Kayseri', 'Kırklareli', 'Kırşehir',
            'Kocaeli', 'Konya', 'Kütahya', 'Malatya', 'Manisa', 'Kahramanmaraş', 'Mardin', 'Muğla',
            'Muş', 'Nevşehir', 'Niğde', 'Ordu', 'Rize', 'Sakarya', 'Samsun', 'Siirt',
            'Sinop', 'Sivas', 'Tekirdağ', 'Tokat', 'Trabzon', 'Tunceli', 'Şanlıurfa', 'Uşak',
            'Van', 'Yozgat', 'Zonguldak', 'Aksaray', 'Bayburt', 'Karaman', 'Kırıkkale', 'Batman',
            'Şırnak', 'Bartın', 'Ardahan', 'Iğdır', 'Yalova', 'Karabük', 'Kilis', 'Osmaniye', 'Düzce'
        ];
    }
}
