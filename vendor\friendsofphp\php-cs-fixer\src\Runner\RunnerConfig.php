<?php

declare(strict_types=1);

/*
 * This file is part of PHP CS Fixer.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *     <PERSON><PERSON> <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled
 * with this source code in the file LICENSE.
 */

namespace PhpCs<PERSON>ixer\Runner;

use PhpCsFixer\Runner\Parallel\ParallelConfig;

/**
 * <AUTHOR> <<EMAIL>>
 *
 * @readonly
 *
 * @internal
 */
final class RunnerConfig
{
    private bool $isDryRun;
    private bool $stopOnViolation;
    private ParallelConfig $parallelConfig;
    private ?string $configFile;

    public function __construct(
        bool $isDryRun,
        bool $stopOnViolation,
        ParallelConfig $parallelConfig,
        ?string $configFile = null
    ) {
        $this->isDryRun = $isDryRun;
        $this->stopOnViolation = $stopOnViolation;
        $this->parallelConfig = $parallelConfig;
        $this->configFile = $configFile;
    }

    public function isDryRun(): bool
    {
        return $this->isDryRun;
    }

    public function shouldStopOnViolation(): bool
    {
        return $this->stopOnViolation;
    }

    public function getParallelConfig(): ParallelConfig
    {
        return $this->parallelConfig;
    }

    public function getConfigFile(): ?string
    {
        return $this->configFile;
    }
}
