<?php

declare(strict_types=1);

/*
 * This file is part of PHP CS Fixer.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *     <PERSON><PERSON> <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled
 * with this source code in the file LICENSE.
 */

namespace PhpCsF<PERSON>er\Linter;

/**
 * Interface for PHP code linting process manager.
 *
 * <AUTHOR> <<EMAIL>>
 */
interface LinterInterface
{
    public function isAsync(): bool;

    /**
     * Lint PHP file.
     */
    public function lintFile(string $path): LintingResultInterface;

    /**
     * Lint PHP code.
     */
    public function lintSource(string $source): LintingResultInterface;
}
