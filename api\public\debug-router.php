<?php

declare(strict_types=1);

// Router debug
header('Content-Type: application/json; charset=utf-8');

// Composer autoloader
require_once dirname(__DIR__, 2) . '/vendor/autoload.php';

use EticSimple\Core\Application;

try {
    // Application'ı başlat
    $app = Application::getInstance();
    
    // Router'ı al
    $router = $app->get('router');
    
    // Route ekle
    $router->get('/test', function () {
        return ['message' => 'Test route çalışıyor'];
    });
    
    // Request bilgilerini al
    $method = $_SERVER['REQUEST_METHOD'];
    $uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
    $routes = $router->getRoutes();
    
    // Debug bilgileri
    $debug = [
        'method' => $method,
        'uri' => $uri,
        'routes_count' => count($routes),
        'routes' => []
    ];
    
    foreach ($routes as $route) {
        $debug['routes'][] = $route->toArray();
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'Router debug bilgileri',
        'data' => $debug
    ], JSON_UNESCAPED_UNICODE);
    
} catch (\Throwable $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Router debug hatası: ' . $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ], JSON_UNESCAPED_UNICODE);
}
