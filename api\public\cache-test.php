<?php

declare(strict_types=1);

// Cache test
header('Content-Type: application/json; charset=utf-8');

// Composer autoloader
require_once dirname(__DIR__, 2) . '/vendor/autoload.php';

use EticSimple\Core\Cache\CacheManager;

try {
    // Environment yükle
    $dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__, 2));
    $dotenv->load();
    
    // Cache Manager oluştur
    $cache = new CacheManager([
        'driver' => $_ENV['CACHE_DRIVER'] ?? 'file',
        'prefix' => $_ENV['CACHE_PREFIX'] ?? 'eticsimple_',
        'ttl' => (int)($_ENV['CACHE_TTL'] ?? 3600),
    ]);
    
    // Cache test
    $testKey = 'test_key';
    $testValue = ['message' => 'Cache test değeri', 'timestamp' => time()];
    
    // Set
    $setResult = $cache->set($testKey, $testValue);
    
    // Get
    $getValue = $cache->get($testKey);
    
    // Ping
    $pingResult = $cache->ping();
    
    echo json_encode([
        'success' => true,
        'message' => 'Cache test başarılı',
        'data' => [
            'set_result' => $setResult,
            'get_value' => $getValue,
            'ping_result' => $pingResult,
            'cache_driver' => $_ENV['CACHE_DRIVER']
        ]
    ], JSON_UNESCAPED_UNICODE);
    
} catch (\Throwable $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Cache test hatası: ' . $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ], JSON_UNESCAPED_UNICODE);
}
