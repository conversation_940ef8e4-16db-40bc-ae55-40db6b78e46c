<?php

declare(strict_types=1);

/*
 * This file is part of PHP CS Fixer.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *     <PERSON><PERSON> <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled
 * with this source code in the file LICENSE.
 */

namespace PhpCsFixer\FixerDefinition;

/**
 * <AUTHOR> <<EMAIL>>
 *
 * @readonly
 */
final class VersionSpecificCodeSample implements VersionSpecificCodeSampleInterface
{
    private CodeSampleInterface $codeSample;

    private VersionSpecificationInterface $versionSpecification;

    /**
     * @param null|array<string, mixed> $configuration
     */
    public function __construct(
        string $code,
        VersionSpecificationInterface $versionSpecification,
        ?array $configuration = null
    ) {
        $this->codeSample = new CodeSample($code, $configuration);
        $this->versionSpecification = $versionSpecification;
    }

    public function getCode(): string
    {
        return $this->codeSample->getCode();
    }

    public function getConfiguration(): ?array
    {
        return $this->codeSample->getConfiguration();
    }

    public function isSuitableFor(int $version): bool
    {
        return $this->versionSpecification->isSatisfiedBy($version);
    }
}
