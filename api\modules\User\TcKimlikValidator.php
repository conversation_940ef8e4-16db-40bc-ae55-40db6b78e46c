<?php

declare(strict_types=1);

namespace EticSimple\Modules\User;

/**
 * T.C. Kimlik No Validator
 * 
 * Türkiye Cumhuriyeti Kimlik Numarası doğrulama sistemi
 */
class TcKimlikValidator
{
    /**
     * T.C. Kimlik No doğrula
     */
    public static function validate(?string $tcKimlik): bool
    {
        // Null veya boş kontrol
        if (!$tcKimlik) {
            return false;
        }
        
        // Boşlukları temizle
        $tcKimlik = trim($tcKimlik);
        
        // Uzunluk kontrolü (11 karakter olmalı)
        if (strlen($tcKimlik) !== 11) {
            return false;
        }
        
        // Sadece rakam kontrolü
        if (!ctype_digit($tcKimlik)) {
            return false;
        }
        
        // İlk rakam 0 olamaz
        if ($tcKimlik[0] === '0') {
            return false;
        }
        
        // Tüm rakamlar aynı olamaz
        if (str_repeat($tcKimlik[0], 11) === $tcKimlik) {
            return false;
        }
        
        // T.C. Kimlik No algoritması
        return self::validateAlgorithm($tcKimlik);
    }
    
    /**
     * T.C. Kimlik No algoritma kontrolü
     */
    private static function validateAlgorithm(string $tcKimlik): bool
    {
        $digits = str_split($tcKimlik);
        $sum1 = 0; // Tek pozisyonlardaki rakamların toplamı
        $sum2 = 0; // Çift pozisyonlardaki rakamların toplamı
        
        // İlk 10 rakamın toplamı
        for ($i = 0; $i < 10; $i++) {
            if ($i % 2 === 0) {
                $sum1 += (int) $digits[$i];
            } else {
                $sum2 += (int) $digits[$i];
            }
        }
        
        // 10. rakam kontrolü
        $check1 = ($sum1 * 7 - $sum2) % 10;
        if ($check1 !== (int) $digits[9]) {
            return false;
        }
        
        // 11. rakam kontrolü
        $check2 = ($sum1 + $sum2 + (int) $digits[9]) % 10;
        if ($check2 !== (int) $digits[10]) {
            return false;
        }
        
        return true;
    }
    
    /**
     * T.C. Kimlik No formatla (11 haneli string olarak)
     */
    public static function format(?string $tcKimlik): ?string
    {
        if (!$tcKimlik) {
            return null;
        }
        
        // Sadece rakamları al
        $tcKimlik = preg_replace('/[^0-9]/', '', $tcKimlik);
        
        // 11 haneli değilse null döndür
        if (strlen($tcKimlik) !== 11) {
            return null;
        }
        
        return $tcKimlik;
    }
    
    /**
     * T.C. Kimlik No'yu maskele (güvenlik için)
     */
    public static function mask(string $tcKimlik): string
    {
        if (strlen($tcKimlik) !== 11) {
            return $tcKimlik;
        }
        
        // İlk 3 ve son 2 rakamı göster, ortasını maskele
        return substr($tcKimlik, 0, 3) . '******' . substr($tcKimlik, -2);
    }
    
    /**
     * Toplu T.C. Kimlik No doğrulama
     */
    public static function validateBatch(array $tcKimlikList): array
    {
        $results = [];
        
        foreach ($tcKimlikList as $index => $tcKimlik) {
            $results[$index] = [
                'tc_kimlik' => $tcKimlik,
                'is_valid' => self::validate($tcKimlik),
                'formatted' => self::format($tcKimlik)
            ];
        }
        
        return $results;
    }
    
    /**
     * T.C. Kimlik No doğrulama hata mesajları
     */
    public static function getValidationErrors(?string $tcKimlik): array
    {
        $errors = [];
        
        if (!$tcKimlik) {
            $errors[] = 'T.C. Kimlik No boş olamaz';
            return $errors;
        }
        
        $tcKimlik = trim($tcKimlik);
        
        if (strlen($tcKimlik) !== 11) {
            $errors[] = 'T.C. Kimlik No 11 haneli olmalıdır';
        }
        
        if (!ctype_digit($tcKimlik)) {
            $errors[] = 'T.C. Kimlik No sadece rakamlardan oluşmalıdır';
        }
        
        if (strlen($tcKimlik) === 11 && $tcKimlik[0] === '0') {
            $errors[] = 'T.C. Kimlik No 0 ile başlayamaz';
        }
        
        if (strlen($tcKimlik) === 11 && str_repeat($tcKimlik[0], 11) === $tcKimlik) {
            $errors[] = 'T.C. Kimlik No tüm rakamları aynı olamaz';
        }
        
        if (empty($errors) && !self::validateAlgorithm($tcKimlik)) {
            $errors[] = 'Geçersiz T.C. Kimlik No';
        }
        
        return $errors;
    }
    
    /**
     * Test T.C. Kimlik No'ları (geliştirme için)
     */
    public static function getTestTcKimlikNumbers(): array
    {
        return [
            '12345678901', // Geçersiz
            '11111111111', // Geçersiz (tüm rakamlar aynı)
            '01234567890', // Geçersiz (0 ile başlıyor)
            '12345678902', // Geçersiz algoritma
            '10000000146', // Geçerli test numarası
            '11111111110', // Geçerli test numarası
            '12345678958', // Geçerli test numarası
        ];
    }
    
    /**
     * Rastgele geçerli T.C. Kimlik No üret (test için)
     */
    public static function generateValidTcKimlik(): string
    {
        // İlk 9 rakamı rastgele üret (ilki 0 olamaz)
        $digits = [];
        $digits[0] = rand(1, 9);
        
        for ($i = 1; $i < 9; $i++) {
            $digits[$i] = rand(0, 9);
        }
        
        // 10. rakamı hesapla
        $sum1 = $digits[0] + $digits[2] + $digits[4] + $digits[6] + $digits[8];
        $sum2 = $digits[1] + $digits[3] + $digits[5] + $digits[7];
        $digits[9] = ($sum1 * 7 - $sum2) % 10;
        
        // 11. rakamı hesapla
        $digits[10] = ($sum1 + $sum2 + $digits[9]) % 10;
        
        return implode('', $digits);
    }
    
    /**
     * T.C. Kimlik No istatistikleri
     */
    public static function getStatistics(array $tcKimlikList): array
    {
        $total = count($tcKimlikList);
        $valid = 0;
        $invalid = 0;
        $empty = 0;
        
        foreach ($tcKimlikList as $tcKimlik) {
            if (!$tcKimlik) {
                $empty++;
            } elseif (self::validate($tcKimlik)) {
                $valid++;
            } else {
                $invalid++;
            }
        }
        
        return [
            'total' => $total,
            'valid' => $valid,
            'invalid' => $invalid,
            'empty' => $empty,
            'valid_percentage' => $total > 0 ? round(($valid / $total) * 100, 2) : 0,
            'invalid_percentage' => $total > 0 ? round(($invalid / $total) * 100, 2) : 0,
        ];
    }
}
