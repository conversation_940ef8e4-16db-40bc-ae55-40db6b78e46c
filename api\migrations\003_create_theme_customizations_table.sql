-- <PERSON><PERSON><PERSON><PERSON> tablosu
CREATE TABLE theme_customizations (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    theme_id BIGINT UNSIGNED NOT NULL,
    user_id BIGINT UNSIGNED NULL,
    customization_key VARCHAR(100) NOT NULL,
    customization_value JSON NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_theme_user_key (theme_id, user_id, customization_key),
    INDEX idx_theme_id (theme_id),
    INDEX idx_user_id (user_id),
    
    CONSTRAINT fk_theme_customizations_theme_id 
        FOREIGN KEY (theme_id) REFERENCES themes(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
