{"name": "eticsimple/ecommerce", "description": "Modern Turkish E-commerce System with Custom PHP Framework", "type": "project", "license": "MIT", "authors": [{"name": "EticSimple Team", "email": "<EMAIL>"}], "require": {"php": ">=8.3", "ext-pdo": "*", "ext-json": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-curl": "*", "firebase/php-jwt": "^6.10", "vlucas/phpdotenv": "^5.6", "monolog/monolog": "^3.5", "predis/predis": "^2.2", "swiftmailer/swiftmailer": "^6.3", "intervention/image": "^3.5", "league/flysystem": "^3.23"}, "require-dev": {"phpunit/phpunit": "^11.0", "phpstan/phpstan": "^1.10", "squizlabs/php_codesniffer": "^3.8", "friendsofphp/php-cs-fixer": "^3.48"}, "autoload": {"psr-4": {"EticSimple\\Core\\": "api/core/", "EticSimple\\Modules\\": "api/modules/", "EticSimple\\Middleware\\": "api/middleware/", "EticSimple\\Tests\\": "api/tests/"}, "files": ["api/core/helpers.php"]}, "autoload-dev": {"psr-4": {"EticSimple\\Tests\\": "api/tests/"}}, "scripts": {"test": "phpunit", "test-coverage": "phpunit --coverage-html coverage", "phpstan": "phpstan analyse", "cs-fix": "php-cs-fixer fix", "cs-check": "phpcs", "post-install-cmd": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""]}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}