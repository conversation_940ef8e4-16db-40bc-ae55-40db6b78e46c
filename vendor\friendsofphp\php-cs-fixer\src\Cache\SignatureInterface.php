<?php

declare(strict_types=1);

/*
 * This file is part of PHP CS Fixer.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *     <PERSON><PERSON> <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled
 * with this source code in the file LICENSE.
 */

namespace PhpCsFixer\Cache;

/**
 * <AUTHOR> <<EMAIL>>
 *
 * @internal
 */
interface SignatureInterface
{
    public function getPhpVersion(): string;

    public function getFixerVersion(): string;

    public function getIndent(): string;

    public function getLineEnding(): string;

    /**
     * @return array<string, array<string, mixed>|bool>
     */
    public function getRules(): array;

    public function equals(self $signature): bool;
}
