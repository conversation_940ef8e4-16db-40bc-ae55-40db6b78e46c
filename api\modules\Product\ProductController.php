<?php

declare(strict_types=1);

namespace EticSimple\Modules\Product;

use EticSimple\Core\Application;

/**
 * Product Controller
 * 
 * Ürün API endpoint'leri
 */
class ProductController
{
    private ProductRepository $productRepository;
    private CategoryRepository $categoryRepository;
    
    public function __construct()
    {
        $this->productRepository = new ProductRepository();
        $this->categoryRepository = new CategoryRepository();
    }
    
    /**
     * Ürün listesi
     */
    public function getProducts(): array
    {
        try {
            $page = (int) ($_GET['page'] ?? 1);
            $perPage = min((int) ($_GET['per_page'] ?? 20), 50);
            
            // Filtreler
            $filters = [];
            
            if (!empty($_GET['category_id'])) {
                $filters['category_id'] = (int) $_GET['category_id'];
            }
            
            if (!empty($_GET['brand'])) {
                $filters['brand'] = $_GET['brand'];
            }
            
            if (!empty($_GET['min_price'])) {
                $filters['min_price'] = (float) $_GET['min_price'];
            }
            
            if (!empty($_GET['max_price'])) {
                $filters['max_price'] = (float) $_GET['max_price'];
            }
            
            if (!empty($_GET['in_stock'])) {
                $filters['in_stock'] = filter_var($_GET['in_stock'], FILTER_VALIDATE_BOOLEAN);
            }
            
            if (!empty($_GET['featured'])) {
                $filters['is_featured'] = filter_var($_GET['featured'], FILTER_VALIDATE_BOOLEAN);
            }
            
            if (!empty($_GET['search'])) {
                $filters['search'] = trim($_GET['search']);
            }
            
            $filters['is_active'] = true;
            $filters['order_by'] = $_GET['order_by'] ?? 'created_at';
            $filters['order_dir'] = $_GET['order_dir'] ?? 'DESC';
            
            // Ürünleri getir
            $products = $this->productRepository->findAll($page, $perPage, $filters);
            $totalCount = $this->productRepository->count($filters);
            
            // Özet array'e çevir
            $productsArray = [];
            foreach ($products as $product) {
                $productsArray[] = $product->toSummaryArray();
            }
            
            return response([
                'products' => $productsArray,
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $perPage,
                    'total' => $totalCount,
                    'total_pages' => ceil($totalCount / $perPage),
                    'has_next' => $page < ceil($totalCount / $perPage),
                    'has_prev' => $page > 1
                ],
                'filters' => $filters
            ], 'Ürünler listelendi');
            
        } catch (\Exception $e) {
            Application::getInstance()->get('logger')->error('Products listing failed', [
                'error' => $e->getMessage()
            ]);
            
            return error_response('Ürünler listelenemedi', [], 500);
        }
    }
    
    /**
     * Ürün detayı
     */
    public function getProduct(): array
    {
        try {
            $productId = (int) ($_GET['id'] ?? 0);
            $slug = $_GET['slug'] ?? '';
            
            if ($productId) {
                $product = $this->productRepository->findById($productId, true);
            } elseif ($slug) {
                $product = $this->productRepository->findBySlug($slug, true);
            } else {
                return error_response('Ürün ID veya slug gerekli', [], 400);
            }
            
            if (!$product) {
                return error_response('Ürün bulunamadı', [], 404);
            }
            
            // Görüntülenme sayısını artır
            $this->productRepository->incrementViewCount($product->getId());
            
            return response($product->toDetailedArray(), 'Ürün detayları');
            
        } catch (\Exception $e) {
            return error_response('Ürün detayları alınamadı', [], 500);
        }
    }
    
    /**
     * Öne çıkan ürünler
     */
    public function getFeaturedProducts(): array
    {
        try {
            $limit = min((int) ($_GET['limit'] ?? 10), 20);
            
            $products = $this->productRepository->findFeatured($limit);
            
            $productsArray = [];
            foreach ($products as $product) {
                $productsArray[] = $product->toSummaryArray();
            }
            
            return response($productsArray, 'Öne çıkan ürünler');
            
        } catch (\Exception $e) {
            return error_response('Öne çıkan ürünler alınamadı', [], 500);
        }
    }
    
    /**
     * Ürün arama
     */
    public function searchProducts(): array
    {
        try {
            $query = trim($_GET['q'] ?? '');
            
            if (empty($query)) {
                return error_response('Arama terimi gerekli', [], 400);
            }
            
            if (strlen($query) < 2) {
                return error_response('Arama terimi en az 2 karakter olmalıdır', [], 400);
            }
            
            $page = (int) ($_GET['page'] ?? 1);
            $perPage = min((int) ($_GET['per_page'] ?? 20), 50);
            
            $filters = [
                'search' => $query,
                'is_active' => true
            ];
            
            // Ek filtreler
            if (!empty($_GET['category_id'])) {
                $filters['category_id'] = (int) $_GET['category_id'];
            }
            
            if (!empty($_GET['brand'])) {
                $filters['brand'] = $_GET['brand'];
            }
            
            $products = $this->productRepository->search($query, $page, $perPage, $filters);
            $totalCount = $this->productRepository->count($filters);
            
            $productsArray = [];
            foreach ($products as $product) {
                $productsArray[] = $product->toSummaryArray();
            }
            
            return response([
                'query' => $query,
                'products' => $productsArray,
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $perPage,
                    'total' => $totalCount,
                    'total_pages' => ceil($totalCount / $perPage)
                ]
            ], 'Arama sonuçları');
            
        } catch (\Exception $e) {
            return error_response('Arama başarısız', [], 500);
        }
    }
    
    /**
     * Kategoriye göre ürünler
     */
    public function getProductsByCategory(): array
    {
        try {
            $categoryId = (int) ($_GET['category_id'] ?? 0);
            $categorySlug = $_GET['category_slug'] ?? '';
            
            if ($categoryId) {
                $category = $this->categoryRepository->findById($categoryId);
            } elseif ($categorySlug) {
                $category = $this->categoryRepository->findBySlug($categorySlug);
            } else {
                return error_response('Kategori ID veya slug gerekli', [], 400);
            }
            
            if (!$category) {
                return error_response('Kategori bulunamadı', [], 404);
            }
            
            $page = (int) ($_GET['page'] ?? 1);
            $perPage = min((int) ($_GET['per_page'] ?? 20), 50);
            
            $filters = [
                'category_id' => $category->getId(),
                'is_active' => true,
                'order_by' => $_GET['order_by'] ?? 'created_at',
                'order_dir' => $_GET['order_dir'] ?? 'DESC'
            ];
            
            $products = $this->productRepository->findByCategory($category->getId(), $page, $perPage, $filters);
            $totalCount = $this->productRepository->count($filters);
            
            $productsArray = [];
            foreach ($products as $product) {
                $productsArray[] = $product->toSummaryArray();
            }
            
            return response([
                'category' => $category->toArray(),
                'products' => $productsArray,
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $perPage,
                    'total' => $totalCount,
                    'total_pages' => ceil($totalCount / $perPage)
                ]
            ], $category->getName() . ' kategorisi ürünleri');
            
        } catch (\Exception $e) {
            return error_response('Kategori ürünleri alınamadı', [], 500);
        }
    }
    
    /**
     * Marka listesi
     */
    public function getBrands(): array
    {
        try {
            $brands = $this->productRepository->getBrands();
            
            return response($brands, 'Marka listesi');
            
        } catch (\Exception $e) {
            return error_response('Marka listesi alınamadı', [], 500);
        }
    }
    
    /**
     * Fiyat aralığı
     */
    public function getPriceRange(): array
    {
        try {
            $priceRange = $this->productRepository->getPriceRange();
            
            return response($priceRange, 'Fiyat aralığı');
            
        } catch (\Exception $e) {
            return error_response('Fiyat aralığı alınamadı', [], 500);
        }
    }
    
    /**
     * Filtre seçenekleri
     */
    public function getFilterOptions(): array
    {
        try {
            $brands = $this->productRepository->getBrands();
            $priceRange = $this->productRepository->getPriceRange();
            $categories = $this->categoryRepository->findAll(true);
            
            $categoriesArray = [];
            foreach ($categories as $category) {
                $categoriesArray[] = [
                    'id' => $category->getId(),
                    'name' => $category->getName(),
                    'slug' => $category->getSlug(),
                    'parent_id' => $category->getParentId()
                ];
            }
            
            return response([
                'brands' => $brands,
                'price_range' => $priceRange,
                'categories' => $categoriesArray
            ], 'Filtre seçenekleri');
            
        } catch (\Exception $e) {
            return error_response('Filtre seçenekleri alınamadı', [], 500);
        }
    }
}
