<?php

declare(strict_types=1);

// Router test
header('Content-Type: application/json; charset=utf-8');

// Composer autoloader
require_once dirname(__DIR__, 2) . '/vendor/autoload.php';

use EticSimple\Core\Application;
use EticSimple\Core\Router\Router;

try {
    // Application'ı başlat
    $app = Application::getInstance();
    
    // Router'ı al
    $router = $app->get('router');
    
    // Basit route ekle
    $router->get('/test', function () {
        return [
            'success' => true,
            'message' => 'Router test başarılı',
            'timestamp' => date('c')
        ];
    });
    
    // Request URI'yi kontrol et
    $uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
    $method = $_SERVER['REQUEST_METHOD'];
    
    echo json_encode([
        'success' => true,
        'message' => 'Router hazır',
        'data' => [
            'uri' => $uri,
            'method' => $method,
            'routes_count' => count($router->getRoutes())
        ]
    ], JSON_UNESCAPED_UNICODE);
    
} catch (\Throwable $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Router test hatası: ' . $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'trace' => explode("\n", $e->getTraceAsString())
    ], JSON_UNESCAPED_UNICODE);
}
