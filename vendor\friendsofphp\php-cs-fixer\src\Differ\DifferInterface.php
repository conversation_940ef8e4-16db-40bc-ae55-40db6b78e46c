<?php

declare(strict_types=1);

/*
 * This file is part of PHP CS Fixer.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *     <PERSON><PERSON> <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled
 * with this source code in the file LICENSE.
 */

namespace PhpCsFixer\Differ;

/**
 * <AUTHOR> <<EMAIL>>
 */
interface DifferInterface
{
    /**
     * Create diff.
     */
    public function diff(string $old, string $new, ?\SplFileInfo $file = null): string;
}
