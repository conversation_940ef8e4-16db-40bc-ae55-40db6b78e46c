<?php

declare(strict_types=1);

/*
 * This file is part of PHP CS Fixer.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *     <PERSON><PERSON> <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled
 * with this source code in the file LICENSE.
 */

namespace PhpCsFixer\FixerDefinition;

/**
 * <AUTHOR> <<EMAIL>>
 *
 * @readonly
 */
final class CodeSample implements CodeSampleInterface
{
    private string $code;

    /**
     * @var null|array<string, mixed>
     */
    private ?array $configuration;

    /**
     * @param null|array<string, mixed> $configuration
     */
    public function __construct(string $code, ?array $configuration = null)
    {
        $this->code = $code;
        $this->configuration = $configuration;
    }

    public function getCode(): string
    {
        return $this->code;
    }

    public function getConfiguration(): ?array
    {
        return $this->configuration;
    }
}
