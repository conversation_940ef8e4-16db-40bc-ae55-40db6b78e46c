<?php

declare(strict_types=1);

namespace EticSimple\Core\Router;

class Route
{
    private string $method;
    private string $path;
    private callable|array $handler;
    private array $middleware = [];
    private ?string $name = null;
    
    public function __construct(string $method, string $path, callable|array $handler)
    {
        $this->method = strtoupper($method);
        $this->path = $this->normalizePath($path);
        $this->handler = $handler;
    }
    
    private function normalizePath(string $path): string
    {
        $path = trim($path, '/');
        return $path === '' ? '/' : '/' . $path;
    }
    
    public function matches(string $method, string $uri): bool
    {
        if ($this->method !== strtoupper($method)) {
            return false;
        }
        
        $pattern = $this->getPattern();
        return preg_match($pattern, $uri) === 1;
    }
    
    private function getPattern(): string
    {
        $pattern = $this->path;
        $pattern = preg_replace('/\{([^}]+)\}/', '(?P<$1>[^/]+)', $pattern);
        return '#^' . $pattern . '$#';
    }
    
    public function getParameters(string $uri): array
    {
        $pattern = $this->getPattern();
        
        if (preg_match($pattern, $uri, $matches)) {
            $params = [];
            foreach ($matches as $key => $value) {
                if (is_string($key)) {
                    $params[$key] = $value;
                }
            }
            return $params;
        }
        
        return [];
    }
    
    public function middleware(array|string $middleware): self
    {
        if (is_string($middleware)) {
            $this->middleware[] = $middleware;
        } else {
            $this->middleware = array_merge($this->middleware, $middleware);
        }
        
        return $this;
    }
    
    public function name(string $name): self
    {
        $this->name = $name;
        return $this;
    }
    
    public function getName(): ?string
    {
        return $this->name;
    }
    
    public function getMethod(): string
    {
        return $this->method;
    }
    
    public function getPath(): string
    {
        return $this->path;
    }
    
    public function getHandler(): callable|array
    {
        return $this->handler;
    }
    
    public function getMiddleware(): array
    {
        return $this->middleware;
    }
    
    public function url(array $parameters = []): string
    {
        $url = $this->path;
        
        foreach ($parameters as $key => $value) {
            $url = str_replace('{' . $key . '}', (string) $value, $url);
        }
        
        return $url;
    }
    
    public function toArray(): array
    {
        return [
            'method' => $this->method,
            'path' => $this->path,
            'name' => $this->name,
            'middleware' => $this->middleware,
            'handler' => is_array($this->handler) ? $this->handler : 'Closure'
        ];
    }
}
