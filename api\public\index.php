<?php

declare(strict_types=1);

// Composer autoloader
require_once dirname(__DIR__, 2) . '/vendor/autoload.php';

use EticSimple\Core\Application;

// Application'ı başlat
$app = Application::getInstance();

// Router'ı al
$router = $app->get('router');

// API Routes
$router->group(['prefix' => 'api'], function ($router) {
    
    // Health Check
    $router->get('/health', function () {
        return response([
            'status' => 'OK',
            'timestamp' => date('c'),
            'version' => '1.0.0'
        ], 'Sistem çalışıyor');
    });
    
    // Theme Routes
    $router->group(['prefix' => 'themes'], function ($router) {
        
        // Mevcut temaları listele
        $router->get('/', function () {
            $themes = theme()->getAvailableThemes();
            return response($themes, 'Temalar listelendi');
        });
        
        // Aktif temayı al
        $router->get('/current', function () {
            $currentTheme = theme()->getCurrentTheme();
            return response($currentTheme, 'Aktif tema bilgileri');
        });
        
        // Tema CSS değişkenlerini al
        $router->get('/variables', function () {
            $userId = $_GET['user_id'] ?? null;
            $variables = theme()->getThemeVariables($userId ? (int) $userId : null);
            return response($variables, 'Tema değişkenleri');
        });
        
        // Tema CSS'ini al
        $router->get('/css', function () {
            $userId = $_GET['user_id'] ?? null;
            $css = theme()->generateThemeCSS($userId ? (int) $userId : null);
            
            header('Content-Type: text/css; charset=utf-8');
            echo $css;
            exit;
        });
        
        // Tema aktifleştir
        $router->post('/activate', function () {
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!isset($input['theme_slug'])) {
                return error_response('Tema slug\'ı gerekli', [], 400);
            }
            
            $userId = $input['user_id'] ?? null;
            $success = theme()->activateTheme($input['theme_slug'], $userId ? (int) $userId : null);
            
            if ($success) {
                return response(null, 'Tema başarıyla aktifleştirildi');
            } else {
                return error_response('Tema aktifleştirilemedi', [], 500);
            }
        });
        
        // Tema özelleştirmesi kaydet
        $router->post('/customize', function () {
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!isset($input['theme_id'], $input['key'], $input['value'])) {
                return error_response('theme_id, key ve value alanları gerekli', [], 400);
            }
            
            $userId = $input['user_id'] ?? null;
            $success = theme()->saveCustomization(
                (int) $input['theme_id'],
                $input['key'],
                $input['value'],
                $userId ? (int) $userId : null
            );
            
            if ($success) {
                return response(null, 'Tema özelleştirmesi kaydedildi');
            } else {
                return error_response('Tema özelleştirmesi kaydedilemedi', [], 500);
            }
        });
    });
    
    // Settings Routes
    $router->group(['prefix' => 'settings'], function ($router) {
        
        // Public ayarları al
        $router->get('/', function () {
            $settings = db()->fetchAll("SELECT setting_key, setting_value FROM site_settings WHERE is_public = 1");
            
            $formattedSettings = [];
            foreach ($settings as $setting) {
                $formattedSettings[$setting['setting_key']] = json_decode($setting['setting_value'], true);
            }
            
            return response($formattedSettings, 'Site ayarları');
        });
    });
    
    // Cache Routes (Development only)
    if (config('APP_ENV') === 'development') {
        $router->group(['prefix' => 'cache'], function ($router) {
            
            // Cache istatistikleri
            $router->get('/stats', function () {
                $stats = cache()->stats();
                return response($stats, 'Cache istatistikleri');
            });
            
            // Cache temizle
            $router->delete('/flush', function () {
                $success = cache()->flush();
                
                if ($success) {
                    return response(null, 'Cache temizlendi');
                } else {
                    return error_response('Cache temizlenemedi', [], 500);
                }
            });
            
            // Cache ping
            $router->get('/ping', function () {
                $pong = cache()->ping();
                
                return response([
                    'connected' => $pong
                ], $pong ? 'Cache bağlantısı aktif' : 'Cache bağlantısı yok');
            });
        });
    }
    
    // Database Routes (Development only)
    if (config('APP_ENV') === 'development') {
        $router->group(['prefix' => 'db'], function ($router) {
            
            // Query log
            $router->get('/queries', function () {
                $queries = db()->getQueryLog();
                return response($queries, 'Sorgu logları');
            });
            
            // Test connection
            $router->get('/ping', function () {
                try {
                    db()->fetchOne("SELECT 1 as test");
                    return response(['connected' => true], 'Veritabanı bağlantısı aktif');
                } catch (\Exception $e) {
                    return error_response('Veritabanı bağlantısı yok: ' . $e->getMessage(), [], 500);
                }
            });
        });
    }
    
    // Test Routes (Development only)
    if (config('APP_ENV') === 'development') {
        $router->group(['prefix' => 'test'], function ($router) {
            
            // Tema kurulumu test
            $router->post('/install-themes', function () {
                try {
                    theme()->installDefaultThemes();
                    return response(null, 'Varsayılan temalar kuruldu');
                } catch (\Exception $e) {
                    return error_response('Tema kurulumu başarısız: ' . $e->getMessage(), [], 500);
                }
            });
            
            // Helper fonksiyonları test
            $router->get('/helpers', function () {
                return response([
                    'tc_kimlik_valid' => validate_tc_kimlik('12345678901'),
                    'currency_format' => format_currency(1234.56),
                    'date_format' => format_date(new DateTime()),
                    'phone_format' => format_phone('05551234567'),
                    'slug_generate' => generate_slug('Türkçe Başlık Örneği'),
                    'token_generate' => generate_token(16)
                ], 'Helper fonksiyonları test edildi');
            });
        });
    }
});

// 404 handler
$router->get('/{path:.*}', function () {
    return error_response('Endpoint bulunamadı', [], 404);
});

// İsteği işle
$app->handleRequest();
