<!DOCTYPE html>
<html lang="tr" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EticSimple E-Ticaret - Tema Si<PERSON>mi Test</title>
    
    <!-- CSS Variables -->
    <link rel="stylesheet" href="css/variables.css">
    
    <!-- Test Styles -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: var(--font-primary);
            background: var(--theme-bg-primary);
            color: var(--theme-text-primary);
            line-height: var(--leading-normal);
            transition: all var(--transition-normal);
        }
        
        .container {
            max-width: var(--container-lg);
            margin: 0 auto;
            padding: var(--space-6);
        }
        
        .header {
            background: var(--theme-bg-secondary);
            border-bottom: 1px solid var(--theme-border-primary);
            padding: var(--space-4) 0;
            margin-bottom: var(--space-8);
        }
        
        .header h1 {
            font-family: var(--font-heading);
            font-size: var(--text-3xl);
            font-weight: var(--font-bold);
            color: var(--theme-accent-primary);
        }
        
        .theme-selector {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--space-4);
            margin-bottom: var(--space-8);
        }
        
        .theme-option {
            background: var(--theme-bg-secondary);
            border: 2px solid var(--theme-border-primary);
            border-radius: var(--radius-lg);
            padding: var(--space-4);
            cursor: pointer;
            transition: all var(--transition-normal);
            text-align: center;
        }
        
        .theme-option:hover {
            border-color: var(--theme-accent-primary);
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }
        
        .theme-option.active {
            border-color: var(--theme-accent-primary);
            background: var(--theme-accent-primary);
            color: var(--white);
        }
        
        .theme-preview-colors {
            display: flex;
            gap: var(--space-2);
            justify-content: center;
            margin-bottom: var(--space-3);
        }
        
        .color-swatch {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            border: 1px solid var(--theme-border-secondary);
        }
        
        .card {
            background: var(--theme-bg-secondary);
            border: 1px solid var(--theme-border-primary);
            border-radius: var(--radius-lg);
            padding: var(--space-6);
            margin-bottom: var(--space-6);
            box-shadow: var(--shadow-sm);
            transition: all var(--transition-normal);
        }
        
        .card:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-1px);
        }
        
        .card h3 {
            font-size: var(--text-xl);
            font-weight: var(--font-semibold);
            margin-bottom: var(--space-3);
            color: var(--theme-text-primary);
        }
        
        .card p {
            color: var(--theme-text-secondary);
            margin-bottom: var(--space-4);
        }
        
        .btn {
            display: inline-block;
            padding: var(--space-3) var(--space-6);
            border-radius: var(--radius-md);
            font-weight: var(--font-medium);
            text-decoration: none;
            border: none;
            cursor: pointer;
            transition: all var(--transition-normal);
            font-size: var(--text-base);
        }
        
        .btn-primary {
            background: var(--theme-accent-primary);
            color: var(--white);
        }
        
        .btn-primary:hover {
            background: var(--primary-600);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }
        
        .btn-secondary {
            background: transparent;
            color: var(--theme-accent-primary);
            border: 2px solid var(--theme-accent-primary);
        }
        
        .btn-secondary:hover {
            background: var(--theme-accent-primary);
            color: var(--white);
        }
        
        .status-indicator {
            display: inline-block;
            padding: var(--space-1) var(--space-3);
            border-radius: var(--radius-full);
            font-size: var(--text-sm);
            font-weight: var(--font-medium);
        }
        
        .status-success {
            background: var(--success-50);
            color: var(--success-600);
        }
        
        .status-warning {
            background: var(--warning-50);
            color: var(--warning-600);
        }
        
        .status-error {
            background: var(--error-50);
            color: var(--error-600);
        }
        
        .api-status {
            background: var(--theme-bg-tertiary);
            border-radius: var(--radius-md);
            padding: var(--space-4);
            margin-bottom: var(--space-6);
        }
        
        .api-status h4 {
            margin-bottom: var(--space-2);
            color: var(--theme-text-primary);
        }
        
        .api-endpoint {
            font-family: var(--font-mono);
            font-size: var(--text-sm);
            background: var(--theme-bg-primary);
            padding: var(--space-2);
            border-radius: var(--radius-sm);
            margin: var(--space-2) 0;
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <h1>🛒 EticSimple E-Ticaret</h1>
            <p>Tema Sistemi Test Sayfası</p>
        </div>
    </header>
    
    <main class="container">
        <!-- Tema Seçici -->
        <section>
            <h2>Tema Seçici</h2>
            <div class="theme-selector">
                <div class="theme-option" data-theme-selector="light">
                    <div class="theme-preview-colors">
                        <div class="color-swatch" style="background: #ffffff;"></div>
                        <div class="color-swatch" style="background: #0ea5e9;"></div>
                        <div class="color-swatch" style="background: #d946ef;"></div>
                    </div>
                    <h4>Light Theme</h4>
                    <p>Açık renkli varsayılan tema</p>
                </div>
                
                <div class="theme-option" data-theme-selector="dark">
                    <div class="theme-preview-colors">
                        <div class="color-swatch" style="background: #111827;"></div>
                        <div class="color-swatch" style="background: #0ea5e9;"></div>
                        <div class="color-swatch" style="background: #d946ef;"></div>
                    </div>
                    <h4>Dark Theme</h4>
                    <p>Koyu renkli tema</p>
                </div>
                
                <div class="theme-option" data-theme-selector="blue">
                    <div class="theme-preview-colors">
                        <div class="color-swatch" style="background: #f8fafc;"></div>
                        <div class="color-swatch" style="background: #3b82f6;"></div>
                        <div class="color-swatch" style="background: #1e40af;"></div>
                    </div>
                    <h4>Blue Theme</h4>
                    <p>Mavi tonlarda kurumsal tema</p>
                </div>
                
                <div class="theme-option" data-theme-selector="green">
                    <div class="theme-preview-colors">
                        <div class="color-swatch" style="background: #f7fdf7;"></div>
                        <div class="color-swatch" style="background: #22c55e;"></div>
                        <div class="color-swatch" style="background: #16a34a;"></div>
                    </div>
                    <h4>Green Theme</h4>
                    <p>Yeşil tonlarda doğa teması</p>
                </div>
            </div>
        </section>
        
        <!-- API Durumu -->
        <section class="api-status">
            <h4>API Durumu</h4>
            <div id="api-status-content">
                <p>API bağlantısı kontrol ediliyor...</p>
            </div>
        </section>
        
        <!-- Örnek İçerik -->
        <section>
            <h2>Örnek E-Ticaret İçeriği</h2>
            
            <div class="card">
                <h3>🛍️ Ürün Katalogu</h3>
                <p>Bu bölümde ürünlerinizi listeleyebilir, kategorilere ayırabilir ve müşterilerinizin kolayca bulmasını sağlayabilirsiniz.</p>
                <button class="btn btn-primary">Ürünleri Görüntüle</button>
                <button class="btn btn-secondary">Kategori Ekle</button>
            </div>
            
            <div class="card">
                <h3>🛒 Sepet Yönetimi</h3>
                <p>Müşterileriniz ürünleri sepete ekleyebilir, miktarları değiştirebilir ve güvenli ödeme işlemlerini gerçekleştirebilir.</p>
                <span class="status-indicator status-success">Aktif</span>
                <button class="btn btn-primary">Sepeti Görüntüle</button>
            </div>
            
            <div class="card">
                <h3>💳 Ödeme Sistemi</h3>
                <p>PayTR, iyzico entegrasyonları ile güvenli ödeme altyapısı. Havale/EFT ve kapıda ödeme seçenekleri.</p>
                <span class="status-indicator status-warning">Geliştiriliyor</span>
                <button class="btn btn-secondary">Ayarları Görüntüle</button>
            </div>
            
            <div class="card">
                <h3>📦 Kargo Yönetimi</h3>
                <p>Manuel kargo firma ekleme, takip numarası sistemi ve otomatik kargo bildirimleri.</p>
                <span class="status-indicator status-error">Planlanıyor</span>
                <button class="btn btn-secondary">Kargo Firmaları</button>
            </div>
        </section>
        
        <!-- Test Butonları -->
        <section>
            <h2>Test İşlemleri</h2>
            <div class="card">
                <h3>🧪 API Test Fonksiyonları</h3>
                <p>Geliştirme ortamında API endpoint'lerini test edebilirsiniz.</p>
                <button class="btn btn-primary" onclick="testAPI()">API Bağlantısını Test Et</button>
                <button class="btn btn-secondary" onclick="installThemes()">Varsayılan Temaları Kur</button>
                <button class="btn btn-secondary" onclick="testHelpers()">Helper Fonksiyonlarını Test Et</button>
            </div>
        </section>
    </main>
    
    <!-- JavaScript -->
    <script src="js/theme-controller.js"></script>
    <script>
        // API Test Fonksiyonları
        async function testAPI() {
            try {
                const response = await fetch('/api/health');
                const data = await response.json();
                
                document.getElementById('api-status-content').innerHTML = `
                    <div class="status-indicator status-success">Bağlantı Başarılı</div>
                    <div class="api-endpoint">GET /api/health</div>
                    <p>Durum: ${data.status}</p>
                    <p>Zaman: ${data.data.timestamp}</p>
                `;
            } catch (error) {
                document.getElementById('api-status-content').innerHTML = `
                    <div class="status-indicator status-error">Bağlantı Hatası</div>
                    <p>Hata: ${error.message}</p>
                `;
            }
        }
        
        async function installThemes() {
            try {
                const response = await fetch('/api/test/install-themes', { method: 'POST' });
                const data = await response.json();
                
                if (data.success) {
                    alert('Varsayılan temalar başarıyla kuruldu!');
                    // Tema listesini yenile
                    if (window.themeController) {
                        await window.themeController.loadAvailableThemes();
                    }
                } else {
                    alert('Tema kurulumu başarısız: ' + data.message);
                }
            } catch (error) {
                alert('Hata: ' + error.message);
            }
        }
        
        async function testHelpers() {
            try {
                const response = await fetch('/api/test/helpers');
                const data = await response.json();
                
                if (data.success) {
                    console.log('Helper Test Results:', data.data);
                    alert('Helper fonksiyonları test edildi. Sonuçlar console\'da görüntüleniyor.');
                } else {
                    alert('Helper test başarısız: ' + data.message);
                }
            } catch (error) {
                alert('Hata: ' + error.message);
            }
        }
        
        // Sayfa yüklendiğinde API durumunu kontrol et
        document.addEventListener('DOMContentLoaded', () => {
            testAPI();
            
            // Tema değişikliği event listener
            document.addEventListener('theme:changed', (e) => {
                console.log('Theme changed to:', e.detail.theme);
            });
        });
    </script>
</body>
</html>
