<?php

declare(strict_types=1);

namespace EticSimple\Core\Cache;

use Predis\Client as RedisClient;

/**
 * Cache Yönetim Sistemi
 * 
 * Redis tabanlı cache yönetimi
 */
class CacheManager
{
    private array $config;
    private ?RedisClient $redis = null;
    private string $cacheDir;

    public function __construct(array $config)
    {
        $this->config = $config;
        $this->cacheDir = dirname(__DIR__, 3) . '/storage/cache';

        // Cache dizinini oluştur
        if (!is_dir($this->cacheDir)) {
            mkdir($this->cacheDir, 0755, true);
        }
    }
    
    /**
     * Redis bağlantısı al
     */
    private function getRedis(): RedisClient
    {
        if ($this->redis === null) {
            $this->redis = new RedisClient([
                'scheme' => 'tcp',
                'host' => $this->config['redis']['host'],
                'port' => $this->config['redis']['port'],
                'password' => $this->config['redis']['password'],
                'database' => $this->config['redis']['database'],
            ]);
        }
        
        return $this->redis;
    }
    
    /**
     * Cache key oluştur
     */
    private function makeKey(string $key): string
    {
        return $this->config['prefix'] . $key;
    }
    
    /**
     * Cache'den değer al
     */
    public function get(string $key): mixed
    {
        try {
            if ($this->config['driver'] === 'file') {
                return $this->getFromFile($key);
            }

            $value = $this->getRedis()->get($this->makeKey($key));

            if ($value === null) {
                return null;
            }

            $decoded = json_decode($value, true);

            // JSON decode hatası varsa raw değeri döndür
            if (json_last_error() !== JSON_ERROR_NONE) {
                return $value;
            }

            return $decoded;

        } catch (\Exception $e) {
            // Cache hatası durumunda null döndür
            return null;
        }
    }
    
    /**
     * Cache'e değer kaydet
     */
    public function set(string $key, mixed $value, ?int $ttl = null): bool
    {
        try {
            if ($this->config['driver'] === 'file') {
                return $this->setToFile($key, $value, $ttl);
            }

            $ttl = $ttl ?? $this->config['ttl'];
            $encodedValue = json_encode($value, JSON_UNESCAPED_UNICODE);

            if ($ttl > 0) {
                return $this->getRedis()->setex($this->makeKey($key), $ttl, $encodedValue) === 'OK';
            } else {
                return $this->getRedis()->set($this->makeKey($key), $encodedValue) === 'OK';
            }

        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * Cache'den değer sil
     */
    public function delete(string $key): bool
    {
        try {
            return $this->getRedis()->del([$this->makeKey($key)]) > 0;
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * Pattern'e göre cache'leri sil
     */
    public function deletePattern(string $pattern): int
    {
        try {
            $keys = $this->getRedis()->keys($this->makeKey($pattern));
            
            if (empty($keys)) {
                return 0;
            }
            
            return $this->getRedis()->del($keys);
            
        } catch (\Exception $e) {
            return 0;
        }
    }
    
    /**
     * Cache'de key var mı kontrol et
     */
    public function exists(string $key): bool
    {
        try {
            return $this->getRedis()->exists($this->makeKey($key)) > 0;
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * Cache TTL'ini al
     */
    public function ttl(string $key): int
    {
        try {
            return $this->getRedis()->ttl($this->makeKey($key));
        } catch (\Exception $e) {
            return -1;
        }
    }
    
    /**
     * Cache TTL'ini güncelle
     */
    public function expire(string $key, int $ttl): bool
    {
        try {
            return $this->getRedis()->expire($this->makeKey($key), $ttl) > 0;
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * Increment işlemi
     */
    public function increment(string $key, int $value = 1): int
    {
        try {
            return $this->getRedis()->incrby($this->makeKey($key), $value);
        } catch (\Exception $e) {
            return 0;
        }
    }
    
    /**
     * Decrement işlemi
     */
    public function decrement(string $key, int $value = 1): int
    {
        try {
            return $this->getRedis()->decrby($this->makeKey($key), $value);
        } catch (\Exception $e) {
            return 0;
        }
    }
    
    /**
     * Remember pattern - cache varsa al, yoksa callback'i çalıştır ve cache'le
     */
    public function remember(string $key, callable $callback, ?int $ttl = null): mixed
    {
        $value = $this->get($key);
        
        if ($value !== null) {
            return $value;
        }
        
        $value = $callback();
        $this->set($key, $value, $ttl);
        
        return $value;
    }
    
    /**
     * Forever - süresiz cache
     */
    public function forever(string $key, mixed $value): bool
    {
        return $this->set($key, $value, 0);
    }
    
    /**
     * Flush - tüm cache'i temizle
     */
    public function flush(): bool
    {
        try {
            return $this->getRedis()->flushdb() === 'OK';
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * Cache istatistikleri
     */
    public function stats(): array
    {
        try {
            $info = $this->getRedis()->info();
            
            return [
                'connected_clients' => $info['connected_clients'] ?? 0,
                'used_memory' => $info['used_memory'] ?? 0,
                'used_memory_human' => $info['used_memory_human'] ?? '0B',
                'keyspace_hits' => $info['keyspace_hits'] ?? 0,
                'keyspace_misses' => $info['keyspace_misses'] ?? 0,
                'total_commands_processed' => $info['total_commands_processed'] ?? 0,
            ];
            
        } catch (\Exception $e) {
            return [];
        }
    }
    
    /**
     * Bağlantıyı test et
     */
    public function ping(): bool
    {
        try {
            if ($this->config['driver'] === 'file') {
                return is_dir($this->cacheDir) && is_writable($this->cacheDir);
            }

            return $this->getRedis()->ping() === 'PONG';
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * File cache'den değer al
     */
    private function getFromFile(string $key): mixed
    {
        $filename = $this->getCacheFilename($key);

        if (!file_exists($filename)) {
            return null;
        }

        $content = file_get_contents($filename);
        if ($content === false) {
            return null;
        }

        $data = json_decode($content, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return null;
        }

        // TTL kontrolü
        if (isset($data['expires_at']) && $data['expires_at'] > 0 && $data['expires_at'] < time()) {
            unlink($filename);
            return null;
        }

        return $data['value'] ?? null;
    }

    /**
     * File cache'e değer kaydet
     */
    private function setToFile(string $key, mixed $value, ?int $ttl = null): bool
    {
        $filename = $this->getCacheFilename($key);
        $ttl = $ttl ?? $this->config['ttl'];

        $data = [
            'value' => $value,
            'created_at' => time(),
            'expires_at' => $ttl > 0 ? time() + $ttl : 0
        ];

        $content = json_encode($data, JSON_UNESCAPED_UNICODE);

        return file_put_contents($filename, $content, LOCK_EX) !== false;
    }

    /**
     * Cache dosya adını al
     */
    private function getCacheFilename(string $key): string
    {
        $hashedKey = md5($this->makeKey($key));
        return $this->cacheDir . '/' . $hashedKey . '.cache';
    }
}
