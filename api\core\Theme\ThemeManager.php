<?php

declare(strict_types=1);

namespace EticSimple\Core\Theme;

use EticSimple\Core\Application;
use EticSimple\Core\Database\DatabaseManager;
use EticSimple\Core\Cache\CacheManager;

/**
 * Tema Yönetim Sistemi
 * 
 * <PERSON><PERSON><PERSON> te<PERSON>ği, tema <PERSON>, özelleştirme ve cache yönetimi
 */
class ThemeManager
{
    private array $config;
    private ?array $currentTheme = null;
    private ?array $availableThemes = null;
    private DatabaseManager $db;
    private CacheManager $cache;
    
    public function __construct(array $config)
    {
        $this->config = $config;
        $this->db = Application::getInstance()->get('db');
        $this->cache = Application::getInstance()->get('cache');
    }
    
    /**
     * Aktif temayı al
     */
    public function getCurrentTheme(): array
    {
        if ($this->currentTheme === null) {
            $this->loadCurrentTheme();
        }
        
        return $this->currentTheme;
    }
    
    /**
     * Mevcut temaları al
     */
    public function getAvailableThemes(): array
    {
        if ($this->availableThemes === null) {
            $this->loadAvailableThemes();
        }
        
        return $this->availableThemes;
    }
    
    /**
     * Tema aktifleştir
     */
    public function activateTheme(string $themeSlug, ?int $userId = null): bool
    {
        try {
            $theme = $this->getThemeBySlug($themeSlug);
            if (!$theme) {
                throw new \InvalidArgumentException("Theme '{$themeSlug}' not found");
            }
            
            // Kullanıcı bazlı tema seçimi
            if ($userId) {
                $this->setUserTheme($userId, $theme['id']);
            } else {
                // Site geneli tema değişimi
                $this->setSiteTheme($theme['id']);
            }
            
            // Cache temizle
            $this->clearThemeCache($userId);
            
            return true;
            
        } catch (\Exception $e) {
            Application::getInstance()->get('logger')->error('Theme activation failed', [
                'theme' => $themeSlug,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }
    
    /**
     * Tema özelleştirmesi kaydet
     */
    public function saveCustomization(int $themeId, string $key, mixed $value, ?int $userId = null): bool
    {
        try {
            $sql = "INSERT INTO theme_customizations (theme_id, user_id, customization_key, customization_value, created_at, updated_at) 
                    VALUES (?, ?, ?, ?, NOW(), NOW()) 
                    ON DUPLICATE KEY UPDATE 
                    customization_value = VALUES(customization_value), 
                    updated_at = NOW()";
            
            $this->db->execute($sql, [
                $themeId,
                $userId,
                $key,
                json_encode($value, JSON_UNESCAPED_UNICODE)
            ]);
            
            // Cache temizle
            $this->clearThemeCache($userId);
            
            return true;
            
        } catch (\Exception $e) {
            Application::getInstance()->get('logger')->error('Theme customization save failed', [
                'theme_id' => $themeId,
                'key' => $key,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }
    
    /**
     * Tema CSS değişkenlerini al
     */
    public function getThemeVariables(?int $userId = null): array
    {
        $cacheKey = "theme_variables_" . ($userId ?? 'global');
        
        if ($this->config['cache_enabled']) {
            $cached = $this->cache->get($cacheKey);
            if ($cached !== null) {
                return $cached;
            }
        }
        
        $theme = $this->getCurrentTheme();
        $variables = json_decode($theme['css_variables'], true) ?? [];
        
        // Kullanıcı özelleştirmelerini al
        if ($userId) {
            $customizations = $this->getUserCustomizations($theme['id'], $userId);
            $variables = array_merge($variables, $customizations);
        }
        
        // Site geneli özelleştirmeler
        $siteCustomizations = $this->getSiteCustomizations($theme['id']);
        $variables = array_merge($variables, $siteCustomizations);
        
        if ($this->config['cache_enabled']) {
            $this->cache->set($cacheKey, $variables, $this->config['cache_ttl']);
        }
        
        return $variables;
    }
    
    /**
     * Tema CSS'ini oluştur
     */
    public function generateThemeCSS(?int $userId = null): string
    {
        $variables = $this->getThemeVariables($userId);
        
        $css = ":root {\n";
        foreach ($variables as $key => $value) {
            $css .= "  --{$key}: {$value};\n";
        }
        $css .= "}\n";
        
        return $css;
    }
    
    /**
     * Yeni tema yükle
     */
    public function installTheme(array $themeData): bool
    {
        try {
            $sql = "INSERT INTO themes (name, slug, description, version, author, preview_image, css_variables, settings, created_at, updated_at) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())";
            
            $this->db->execute($sql, [
                $themeData['name'],
                $themeData['slug'],
                $themeData['description'] ?? '',
                $themeData['version'],
                $themeData['author'] ?? '',
                $themeData['preview_image'] ?? '',
                json_encode($themeData['css_variables'] ?? [], JSON_UNESCAPED_UNICODE),
                json_encode($themeData['settings'] ?? [], JSON_UNESCAPED_UNICODE)
            ]);
            
            // Cache temizle
            $this->clearAllThemeCache();
            
            return true;
            
        } catch (\Exception $e) {
            Application::getInstance()->get('logger')->error('Theme installation failed', [
                'theme' => $themeData,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }
    
    /**
     * Varsayılan temaları yükle
     */
    public function installDefaultThemes(): void
    {
        $defaultThemes = [
            [
                'name' => 'Light Theme',
                'slug' => 'light',
                'description' => 'Açık renkli varsayılan tema',
                'version' => '1.0.0',
                'author' => 'EticSimple Team',
                'css_variables' => [
                    'theme-bg-primary' => '#ffffff',
                    'theme-bg-secondary' => '#f9fafb',
                    'theme-bg-tertiary' => '#f3f4f6',
                    'theme-text-primary' => '#111827',
                    'theme-text-secondary' => '#6b7280',
                    'theme-text-muted' => '#9ca3af',
                    'theme-border-primary' => '#e5e7eb',
                    'theme-border-secondary' => '#d1d5db',
                    'theme-accent-primary' => '#0ea5e9',
                    'theme-accent-secondary' => '#d946ef',
                ],
                'is_default' => true
            ],
            [
                'name' => 'Dark Theme',
                'slug' => 'dark',
                'description' => 'Koyu renkli tema',
                'version' => '1.0.0',
                'author' => 'EticSimple Team',
                'css_variables' => [
                    'theme-bg-primary' => '#111827',
                    'theme-bg-secondary' => '#1f2937',
                    'theme-bg-tertiary' => '#374151',
                    'theme-text-primary' => '#f9fafb',
                    'theme-text-secondary' => '#d1d5db',
                    'theme-text-muted' => '#9ca3af',
                    'theme-border-primary' => '#374151',
                    'theme-border-secondary' => '#4b5563',
                    'theme-accent-primary' => '#0ea5e9',
                    'theme-accent-secondary' => '#d946ef',
                ]
            ],
            [
                'name' => 'Blue Theme',
                'slug' => 'blue',
                'description' => 'Mavi tonlarda kurumsal tema',
                'version' => '1.0.0',
                'author' => 'EticSimple Team',
                'css_variables' => [
                    'theme-bg-primary' => '#f8fafc',
                    'theme-bg-secondary' => '#f1f5f9',
                    'theme-bg-tertiary' => '#e2e8f0',
                    'theme-text-primary' => '#0f172a',
                    'theme-text-secondary' => '#475569',
                    'theme-text-muted' => '#64748b',
                    'theme-border-primary' => '#cbd5e1',
                    'theme-border-secondary' => '#94a3b8',
                    'theme-accent-primary' => '#3b82f6',
                    'theme-accent-secondary' => '#1e40af',
                ]
            ],
            [
                'name' => 'Green Theme',
                'slug' => 'green',
                'description' => 'Yeşil tonlarda doğa teması',
                'version' => '1.0.0',
                'author' => 'EticSimple Team',
                'css_variables' => [
                    'theme-bg-primary' => '#f7fdf7',
                    'theme-bg-secondary' => '#f0fdf4',
                    'theme-bg-tertiary' => '#dcfce7',
                    'theme-text-primary' => '#14532d',
                    'theme-text-secondary' => '#166534',
                    'theme-text-muted' => '#22c55e',
                    'theme-border-primary' => '#bbf7d0',
                    'theme-border-secondary' => '#86efac',
                    'theme-accent-primary' => '#22c55e',
                    'theme-accent-secondary' => '#16a34a',
                ]
            ]
        ];
        
        foreach ($defaultThemes as $theme) {
            // Tema zaten varsa atla
            if ($this->getThemeBySlug($theme['slug'])) {
                continue;
            }
            
            $this->installTheme($theme);
            
            // Varsayılan temayı aktifleştir
            if ($theme['is_default'] ?? false) {
                $this->activateTheme($theme['slug']);
            }
        }
    }
    
    // Private helper methods
    
    private function loadCurrentTheme(): void
    {
        $cacheKey = 'current_theme';
        
        if ($this->config['cache_enabled']) {
            $cached = $this->cache->get($cacheKey);
            if ($cached !== null) {
                $this->currentTheme = $cached;
                return;
            }
        }
        
        // Site ayarlarından aktif temayı al
        $activeThemeId = $this->getSiteSetting('active_theme_id');
        
        if ($activeThemeId) {
            $sql = "SELECT * FROM themes WHERE id = ? AND is_active = 1";
            $theme = $this->db->fetchOne($sql, [$activeThemeId]);
        }
        
        // Aktif tema bulunamazsa varsayılan temayı al
        if (!isset($theme) || !$theme) {
            $sql = "SELECT * FROM themes WHERE is_default = 1 LIMIT 1";
            $theme = $this->db->fetchOne($sql);
        }
        
        // Hala tema bulunamazsa ilk temayı al
        if (!$theme) {
            $sql = "SELECT * FROM themes ORDER BY id ASC LIMIT 1";
            $theme = $this->db->fetchOne($sql);
        }
        
        $this->currentTheme = $theme ?: [];
        
        if ($this->config['cache_enabled'] && $this->currentTheme) {
            $this->cache->set($cacheKey, $this->currentTheme, $this->config['cache_ttl']);
        }
    }
    
    private function loadAvailableThemes(): void
    {
        $cacheKey = 'available_themes';
        
        if ($this->config['cache_enabled']) {
            $cached = $this->cache->get($cacheKey);
            if ($cached !== null) {
                $this->availableThemes = $cached;
                return;
            }
        }
        
        $sql = "SELECT * FROM themes ORDER BY name ASC";
        $this->availableThemes = $this->db->fetchAll($sql);
        
        if ($this->config['cache_enabled']) {
            $this->cache->set($cacheKey, $this->availableThemes, $this->config['cache_ttl']);
        }
    }
    
    private function getThemeBySlug(string $slug): ?array
    {
        $sql = "SELECT * FROM themes WHERE slug = ?";
        return $this->db->fetchOne($sql, [$slug]) ?: null;
    }
    
    private function setUserTheme(int $userId, int $themeId): void
    {
        $this->saveCustomization($themeId, 'user_theme_id', $themeId, $userId);
    }
    
    private function setSiteTheme(int $themeId): void
    {
        $this->setSiteSetting('active_theme_id', $themeId);
        
        // Tüm temaları pasif yap
        $this->db->execute("UPDATE themes SET is_active = 0");
        
        // Seçilen temayı aktif yap
        $this->db->execute("UPDATE themes SET is_active = 1 WHERE id = ?", [$themeId]);
    }
    
    private function getUserCustomizations(int $themeId, int $userId): array
    {
        $sql = "SELECT customization_key, customization_value FROM theme_customizations 
                WHERE theme_id = ? AND user_id = ?";
        
        $rows = $this->db->fetchAll($sql, [$themeId, $userId]);
        
        $customizations = [];
        foreach ($rows as $row) {
            $customizations[$row['customization_key']] = json_decode($row['customization_value'], true);
        }
        
        return $customizations;
    }
    
    private function getSiteCustomizations(int $themeId): array
    {
        $sql = "SELECT customization_key, customization_value FROM theme_customizations 
                WHERE theme_id = ? AND user_id IS NULL";
        
        $rows = $this->db->fetchAll($sql, [$themeId]);
        
        $customizations = [];
        foreach ($rows as $row) {
            $customizations[$row['customization_key']] = json_decode($row['customization_value'], true);
        }
        
        return $customizations;
    }
    
    private function getSiteSetting(string $key): mixed
    {
        $sql = "SELECT setting_value FROM site_settings WHERE setting_key = ?";
        $result = $this->db->fetchOne($sql, [$key]);
        
        return $result ? json_decode($result['setting_value'], true) : null;
    }
    
    private function setSiteSetting(string $key, mixed $value): void
    {
        $sql = "INSERT INTO site_settings (setting_key, setting_value, updated_at) 
                VALUES (?, ?, NOW()) 
                ON DUPLICATE KEY UPDATE 
                setting_value = VALUES(setting_value), 
                updated_at = NOW()";
        
        $this->db->execute($sql, [$key, json_encode($value, JSON_UNESCAPED_UNICODE)]);
    }
    
    private function clearThemeCache(?int $userId = null): void
    {
        $keys = [
            'current_theme',
            'available_themes',
            'theme_variables_' . ($userId ?? 'global')
        ];
        
        foreach ($keys as $key) {
            $this->cache->delete($key);
        }
    }
    
    private function clearAllThemeCache(): void
    {
        $this->cache->deletePattern('theme_*');
        $this->availableThemes = null;
        $this->currentTheme = null;
    }
}
