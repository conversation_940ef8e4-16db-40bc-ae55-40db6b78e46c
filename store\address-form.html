<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Adres Formu - EticSimple</title>
    <link rel="stylesheet" href="css/variables.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: var(--theme-bg-primary);
            color: var(--theme-text-primary);
            margin: 0;
            padding: 20px;
            line-height: 1.6;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: var(--theme-bg-secondary);
            padding: 30px;
            border-radius: 12px;
            border: 1px solid var(--theme-border-primary);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            color: var(--theme-accent-primary);
            margin-bottom: 30px;
            text-align: center;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: var(--theme-text-secondary);
        }
        
        .required {
            color: #e74c3c;
        }
        
        input, select, textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid var(--theme-border-primary);
            border-radius: 8px;
            background: var(--theme-bg-primary);
            color: var(--theme-text-primary);
            font-size: 14px;
            transition: border-color 0.3s ease;
            box-sizing: border-box;
        }
        
        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: var(--theme-accent-primary);
        }
        
        select:disabled {
            background-color: var(--theme-bg-tertiary);
            color: var(--theme-text-muted);
            cursor: not-allowed;
        }
        
        .hint {
            font-size: 12px;
            color: var(--theme-text-muted);
            margin-top: 5px;
            display: none;
        }
        
        .error {
            color: #e74c3c;
            font-size: 12px;
            margin-top: 5px;
            display: none;
        }
        
        .btn {
            background: var(--theme-accent-primary);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: background-color 0.3s ease;
            width: 100%;
        }
        
        .btn:hover {
            background: var(--theme-accent-secondary);
        }
        
        .btn:disabled {
            background: var(--theme-text-muted);
            cursor: not-allowed;
        }
        
        .loading {
            display: none;
            text-align: center;
            color: var(--theme-text-muted);
            margin: 20px 0;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
        }
        
        .address-type {
            display: flex;
            gap: 15px;
            margin-top: 10px;
        }
        
        .radio-group {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .radio-group input[type="radio"] {
            width: auto;
            margin: 0;
        }
        
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 20px;
                margin: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📍 Adres Bilgileri</h1>
        
        <div class="success" id="success-message">
            Adres başarıyla kaydedildi!
        </div>
        
        <form id="address-form">
            <div class="form-group">
                <label for="address-title">Adres Başlığı <span class="required">*</span></label>
                <input type="text" id="address-title" name="title" placeholder="Ev, İş, Diğer..." required>
                <div class="error" id="title-error"></div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="address-first-name">Ad <span class="required">*</span></label>
                    <input type="text" id="address-first-name" name="first_name" required>
                    <div class="error" id="first-name-error"></div>
                </div>
                
                <div class="form-group">
                    <label for="address-last-name">Soyad <span class="required">*</span></label>
                    <input type="text" id="address-last-name" name="last_name" required>
                    <div class="error" id="last-name-error"></div>
                </div>
            </div>
            
            <div class="form-group">
                <label for="address-company">Şirket</label>
                <input type="text" id="address-company" name="company" placeholder="Opsiyonel">
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="address-city">İl <span class="required">*</span></label>
                    <select id="address-city" name="city" required>
                        <option value="" disabled selected>İl seçiniz...</option>
                    </select>
                    <div class="error" id="city-error"></div>
                </div>
                
                <div class="form-group">
                    <label for="address-district">İlçe <span class="required">*</span></label>
                    <select id="address-district" name="district" required disabled>
                        <option value="" disabled selected>İlçe seçiniz...</option>
                    </select>
                    <div class="error" id="district-error"></div>
                </div>
            </div>
            
            <div class="form-group">
                <label for="address-neighborhood">Mahalle</label>
                <input type="text" id="address-neighborhood" name="neighborhood" placeholder="Mahalle adı">
            </div>
            
            <div class="form-group">
                <label for="address-line-1">Adres Satırı 1 <span class="required">*</span></label>
                <textarea id="address-line-1" name="address_line_1" rows="2" placeholder="Sokak, cadde, apartman adı ve numarası" required></textarea>
                <div class="error" id="address-line-1-error"></div>
            </div>
            
            <div class="form-group">
                <label for="address-line-2">Adres Satırı 2</label>
                <textarea id="address-line-2" name="address_line_2" rows="2" placeholder="Daire no, kat, diğer detaylar (opsiyonel)"></textarea>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="address-postal-code">Posta Kodu <span class="required">*</span></label>
                    <input type="text" id="address-postal-code" name="postal_code" placeholder="12345" maxlength="5" required>
                    <div class="hint" id="postal-code-hint">İlçe seçtikten sonra posta kodu aralığı gösterilecek</div>
                    <div class="error" id="postal-code-error"></div>
                </div>
                
                <div class="form-group">
                    <label for="address-phone">Telefon</label>
                    <input type="tel" id="address-phone" name="phone" placeholder="0555 123 45 67">
                    <div class="error" id="phone-error"></div>
                </div>
            </div>
            
            <div class="form-group">
                <label>Adres Tipi</label>
                <div class="address-type">
                    <div class="radio-group">
                        <input type="radio" id="type-both" name="type" value="both" checked>
                        <label for="type-both">Fatura ve Teslimat</label>
                    </div>
                    <div class="radio-group">
                        <input type="radio" id="type-billing" name="type" value="billing">
                        <label for="type-billing">Sadece Fatura</label>
                    </div>
                    <div class="radio-group">
                        <input type="radio" id="type-shipping" name="type" value="shipping">
                        <label for="type-shipping">Sadece Teslimat</label>
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <div class="radio-group">
                    <input type="checkbox" id="is-default" name="is_default">
                    <label for="is-default">Varsayılan adres olarak ayarla</label>
                </div>
            </div>
            
            <div class="loading" id="loading">
                Adres kaydediliyor...
            </div>
            
            <button type="submit" class="btn" id="submit-btn">
                💾 Adresi Kaydet
            </button>
        </form>
    </div>
    
    <script src="js/location-selector.js"></script>
    <script>
        // Adres formu için LocationSelector başlat
        const locationSelector = new AddressFormLocationSelector({
            citySelectId: 'address-city',
            districtSelectId: 'address-district'
        });
        
        // Form submit
        document.getElementById('address-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const data = Object.fromEntries(formData.entries());
            
            // LocationSelector verilerini ekle
            const locationData = locationSelector.getFormData();
            Object.assign(data, locationData);
            
            // Validation
            const validation = locationSelector.validate();
            if (!validation.isValid) {
                alert('Lütfen il ve ilçe seçimini yapınız');
                return;
            }
            
            // Loading göster
            document.getElementById('loading').style.display = 'block';
            document.getElementById('submit-btn').disabled = true;
            
            try {
                console.log('Adres verisi:', data);
                
                // Simüle edilmiş API çağrısı
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // Başarı mesajı göster
                document.getElementById('success-message').style.display = 'block';
                document.getElementById('address-form').reset();
                locationSelector.clear();
                
                // Başarı mesajını 3 saniye sonra gizle
                setTimeout(() => {
                    document.getElementById('success-message').style.display = 'none';
                }, 3000);
                
            } catch (error) {
                alert('Adres kaydedilirken hata oluştu: ' + error.message);
            } finally {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('submit-btn').disabled = false;
            }
        });
        
        // Posta kodu formatı
        document.getElementById('address-postal-code').addEventListener('input', (e) => {
            e.target.value = e.target.value.replace(/\D/g, '').slice(0, 5);
        });
        
        // Telefon formatı
        document.getElementById('address-phone').addEventListener('input', (e) => {
            let value = e.target.value.replace(/\D/g, '');
            if (value.startsWith('90')) value = value.slice(2);
            if (value.startsWith('0')) value = value.slice(1);
            if (value.length > 10) value = value.slice(0, 10);
            
            if (value.length >= 3) {
                value = value.replace(/(\d{3})(\d{3})(\d{2})(\d{2})/, '0$1 $2 $3 $4');
            }
            
            e.target.value = value;
        });
    </script>
</body>
</html>
