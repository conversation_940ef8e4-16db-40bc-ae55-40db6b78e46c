<?php

declare(strict_types=1);

namespace EticSimple\Modules\Product;

use DateTime;

/**
 * Product Model
 * 
 * Ürün veri modeli
 */
class Product
{
    private ?int $id = null;
    private int $categoryId;
    private string $name;
    private string $slug;
    private ?string $shortDescription = null;
    private ?string $description = null;
    private string $sku;
    private ?string $barcode = null;
    private float $price;
    private ?float $comparePrice = null;
    private ?float $costPrice = null;
    private string $currency = 'TRY';
    private int $stockQuantity = 0;
    private int $minStockLevel = 0;
    private ?int $maxStockLevel = null;
    private ?float $weight = null;
    private ?float $dimensionsLength = null;
    private ?float $dimensionsWidth = null;
    private ?float $dimensionsHeight = null;
    private bool $isActive = true;
    private bool $isFeatured = false;
    private bool $isDigital = false;
    private bool $requiresShipping = true;
    private bool $trackInventory = true;
    private bool $allowBackorder = false;
    private float $taxRate = 0.00;
    private ?string $brand = null;
    private ?string $manufacturer = null;
    private ?string $model = null;
    private ?int $warrantyPeriod = null;
    private string $warrantyType = 'none';
    private float $ratingAverage = 0.00;
    private int $ratingCount = 0;
    private int $viewCount = 0;
    private int $saleCount = 0;
    private ?string $metaTitle = null;
    private ?string $metaDescription = null;
    private ?string $metaKeywords = null;
    private ?DateTime $createdAt = null;
    private ?DateTime $updatedAt = null;
    
    // İlişkili veriler
    private ?Category $category = null;
    private array $images = [];
    private array $attributes = [];
    private array $variants = [];
    
    public function __construct(array $data = [])
    {
        if (!empty($data)) {
            $this->fill($data);
        }
    }
    
    /**
     * Array'den model doldur
     */
    public function fill(array $data): self
    {
        foreach ($data as $key => $value) {
            $method = 'set' . str_replace('_', '', ucwords($key, '_'));
            if (method_exists($this, $method)) {
                $this->$method($value);
            }
        }
        
        return $this;
    }
    
    /**
     * Model'i array'e çevir
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'category_id' => $this->categoryId,
            'name' => $this->name,
            'slug' => $this->slug,
            'short_description' => $this->shortDescription,
            'description' => $this->description,
            'sku' => $this->sku,
            'barcode' => $this->barcode,
            'price' => $this->price,
            'compare_price' => $this->comparePrice,
            'cost_price' => $this->costPrice,
            'currency' => $this->currency,
            'stock_quantity' => $this->stockQuantity,
            'min_stock_level' => $this->minStockLevel,
            'max_stock_level' => $this->maxStockLevel,
            'weight' => $this->weight,
            'dimensions_length' => $this->dimensionsLength,
            'dimensions_width' => $this->dimensionsWidth,
            'dimensions_height' => $this->dimensionsHeight,
            'is_active' => $this->isActive,
            'is_featured' => $this->isFeatured,
            'is_digital' => $this->isDigital,
            'requires_shipping' => $this->requiresShipping,
            'track_inventory' => $this->trackInventory,
            'allow_backorder' => $this->allowBackorder,
            'tax_rate' => $this->taxRate,
            'brand' => $this->brand,
            'manufacturer' => $this->manufacturer,
            'model' => $this->model,
            'warranty_period' => $this->warrantyPeriod,
            'warranty_type' => $this->warrantyType,
            'rating_average' => $this->ratingAverage,
            'rating_count' => $this->ratingCount,
            'view_count' => $this->viewCount,
            'sale_count' => $this->saleCount,
            'meta_title' => $this->metaTitle,
            'meta_description' => $this->metaDescription,
            'meta_keywords' => $this->metaKeywords,
            'created_at' => $this->createdAt?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updatedAt?->format('Y-m-d H:i:s'),
        ];
    }
    
    /**
     * Detaylı array (ilişkili veriler dahil)
     */
    public function toDetailedArray(): array
    {
        $data = $this->toArray();
        
        if ($this->category) {
            $data['category'] = $this->category->toArray();
        }
        
        if (!empty($this->images)) {
            $data['images'] = $this->images;
        }
        
        if (!empty($this->attributes)) {
            $data['attributes'] = $this->attributes;
        }
        
        if (!empty($this->variants)) {
            $data['variants'] = $this->variants;
        }
        
        return $data;
    }
    
    /**
     * Liste için özet array
     */
    public function toSummaryArray(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'slug' => $this->slug,
            'short_description' => $this->shortDescription,
            'price' => $this->price,
            'compare_price' => $this->comparePrice,
            'currency' => $this->currency,
            'stock_quantity' => $this->stockQuantity,
            'is_active' => $this->isActive,
            'is_featured' => $this->isFeatured,
            'brand' => $this->brand,
            'rating_average' => $this->ratingAverage,
            'rating_count' => $this->ratingCount,
            'primary_image' => $this->getPrimaryImage(),
            'category_name' => $this->category?->getName(),
        ];
    }
    
    /**
     * Stokta var mı?
     */
    public function isInStock(): bool
    {
        if (!$this->trackInventory) {
            return true;
        }
        
        return $this->stockQuantity > 0 || $this->allowBackorder;
    }
    
    /**
     * Düşük stok seviyesinde mi?
     */
    public function isLowStock(): bool
    {
        if (!$this->trackInventory) {
            return false;
        }
        
        return $this->stockQuantity <= $this->minStockLevel;
    }
    
    /**
     * İndirimli mi?
     */
    public function isOnSale(): bool
    {
        return $this->comparePrice && $this->comparePrice > $this->price;
    }
    
    /**
     * İndirim yüzdesi
     */
    public function getDiscountPercentage(): float
    {
        if (!$this->isOnSale()) {
            return 0.0;
        }
        
        return round((($this->comparePrice - $this->price) / $this->comparePrice) * 100, 1);
    }
    
    /**
     * Formatlanmış fiyat
     */
    public function getFormattedPrice(): string
    {
        return format_currency($this->price, $this->currency);
    }
    
    /**
     * Formatlanmış karşılaştırma fiyatı
     */
    public function getFormattedComparePrice(): string
    {
        if (!$this->comparePrice) {
            return '';
        }
        
        return format_currency($this->comparePrice, $this->currency);
    }
    
    /**
     * Ana resim URL'si
     */
    public function getPrimaryImage(): ?string
    {
        foreach ($this->images as $image) {
            if ($image['is_primary'] ?? false) {
                return $image['image_url'];
            }
        }
        
        return $this->images[0]['image_url'] ?? null;
    }
    
    /**
     * URL oluştur
     */
    public function getUrl(): string
    {
        return '/urun/' . $this->slug;
    }
    
    /**
     * SEO başlığı
     */
    public function getSeoTitle(): string
    {
        return $this->metaTitle ?: $this->name;
    }
    
    /**
     * SEO açıklaması
     */
    public function getSeoDescription(): string
    {
        return $this->metaDescription ?: $this->shortDescription ?: '';
    }
    
    /**
     * Boyutlar string olarak
     */
    public function getDimensionsString(): string
    {
        if (!$this->dimensionsLength || !$this->dimensionsWidth || !$this->dimensionsHeight) {
            return '';
        }
        
        return sprintf('%.1f x %.1f x %.1f cm', 
            $this->dimensionsLength, 
            $this->dimensionsWidth, 
            $this->dimensionsHeight
        );
    }
    
    /**
     * Garanti bilgisi
     */
    public function getWarrantyInfo(): string
    {
        if ($this->warrantyType === 'none' || !$this->warrantyPeriod) {
            return 'Garanti yok';
        }
        
        $types = [
            'manufacturer' => 'Üretici garantisi',
            'seller' => 'Satıcı garantisi',
            'extended' => 'Genişletilmiş garanti'
        ];
        
        $type = $types[$this->warrantyType] ?? 'Garanti';
        
        return sprintf('%s - %d ay', $type, $this->warrantyPeriod);
    }
    
    // Getter ve Setter metodları (önemli olanlar)
    
    public function getId(): ?int
    {
        return $this->id;
    }
    
    public function setId(?int $id): self
    {
        $this->id = $id;
        return $this;
    }
    
    public function getCategoryId(): int
    {
        return $this->categoryId;
    }
    
    public function setCategoryId(int $categoryId): self
    {
        $this->categoryId = $categoryId;
        return $this;
    }
    
    public function getName(): string
    {
        return $this->name;
    }
    
    public function setName(string $name): self
    {
        $this->name = trim($name);
        return $this;
    }
    
    public function getSlug(): string
    {
        return $this->slug;
    }
    
    public function setSlug(string $slug): self
    {
        $this->slug = trim($slug);
        return $this;
    }
    
    public function getSku(): string
    {
        return $this->sku;
    }
    
    public function setSku(string $sku): self
    {
        $this->sku = trim($sku);
        return $this;
    }
    
    public function getPrice(): float
    {
        return $this->price;
    }
    
    public function setPrice(float $price): self
    {
        $this->price = $price;
        return $this;
    }
    
    public function getStockQuantity(): int
    {
        return $this->stockQuantity;
    }
    
    public function setStockQuantity(int $stockQuantity): self
    {
        $this->stockQuantity = $stockQuantity;
        return $this;
    }
    
    public function isActive(): bool
    {
        return $this->isActive;
    }
    
    public function setIsActive(bool $isActive): self
    {
        $this->isActive = $isActive;
        return $this;
    }
    
    public function getCategory(): ?Category
    {
        return $this->category;
    }
    
    public function setCategory(?Category $category): self
    {
        $this->category = $category;
        return $this;
    }
    
    public function getImages(): array
    {
        return $this->images;
    }
    
    public function setImages(array $images): self
    {
        $this->images = $images;
        return $this;
    }
    
    public function getAttributes(): array
    {
        return $this->attributes;
    }
    
    public function setAttributes(array $attributes): self
    {
        $this->attributes = $attributes;
        return $this;
    }
}
