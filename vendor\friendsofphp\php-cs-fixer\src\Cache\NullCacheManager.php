<?php

declare(strict_types=1);

/*
 * This file is part of PHP CS Fixer.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *     <PERSON><PERSON> <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled
 * with this source code in the file LICENSE.
 */

namespace PhpCsFixer\Cache;

/**
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @internal
 */
final class NullCacheManager implements CacheManagerInterface
{
    public function needFixing(string $file, string $fileContent): bool
    {
        return true;
    }

    public function setFile(string $file, string $fileContent): void {}

    public function setFileHash(string $file, string $hash): void {}
}
