<?php

declare(strict_types=1);

namespace EticSimple\Modules\User;

use EticSimple\Core\Database\DatabaseManager;
use EticSimple\Core\Application;
use DateTime;

/**
 * User Repository
 * 
 * Kullanıcı veritabanı işlemleri
 */
class UserRepository
{
    private DatabaseManager $db;
    
    public function __construct()
    {
        $this->db = Application::getInstance()->get('db');
    }
    
    /**
     * Kullanıcı oluştur
     */
    public function create(User $user): ?User
    {
        try {
            $data = [
                'email' => $user->getEmail(),
                'password_hash' => $user->getPasswordHash(),
                'first_name' => $user->getFirstName(),
                'last_name' => $user->getLastName(),
                'phone' => $user->getPhone(),
                'tc_kimlik_no' => $user->getTcKimlikNo(),
                'birth_date' => $user->getBirthDate()?->format('Y-m-d'),
                'gender' => $user->getGender(),
                'status' => $user->getStatus(),
                'preferred_language' => $user->getPreferredLanguage(),
                'preferred_theme' => $user->getPreferredTheme(),
                'newsletter_subscribed' => $user->isNewsletterSubscribed() ? 1 : 0,
                'marketing_emails' => $user->isMarketingEmails() ? 1 : 0,
                'two_factor_enabled' => $user->isTwoFactorEnabled() ? 1 : 0,
            ];
            
            // Null değerleri filtrele
            $data = array_filter($data, fn($value) => $value !== null);
            
            $userId = $this->db->insert('users', $data);
            
            return $this->findById($userId);
            
        } catch (\Exception $e) {
            Application::getInstance()->get('logger')->error('User creation failed', [
                'email' => $user->getEmail(),
                'error' => $e->getMessage()
            ]);
            
            return null;
        }
    }
    
    /**
     * ID ile kullanıcı bul
     */
    public function findById(int $id): ?User
    {
        $sql = "SELECT * FROM users WHERE id = ?";
        $userData = $this->db->fetchOne($sql, [$id]);
        
        return $userData ? new User($userData) : null;
    }
    
    /**
     * Email ile kullanıcı bul
     */
    public function findByEmail(string $email): ?User
    {
        $sql = "SELECT * FROM users WHERE email = ?";
        $userData = $this->db->fetchOne($sql, [strtolower(trim($email))]);
        
        return $userData ? new User($userData) : null;
    }
    
    /**
     * T.C. Kimlik No ile kullanıcı bul
     */
    public function findByTcKimlikNo(string $tcKimlikNo): ?User
    {
        $sql = "SELECT * FROM users WHERE tc_kimlik_no = ?";
        $userData = $this->db->fetchOne($sql, [$tcKimlikNo]);
        
        return $userData ? new User($userData) : null;
    }
    
    /**
     * Telefon ile kullanıcı bul
     */
    public function findByPhone(string $phone): ?User
    {
        $sql = "SELECT * FROM users WHERE phone = ?";
        $userData = $this->db->fetchOne($sql, [$phone]);
        
        return $userData ? new User($userData) : null;
    }
    
    /**
     * Kullanıcı güncelle
     */
    public function update(User $user): bool
    {
        if (!$user->getId()) {
            return false;
        }
        
        try {
            $data = [
                'email' => $user->getEmail(),
                'first_name' => $user->getFirstName(),
                'last_name' => $user->getLastName(),
                'phone' => $user->getPhone(),
                'tc_kimlik_no' => $user->getTcKimlikNo(),
                'birth_date' => $user->getBirthDate()?->format('Y-m-d'),
                'gender' => $user->getGender(),
                'status' => $user->getStatus(),
                'preferred_language' => $user->getPreferredLanguage(),
                'preferred_theme' => $user->getPreferredTheme(),
                'newsletter_subscribed' => $user->isNewsletterSubscribed() ? 1 : 0,
                'marketing_emails' => $user->isMarketingEmails() ? 1 : 0,
                'two_factor_enabled' => $user->isTwoFactorEnabled() ? 1 : 0,
            ];
            
            // Null değerleri filtrele
            $data = array_filter($data, fn($value) => $value !== null);
            
            $affectedRows = $this->db->update('users', $data, ['id' => $user->getId()]);
            
            return $affectedRows > 0;
            
        } catch (\Exception $e) {
            Application::getInstance()->get('logger')->error('User update failed', [
                'user_id' => $user->getId(),
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }
    
    /**
     * Şifre güncelle
     */
    public function updatePassword(int $userId, string $passwordHash): bool
    {
        try {
            $affectedRows = $this->db->update(
                'users',
                ['password_hash' => $passwordHash],
                ['id' => $userId]
            );
            
            return $affectedRows > 0;
            
        } catch (\Exception $e) {
            Application::getInstance()->get('logger')->error('Password update failed', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }
    
    /**
     * Email doğrulama
     */
    public function markEmailAsVerified(int $userId): bool
    {
        try {
            $affectedRows = $this->db->update(
                'users',
                ['email_verified_at' => date('Y-m-d H:i:s')],
                ['id' => $userId]
            );
            
            return $affectedRows > 0;
            
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * Telefon doğrulama
     */
    public function markPhoneAsVerified(int $userId): bool
    {
        try {
            $affectedRows = $this->db->update(
                'users',
                ['phone_verified_at' => date('Y-m-d H:i:s')],
                ['id' => $userId]
            );
            
            return $affectedRows > 0;
            
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * Son giriş zamanını güncelle
     */
    public function updateLastLogin(int $userId): bool
    {
        try {
            $this->db->execute(
                "UPDATE users SET 
                    last_login_at = NOW(), 
                    login_count = login_count + 1,
                    failed_login_attempts = 0
                WHERE id = ?",
                [$userId]
            );
            
            return true;
            
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * Başarısız giriş denemesi kaydet
     */
    public function incrementFailedLoginAttempts(string $email): bool
    {
        try {
            $this->db->execute(
                "UPDATE users SET 
                    failed_login_attempts = failed_login_attempts + 1
                WHERE email = ?",
                [$email]
            );
            
            return true;
            
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * Kullanıcıyı kilitle
     */
    public function lockUser(int $userId, int $minutes = 30): bool
    {
        try {
            $lockedUntil = (new DateTime())->modify("+{$minutes} minutes")->format('Y-m-d H:i:s');
            
            $affectedRows = $this->db->update(
                'users',
                ['locked_until' => $lockedUntil],
                ['id' => $userId]
            );
            
            return $affectedRows > 0;
            
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * Kullanıcı kilidini aç
     */
    public function unlockUser(int $userId): bool
    {
        try {
            $affectedRows = $this->db->update(
                'users',
                ['locked_until' => null, 'failed_login_attempts' => 0],
                ['id' => $userId]
            );
            
            return $affectedRows > 0;
            
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * Kullanıcı sil (soft delete)
     */
    public function delete(int $userId): bool
    {
        try {
            $affectedRows = $this->db->update(
                'users',
                ['status' => 'inactive'],
                ['id' => $userId]
            );
            
            return $affectedRows > 0;
            
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * Email benzersiz mi kontrol et
     */
    public function isEmailUnique(string $email, ?int $excludeUserId = null): bool
    {
        $sql = "SELECT COUNT(*) FROM users WHERE email = ?";
        $params = [strtolower(trim($email))];
        
        if ($excludeUserId) {
            $sql .= " AND id != ?";
            $params[] = $excludeUserId;
        }
        
        $count = $this->db->fetchColumn($sql, $params);
        
        return $count == 0;
    }
    
    /**
     * T.C. Kimlik No benzersiz mi kontrol et
     */
    public function isTcKimlikNoUnique(string $tcKimlikNo, ?int $excludeUserId = null): bool
    {
        $sql = "SELECT COUNT(*) FROM users WHERE tc_kimlik_no = ?";
        $params = [$tcKimlikNo];
        
        if ($excludeUserId) {
            $sql .= " AND id != ?";
            $params[] = $excludeUserId;
        }
        
        $count = $this->db->fetchColumn($sql, $params);
        
        return $count == 0;
    }
    
    /**
     * Kullanıcı listesi (sayfalama ile)
     */
    public function findAll(int $page = 1, int $perPage = 20, array $filters = []): array
    {
        $offset = ($page - 1) * $perPage;
        
        $sql = "SELECT * FROM users WHERE 1=1";
        $params = [];
        
        // Filtreler
        if (!empty($filters['status'])) {
            $sql .= " AND status = ?";
            $params[] = $filters['status'];
        }
        
        if (!empty($filters['search'])) {
            $sql .= " AND (first_name LIKE ? OR last_name LIKE ? OR email LIKE ?)";
            $searchTerm = '%' . $filters['search'] . '%';
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }
        
        $sql .= " ORDER BY created_at DESC LIMIT ? OFFSET ?";
        $params[] = $perPage;
        $params[] = $offset;
        
        $usersData = $this->db->fetchAll($sql, $params);
        
        $users = [];
        foreach ($usersData as $userData) {
            $users[] = new User($userData);
        }
        
        return $users;
    }
    
    /**
     * Toplam kullanıcı sayısı
     */
    public function count(array $filters = []): int
    {
        $sql = "SELECT COUNT(*) FROM users WHERE 1=1";
        $params = [];
        
        // Filtreler
        if (!empty($filters['status'])) {
            $sql .= " AND status = ?";
            $params[] = $filters['status'];
        }
        
        if (!empty($filters['search'])) {
            $sql .= " AND (first_name LIKE ? OR last_name LIKE ? OR email LIKE ?)";
            $searchTerm = '%' . $filters['search'] . '%';
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }
        
        return (int) $this->db->fetchColumn($sql, $params);
    }
}
