<?php

declare(strict_types=1);

namespace EticSimple\Core\Auth;

use Firebase\JWT\JWT;
use Firebase\JWT\Key;

/**
 * Authentication Manager
 * 
 * JWT tabanlı kimlik doğrulama sistemi
 */
class AuthManager
{
    private array $config;
    
    public function __construct(array $config)
    {
        $this->config = $config;
    }
    
    /**
     * JWT token oluştur
     */
    public function generateToken(array $payload): string
    {
        $now = time();
        
        $tokenPayload = array_merge($payload, [
            'iat' => $now,
            'exp' => $now + $this->config['jwt_ttl'],
            'iss' => $_ENV['APP_URL'] ?? 'eticsimple'
        ]);
        
        return JWT::encode($tokenPayload, $this->config['jwt_secret'], $this->config['jwt_algorithm']);
    }
    
    /**
     * JWT token doğrula
     */
    public function validateToken(string $token): ?array
    {
        try {
            $decoded = JWT::decode($token, new Key($this->config['jwt_secret'], $this->config['jwt_algorithm']));
            return (array) $decoded;
        } catch (\Exception $e) {
            return null;
        }
    }
    
    /**
     * Refresh token oluştur
     */
    public function generateRefreshToken(array $payload): string
    {
        $now = time();
        
        $tokenPayload = array_merge($payload, [
            'iat' => $now,
            'exp' => $now + $this->config['jwt_refresh_ttl'],
            'type' => 'refresh',
            'iss' => $_ENV['APP_URL'] ?? 'eticsimple'
        ]);
        
        return JWT::encode($tokenPayload, $this->config['jwt_secret'], $this->config['jwt_algorithm']);
    }
    
    /**
     * Bearer token'ı header'dan al
     */
    public function getBearerToken(): ?string
    {
        $headers = getallheaders();
        
        if (isset($headers['Authorization'])) {
            $authHeader = $headers['Authorization'];
            
            if (preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
                return $matches[1];
            }
        }
        
        return null;
    }
    
    /**
     * Mevcut kullanıcıyı al
     */
    public function getCurrentUser(): ?array
    {
        $token = $this->getBearerToken();
        
        if (!$token) {
            return null;
        }
        
        return $this->validateToken($token);
    }
    
    /**
     * Kullanıcı giriş yapmış mı kontrol et
     */
    public function isAuthenticated(): bool
    {
        return $this->getCurrentUser() !== null;
    }
    
    /**
     * Şifre hash'le
     */
    public function hashPassword(string $password): string
    {
        $cost = (int) ($_ENV['BCRYPT_ROUNDS'] ?? 12);
        return password_hash($password, PASSWORD_BCRYPT, ['cost' => $cost]);
    }
    
    /**
     * Şifre doğrula
     */
    public function verifyPassword(string $password, string $hash): bool
    {
        return password_verify($password, $hash);
    }
}
