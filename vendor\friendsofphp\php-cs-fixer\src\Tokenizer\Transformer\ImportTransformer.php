<?php

declare(strict_types=1);

/*
 * This file is part of PHP CS Fixer.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *     <PERSON><PERSON> <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled
 * with this source code in the file LICENSE.
 */

namespace Php<PERSON><PERSON>ixer\Tokenizer\Transformer;

use <PERSON>pCs<PERSON><PERSON>er\Tokenizer\AbstractTransformer;
use PhpCsF<PERSON>er\Tokenizer\CT;
use <PERSON>p<PERSON>F<PERSON>er\Tokenizer\Token;
use <PERSON>pCsF<PERSON>er\Tokenizer\Tokens;

/**
 * Transform const/function import tokens.
 *
 * Performed transformations:
 * - T_CONST into CT::T_CONST_IMPORT
 * - T_FUNCTION into CT::T_FUNCTION_IMPORT
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @internal
 */
final class ImportTransformer extends AbstractTransformer
{
    public function getPriority(): int
    {
        // Should run after CurlyBraceTransformer and ReturnRefTransformer
        return -1;
    }

    public function getRequiredPhpVersionId(): int
    {
        return 5_06_00;
    }

    public function process(Tokens $tokens, Token $token, int $index): void
    {
        if (!$token->isGivenKind([T_CONST, T_FUNCTION])) {
            return;
        }

        $prevToken = $tokens[$tokens->getPrevMeaningfulToken($index)];

        if (!$prevToken->isGivenKind(T_USE)) {
            $nextToken = $tokens[$tokens->getNextTokenOfKind($index, ['=', '(', [CT::T_RETURN_REF], [CT::T_GROUP_IMPORT_BRACE_CLOSE]])];

            if (!$nextToken->isGivenKind(CT::T_GROUP_IMPORT_BRACE_CLOSE)) {
                return;
            }
        }

        $tokens[$index] = new Token([
            $token->isGivenKind(T_FUNCTION) ? CT::T_FUNCTION_IMPORT : CT::T_CONST_IMPORT,
            $token->getContent(),
        ]);
    }

    public function getCustomTokens(): array
    {
        return [CT::T_CONST_IMPORT, CT::T_FUNCTION_IMPORT];
    }
}
