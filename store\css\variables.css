/* EticSimple CSS Variables - <PERSON><PERSON> */

:root {
  /* <PERSON><PERSON> (Light) */
  --theme-bg-primary: #ffffff;
  --theme-bg-secondary: #f9fafb;
  --theme-bg-tertiary: #f3f4f6;
  --theme-text-primary: #111827;
  --theme-text-secondary: #6b7280;
  --theme-text-muted: #9ca3af;
  --theme-border-primary: #e5e7eb;
  --theme-border-secondary: #d1d5db;
  --theme-accent-primary: #0ea5e9;
  --theme-accent-secondary: #d946ef;
  
  /* Sabit Renk Paleti */
  --primary-50: #f0f9ff;
  --primary-100: #e0f2fe;
  --primary-200: #bae6fd;
  --primary-300: #7dd3fc;
  --primary-400: #38bdf8;
  --primary-500: #0ea5e9;
  --primary-600: #0284c7;
  --primary-700: #0369a1;
  --primary-800: #075985;
  --primary-900: #0c4a6e;

  --secondary-50: #fdf4ff;
  --secondary-100: #fae8ff;
  --secondary-200: #f5d0fe;
  --secondary-300: #f0abfc;
  --secondary-400: #e879f9;
  --secondary-500: #d946ef;
  --secondary-600: #c026d3;
  --secondary-700: #a21caf;
  --secondary-800: #86198f;
  --secondary-900: #701a75;

  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  --success-50: #f0fdf4;
  --success-500: #22c55e;
  --success-600: #16a34a;

  --warning-50: #fffbeb;
  --warning-500: #f59e0b;
  --warning-600: #d97706;

  --error-50: #fef2f2;
  --error-500: #ef4444;
  --error-600: #dc2626;

  --info-50: #eff6ff;
  --info-500: #3b82f6;
  --info-600: #2563eb;

  --white: #ffffff;
  --black: #000000;

  /* Font Aileleri */
  --font-primary: 'Inter', 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  --font-heading: 'Poppins', 'Inter', sans-serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;

  /* Font Boyutları */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  --text-5xl: 3rem;

  /* Font Ağırlıkları */
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;

  /* Line Heights */
  --leading-tight: 1.25;
  --leading-normal: 1.5;
  --leading-relaxed: 1.75;

  /* Spacing (8px base) */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;
  --space-24: 6rem;

  /* Border Radius */
  --radius-sm: 0.125rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-full: 9999px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Transitions */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.2s ease;
  --transition-slow: 0.3s ease;

  /* Z-Index */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;

  /* Container Sizes */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
  --container-2xl: 1536px;

  /* Breakpoints */
  --bp-sm: 640px;
  --bp-md: 768px;
  --bp-lg: 1024px;
  --bp-xl: 1280px;
  --bp-2xl: 1536px;
}

/* Tema Varyantları */

/* Light Theme (Varsayılan) */
[data-theme="light"] {
  --theme-bg-primary: #ffffff;
  --theme-bg-secondary: #f9fafb;
  --theme-bg-tertiary: #f3f4f6;
  --theme-text-primary: #111827;
  --theme-text-secondary: #6b7280;
  --theme-text-muted: #9ca3af;
  --theme-border-primary: #e5e7eb;
  --theme-border-secondary: #d1d5db;
  --theme-accent-primary: #0ea5e9;
  --theme-accent-secondary: #d946ef;
}

/* Dark Theme */
[data-theme="dark"] {
  --theme-bg-primary: #111827;
  --theme-bg-secondary: #1f2937;
  --theme-bg-tertiary: #374151;
  --theme-text-primary: #f9fafb;
  --theme-text-secondary: #d1d5db;
  --theme-text-muted: #9ca3af;
  --theme-border-primary: #374151;
  --theme-border-secondary: #4b5563;
  --theme-accent-primary: #0ea5e9;
  --theme-accent-secondary: #d946ef;
}

/* Blue Theme */
[data-theme="blue"] {
  --theme-bg-primary: #f8fafc;
  --theme-bg-secondary: #f1f5f9;
  --theme-bg-tertiary: #e2e8f0;
  --theme-text-primary: #0f172a;
  --theme-text-secondary: #475569;
  --theme-text-muted: #64748b;
  --theme-border-primary: #cbd5e1;
  --theme-border-secondary: #94a3b8;
  --theme-accent-primary: #3b82f6;
  --theme-accent-secondary: #1e40af;
}

/* Green Theme */
[data-theme="green"] {
  --theme-bg-primary: #f7fdf7;
  --theme-bg-secondary: #f0fdf4;
  --theme-bg-tertiary: #dcfce7;
  --theme-text-primary: #14532d;
  --theme-text-secondary: #166534;
  --theme-text-muted: #22c55e;
  --theme-border-primary: #bbf7d0;
  --theme-border-secondary: #86efac;
  --theme-accent-primary: #22c55e;
  --theme-accent-secondary: #16a34a;
}
