<?php

declare(strict_types=1);

// Basit route test
header('Content-Type: application/json; charset=utf-8');

// Composer autoloader
require_once dirname(__DIR__, 2) . '/vendor/autoload.php';

use EticSimple\Core\Application;

try {
    // Application'ı başlat
    $app = Application::getInstance();
    
    // Router'ı al
    $router = $app->get('router');
    
    // Basit route ekle
    $router->get('/health', function () {
        return [
            'success' => true,
            'message' => 'Health check başarılı',
            'timestamp' => date('c')
        ];
    });
    
    // İsteği işle
    $app->handleRequest();
    
} catch (\Throwable $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Route test hatası: ' . $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ], JSON_UNESCAPED_UNICODE);
}
