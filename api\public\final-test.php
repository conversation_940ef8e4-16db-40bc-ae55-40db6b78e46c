<?php

declare(strict_types=1);

// Final test - working API
header('Content-Type: application/json; charset=utf-8');

// Composer autoloader
require_once dirname(__DIR__, 2) . '/vendor/autoload.php';

// Helper fonksiyon<PERSON><PERSON>ükle
require_once dirname(__DIR__) . '/core/helpers.php';

try {
    // Environment yükle
    $dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__, 2));
    $dotenv->load();
    
    // Request URI'yi kontrol et
    $uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
    $method = $_SERVER['REQUEST_METHOD'];
    
    // Basit routing
    if ($uri === '/api/health' && $method === 'GET') {
        echo json_encode(response([
            'status' => 'OK',
            'timestamp' => date('c'),
            'version' => '1.0.0'
        ], '<PERSON><PERSON>m çalışıyor'), JSON_UNESCAPED_UNICODE);
        
    } elseif ($uri === '/api/test' && $method === 'GET') {
        echo json_encode(response([
            'message' => 'Test endpoint çalışıyor',
            'php_version' => PHP_VERSION,
            'environment' => $_ENV['APP_ENV'] ?? 'unknown'
        ], 'Test başarılı'), JSON_UNESCAPED_UNICODE);
        
    } else {
        http_response_code(404);
        echo json_encode(error_response('Endpoint bulunamadı', [], 404), JSON_UNESCAPED_UNICODE);
    }
    
} catch (\Throwable $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Final test hatası: ' . $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ], JSON_UNESCAPED_UNICODE);
}
