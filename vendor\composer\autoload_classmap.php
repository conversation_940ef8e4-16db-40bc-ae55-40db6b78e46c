<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'Attribute' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/Attribute.php',
    'CURLStringFile' => $vendorDir . '/symfony/polyfill-php81/Resources/stubs/CURLStringFile.php',
    'Clue\\React\\NDJson\\Decoder' => $vendorDir . '/clue/ndjson-react/src/Decoder.php',
    'Clue\\React\\NDJson\\Encoder' => $vendorDir . '/clue/ndjson-react/src/Encoder.php',
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'Composer\\Pcre\\MatchAllResult' => $vendorDir . '/composer/pcre/src/MatchAllResult.php',
    'Composer\\Pcre\\MatchAllStrictGroupsResult' => $vendorDir . '/composer/pcre/src/MatchAllStrictGroupsResult.php',
    'Composer\\Pcre\\MatchAllWithOffsetsResult' => $vendorDir . '/composer/pcre/src/MatchAllWithOffsetsResult.php',
    'Composer\\Pcre\\MatchResult' => $vendorDir . '/composer/pcre/src/MatchResult.php',
    'Composer\\Pcre\\MatchStrictGroupsResult' => $vendorDir . '/composer/pcre/src/MatchStrictGroupsResult.php',
    'Composer\\Pcre\\MatchWithOffsetsResult' => $vendorDir . '/composer/pcre/src/MatchWithOffsetsResult.php',
    'Composer\\Pcre\\PHPStan\\InvalidRegexPatternRule' => $vendorDir . '/composer/pcre/src/PHPStan/InvalidRegexPatternRule.php',
    'Composer\\Pcre\\PHPStan\\PregMatchFlags' => $vendorDir . '/composer/pcre/src/PHPStan/PregMatchFlags.php',
    'Composer\\Pcre\\PHPStan\\PregMatchParameterOutTypeExtension' => $vendorDir . '/composer/pcre/src/PHPStan/PregMatchParameterOutTypeExtension.php',
    'Composer\\Pcre\\PHPStan\\PregMatchTypeSpecifyingExtension' => $vendorDir . '/composer/pcre/src/PHPStan/PregMatchTypeSpecifyingExtension.php',
    'Composer\\Pcre\\PHPStan\\PregReplaceCallbackClosureTypeExtension' => $vendorDir . '/composer/pcre/src/PHPStan/PregReplaceCallbackClosureTypeExtension.php',
    'Composer\\Pcre\\PHPStan\\UnsafeStrictGroupsCallRule' => $vendorDir . '/composer/pcre/src/PHPStan/UnsafeStrictGroupsCallRule.php',
    'Composer\\Pcre\\PcreException' => $vendorDir . '/composer/pcre/src/PcreException.php',
    'Composer\\Pcre\\Preg' => $vendorDir . '/composer/pcre/src/Preg.php',
    'Composer\\Pcre\\Regex' => $vendorDir . '/composer/pcre/src/Regex.php',
    'Composer\\Pcre\\ReplaceResult' => $vendorDir . '/composer/pcre/src/ReplaceResult.php',
    'Composer\\Pcre\\UnexpectedNullMatchException' => $vendorDir . '/composer/pcre/src/UnexpectedNullMatchException.php',
    'Composer\\Semver\\Comparator' => $vendorDir . '/composer/semver/src/Comparator.php',
    'Composer\\Semver\\CompilingMatcher' => $vendorDir . '/composer/semver/src/CompilingMatcher.php',
    'Composer\\Semver\\Constraint\\Bound' => $vendorDir . '/composer/semver/src/Constraint/Bound.php',
    'Composer\\Semver\\Constraint\\Constraint' => $vendorDir . '/composer/semver/src/Constraint/Constraint.php',
    'Composer\\Semver\\Constraint\\ConstraintInterface' => $vendorDir . '/composer/semver/src/Constraint/ConstraintInterface.php',
    'Composer\\Semver\\Constraint\\MatchAllConstraint' => $vendorDir . '/composer/semver/src/Constraint/MatchAllConstraint.php',
    'Composer\\Semver\\Constraint\\MatchNoneConstraint' => $vendorDir . '/composer/semver/src/Constraint/MatchNoneConstraint.php',
    'Composer\\Semver\\Constraint\\MultiConstraint' => $vendorDir . '/composer/semver/src/Constraint/MultiConstraint.php',
    'Composer\\Semver\\Interval' => $vendorDir . '/composer/semver/src/Interval.php',
    'Composer\\Semver\\Intervals' => $vendorDir . '/composer/semver/src/Intervals.php',
    'Composer\\Semver\\Semver' => $vendorDir . '/composer/semver/src/Semver.php',
    'Composer\\Semver\\VersionParser' => $vendorDir . '/composer/semver/src/VersionParser.php',
    'Composer\\XdebugHandler\\PhpConfig' => $vendorDir . '/composer/xdebug-handler/src/PhpConfig.php',
    'Composer\\XdebugHandler\\Process' => $vendorDir . '/composer/xdebug-handler/src/Process.php',
    'Composer\\XdebugHandler\\Status' => $vendorDir . '/composer/xdebug-handler/src/Status.php',
    'Composer\\XdebugHandler\\XdebugHandler' => $vendorDir . '/composer/xdebug-handler/src/XdebugHandler.php',
    'DeepCopy\\DeepCopy' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/DeepCopy.php',
    'DeepCopy\\Exception\\CloneException' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/Exception/CloneException.php',
    'DeepCopy\\Exception\\PropertyException' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/Exception/PropertyException.php',
    'DeepCopy\\Filter\\ChainableFilter' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/Filter/ChainableFilter.php',
    'DeepCopy\\Filter\\Doctrine\\DoctrineCollectionFilter' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/Filter/Doctrine/DoctrineCollectionFilter.php',
    'DeepCopy\\Filter\\Doctrine\\DoctrineEmptyCollectionFilter' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/Filter/Doctrine/DoctrineEmptyCollectionFilter.php',
    'DeepCopy\\Filter\\Doctrine\\DoctrineProxyFilter' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/Filter/Doctrine/DoctrineProxyFilter.php',
    'DeepCopy\\Filter\\Filter' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/Filter/Filter.php',
    'DeepCopy\\Filter\\KeepFilter' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/Filter/KeepFilter.php',
    'DeepCopy\\Filter\\ReplaceFilter' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/Filter/ReplaceFilter.php',
    'DeepCopy\\Filter\\SetNullFilter' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/Filter/SetNullFilter.php',
    'DeepCopy\\Matcher\\Doctrine\\DoctrineProxyMatcher' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/Matcher/Doctrine/DoctrineProxyMatcher.php',
    'DeepCopy\\Matcher\\Matcher' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/Matcher/Matcher.php',
    'DeepCopy\\Matcher\\PropertyMatcher' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/Matcher/PropertyMatcher.php',
    'DeepCopy\\Matcher\\PropertyNameMatcher' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/Matcher/PropertyNameMatcher.php',
    'DeepCopy\\Matcher\\PropertyTypeMatcher' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/Matcher/PropertyTypeMatcher.php',
    'DeepCopy\\Reflection\\ReflectionHelper' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/Reflection/ReflectionHelper.php',
    'DeepCopy\\TypeFilter\\Date\\DateIntervalFilter' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/TypeFilter/Date/DateIntervalFilter.php',
    'DeepCopy\\TypeFilter\\Date\\DatePeriodFilter' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/TypeFilter/Date/DatePeriodFilter.php',
    'DeepCopy\\TypeFilter\\ReplaceFilter' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/TypeFilter/ReplaceFilter.php',
    'DeepCopy\\TypeFilter\\ShallowCopyFilter' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/TypeFilter/ShallowCopyFilter.php',
    'DeepCopy\\TypeFilter\\Spl\\ArrayObjectFilter' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/TypeFilter/Spl/ArrayObjectFilter.php',
    'DeepCopy\\TypeFilter\\Spl\\SplDoublyLinkedList' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/TypeFilter/Spl/SplDoublyLinkedList.php',
    'DeepCopy\\TypeFilter\\Spl\\SplDoublyLinkedListFilter' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/TypeFilter/Spl/SplDoublyLinkedListFilter.php',
    'DeepCopy\\TypeFilter\\TypeFilter' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/TypeFilter/TypeFilter.php',
    'DeepCopy\\TypeMatcher\\TypeMatcher' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/TypeMatcher/TypeMatcher.php',
    'Doctrine\\Common\\Lexer\\AbstractLexer' => $vendorDir . '/doctrine/lexer/src/AbstractLexer.php',
    'Doctrine\\Common\\Lexer\\Token' => $vendorDir . '/doctrine/lexer/src/Token.php',
    'Doctrine\\Deprecations\\Deprecation' => $vendorDir . '/doctrine/deprecations/src/Deprecation.php',
    'Doctrine\\Deprecations\\PHPUnit\\VerifyDeprecations' => $vendorDir . '/doctrine/deprecations/src/PHPUnit/VerifyDeprecations.php',
    'Dotenv\\Dotenv' => $vendorDir . '/vlucas/phpdotenv/src/Dotenv.php',
    'Dotenv\\Exception\\ExceptionInterface' => $vendorDir . '/vlucas/phpdotenv/src/Exception/ExceptionInterface.php',
    'Dotenv\\Exception\\InvalidEncodingException' => $vendorDir . '/vlucas/phpdotenv/src/Exception/InvalidEncodingException.php',
    'Dotenv\\Exception\\InvalidFileException' => $vendorDir . '/vlucas/phpdotenv/src/Exception/InvalidFileException.php',
    'Dotenv\\Exception\\InvalidPathException' => $vendorDir . '/vlucas/phpdotenv/src/Exception/InvalidPathException.php',
    'Dotenv\\Exception\\ValidationException' => $vendorDir . '/vlucas/phpdotenv/src/Exception/ValidationException.php',
    'Dotenv\\Loader\\Loader' => $vendorDir . '/vlucas/phpdotenv/src/Loader/Loader.php',
    'Dotenv\\Loader\\LoaderInterface' => $vendorDir . '/vlucas/phpdotenv/src/Loader/LoaderInterface.php',
    'Dotenv\\Loader\\Resolver' => $vendorDir . '/vlucas/phpdotenv/src/Loader/Resolver.php',
    'Dotenv\\Parser\\Entry' => $vendorDir . '/vlucas/phpdotenv/src/Parser/Entry.php',
    'Dotenv\\Parser\\EntryParser' => $vendorDir . '/vlucas/phpdotenv/src/Parser/EntryParser.php',
    'Dotenv\\Parser\\Lexer' => $vendorDir . '/vlucas/phpdotenv/src/Parser/Lexer.php',
    'Dotenv\\Parser\\Lines' => $vendorDir . '/vlucas/phpdotenv/src/Parser/Lines.php',
    'Dotenv\\Parser\\Parser' => $vendorDir . '/vlucas/phpdotenv/src/Parser/Parser.php',
    'Dotenv\\Parser\\ParserInterface' => $vendorDir . '/vlucas/phpdotenv/src/Parser/ParserInterface.php',
    'Dotenv\\Parser\\Value' => $vendorDir . '/vlucas/phpdotenv/src/Parser/Value.php',
    'Dotenv\\Repository\\AdapterRepository' => $vendorDir . '/vlucas/phpdotenv/src/Repository/AdapterRepository.php',
    'Dotenv\\Repository\\Adapter\\AdapterInterface' => $vendorDir . '/vlucas/phpdotenv/src/Repository/Adapter/AdapterInterface.php',
    'Dotenv\\Repository\\Adapter\\ApacheAdapter' => $vendorDir . '/vlucas/phpdotenv/src/Repository/Adapter/ApacheAdapter.php',
    'Dotenv\\Repository\\Adapter\\ArrayAdapter' => $vendorDir . '/vlucas/phpdotenv/src/Repository/Adapter/ArrayAdapter.php',
    'Dotenv\\Repository\\Adapter\\EnvConstAdapter' => $vendorDir . '/vlucas/phpdotenv/src/Repository/Adapter/EnvConstAdapter.php',
    'Dotenv\\Repository\\Adapter\\GuardedWriter' => $vendorDir . '/vlucas/phpdotenv/src/Repository/Adapter/GuardedWriter.php',
    'Dotenv\\Repository\\Adapter\\ImmutableWriter' => $vendorDir . '/vlucas/phpdotenv/src/Repository/Adapter/ImmutableWriter.php',
    'Dotenv\\Repository\\Adapter\\MultiReader' => $vendorDir . '/vlucas/phpdotenv/src/Repository/Adapter/MultiReader.php',
    'Dotenv\\Repository\\Adapter\\MultiWriter' => $vendorDir . '/vlucas/phpdotenv/src/Repository/Adapter/MultiWriter.php',
    'Dotenv\\Repository\\Adapter\\PutenvAdapter' => $vendorDir . '/vlucas/phpdotenv/src/Repository/Adapter/PutenvAdapter.php',
    'Dotenv\\Repository\\Adapter\\ReaderInterface' => $vendorDir . '/vlucas/phpdotenv/src/Repository/Adapter/ReaderInterface.php',
    'Dotenv\\Repository\\Adapter\\ReplacingWriter' => $vendorDir . '/vlucas/phpdotenv/src/Repository/Adapter/ReplacingWriter.php',
    'Dotenv\\Repository\\Adapter\\ServerConstAdapter' => $vendorDir . '/vlucas/phpdotenv/src/Repository/Adapter/ServerConstAdapter.php',
    'Dotenv\\Repository\\Adapter\\WriterInterface' => $vendorDir . '/vlucas/phpdotenv/src/Repository/Adapter/WriterInterface.php',
    'Dotenv\\Repository\\RepositoryBuilder' => $vendorDir . '/vlucas/phpdotenv/src/Repository/RepositoryBuilder.php',
    'Dotenv\\Repository\\RepositoryInterface' => $vendorDir . '/vlucas/phpdotenv/src/Repository/RepositoryInterface.php',
    'Dotenv\\Store\\FileStore' => $vendorDir . '/vlucas/phpdotenv/src/Store/FileStore.php',
    'Dotenv\\Store\\File\\Paths' => $vendorDir . '/vlucas/phpdotenv/src/Store/File/Paths.php',
    'Dotenv\\Store\\File\\Reader' => $vendorDir . '/vlucas/phpdotenv/src/Store/File/Reader.php',
    'Dotenv\\Store\\StoreBuilder' => $vendorDir . '/vlucas/phpdotenv/src/Store/StoreBuilder.php',
    'Dotenv\\Store\\StoreInterface' => $vendorDir . '/vlucas/phpdotenv/src/Store/StoreInterface.php',
    'Dotenv\\Store\\StringStore' => $vendorDir . '/vlucas/phpdotenv/src/Store/StringStore.php',
    'Dotenv\\Util\\Regex' => $vendorDir . '/vlucas/phpdotenv/src/Util/Regex.php',
    'Dotenv\\Util\\Str' => $vendorDir . '/vlucas/phpdotenv/src/Util/Str.php',
    'Dotenv\\Validator' => $vendorDir . '/vlucas/phpdotenv/src/Validator.php',
    'Egulias\\EmailValidator\\EmailLexer' => $vendorDir . '/egulias/email-validator/src/EmailLexer.php',
    'Egulias\\EmailValidator\\EmailParser' => $vendorDir . '/egulias/email-validator/src/EmailParser.php',
    'Egulias\\EmailValidator\\EmailValidator' => $vendorDir . '/egulias/email-validator/src/EmailValidator.php',
    'Egulias\\EmailValidator\\MessageIDParser' => $vendorDir . '/egulias/email-validator/src/MessageIDParser.php',
    'Egulias\\EmailValidator\\Parser' => $vendorDir . '/egulias/email-validator/src/Parser.php',
    'Egulias\\EmailValidator\\Parser\\Comment' => $vendorDir . '/egulias/email-validator/src/Parser/Comment.php',
    'Egulias\\EmailValidator\\Parser\\CommentStrategy\\CommentStrategy' => $vendorDir . '/egulias/email-validator/src/Parser/CommentStrategy/CommentStrategy.php',
    'Egulias\\EmailValidator\\Parser\\CommentStrategy\\DomainComment' => $vendorDir . '/egulias/email-validator/src/Parser/CommentStrategy/DomainComment.php',
    'Egulias\\EmailValidator\\Parser\\CommentStrategy\\LocalComment' => $vendorDir . '/egulias/email-validator/src/Parser/CommentStrategy/LocalComment.php',
    'Egulias\\EmailValidator\\Parser\\DomainLiteral' => $vendorDir . '/egulias/email-validator/src/Parser/DomainLiteral.php',
    'Egulias\\EmailValidator\\Parser\\DomainPart' => $vendorDir . '/egulias/email-validator/src/Parser/DomainPart.php',
    'Egulias\\EmailValidator\\Parser\\DoubleQuote' => $vendorDir . '/egulias/email-validator/src/Parser/DoubleQuote.php',
    'Egulias\\EmailValidator\\Parser\\FoldingWhiteSpace' => $vendorDir . '/egulias/email-validator/src/Parser/FoldingWhiteSpace.php',
    'Egulias\\EmailValidator\\Parser\\IDLeftPart' => $vendorDir . '/egulias/email-validator/src/Parser/IDLeftPart.php',
    'Egulias\\EmailValidator\\Parser\\IDRightPart' => $vendorDir . '/egulias/email-validator/src/Parser/IDRightPart.php',
    'Egulias\\EmailValidator\\Parser\\LocalPart' => $vendorDir . '/egulias/email-validator/src/Parser/LocalPart.php',
    'Egulias\\EmailValidator\\Parser\\PartParser' => $vendorDir . '/egulias/email-validator/src/Parser/PartParser.php',
    'Egulias\\EmailValidator\\Result\\InvalidEmail' => $vendorDir . '/egulias/email-validator/src/Result/InvalidEmail.php',
    'Egulias\\EmailValidator\\Result\\MultipleErrors' => $vendorDir . '/egulias/email-validator/src/Result/MultipleErrors.php',
    'Egulias\\EmailValidator\\Result\\Reason\\AtextAfterCFWS' => $vendorDir . '/egulias/email-validator/src/Result/Reason/AtextAfterCFWS.php',
    'Egulias\\EmailValidator\\Result\\Reason\\CRLFAtTheEnd' => $vendorDir . '/egulias/email-validator/src/Result/Reason/CRLFAtTheEnd.php',
    'Egulias\\EmailValidator\\Result\\Reason\\CRLFX2' => $vendorDir . '/egulias/email-validator/src/Result/Reason/CRLFX2.php',
    'Egulias\\EmailValidator\\Result\\Reason\\CRNoLF' => $vendorDir . '/egulias/email-validator/src/Result/Reason/CRNoLF.php',
    'Egulias\\EmailValidator\\Result\\Reason\\CharNotAllowed' => $vendorDir . '/egulias/email-validator/src/Result/Reason/CharNotAllowed.php',
    'Egulias\\EmailValidator\\Result\\Reason\\CommaInDomain' => $vendorDir . '/egulias/email-validator/src/Result/Reason/CommaInDomain.php',
    'Egulias\\EmailValidator\\Result\\Reason\\CommentsInIDRight' => $vendorDir . '/egulias/email-validator/src/Result/Reason/CommentsInIDRight.php',
    'Egulias\\EmailValidator\\Result\\Reason\\ConsecutiveAt' => $vendorDir . '/egulias/email-validator/src/Result/Reason/ConsecutiveAt.php',
    'Egulias\\EmailValidator\\Result\\Reason\\ConsecutiveDot' => $vendorDir . '/egulias/email-validator/src/Result/Reason/ConsecutiveDot.php',
    'Egulias\\EmailValidator\\Result\\Reason\\DetailedReason' => $vendorDir . '/egulias/email-validator/src/Result/Reason/DetailedReason.php',
    'Egulias\\EmailValidator\\Result\\Reason\\DomainAcceptsNoMail' => $vendorDir . '/egulias/email-validator/src/Result/Reason/DomainAcceptsNoMail.php',
    'Egulias\\EmailValidator\\Result\\Reason\\DomainHyphened' => $vendorDir . '/egulias/email-validator/src/Result/Reason/DomainHyphened.php',
    'Egulias\\EmailValidator\\Result\\Reason\\DomainTooLong' => $vendorDir . '/egulias/email-validator/src/Result/Reason/DomainTooLong.php',
    'Egulias\\EmailValidator\\Result\\Reason\\DotAtEnd' => $vendorDir . '/egulias/email-validator/src/Result/Reason/DotAtEnd.php',
    'Egulias\\EmailValidator\\Result\\Reason\\DotAtStart' => $vendorDir . '/egulias/email-validator/src/Result/Reason/DotAtStart.php',
    'Egulias\\EmailValidator\\Result\\Reason\\EmptyReason' => $vendorDir . '/egulias/email-validator/src/Result/Reason/EmptyReason.php',
    'Egulias\\EmailValidator\\Result\\Reason\\ExceptionFound' => $vendorDir . '/egulias/email-validator/src/Result/Reason/ExceptionFound.php',
    'Egulias\\EmailValidator\\Result\\Reason\\ExpectingATEXT' => $vendorDir . '/egulias/email-validator/src/Result/Reason/ExpectingATEXT.php',
    'Egulias\\EmailValidator\\Result\\Reason\\ExpectingCTEXT' => $vendorDir . '/egulias/email-validator/src/Result/Reason/ExpectingCTEXT.php',
    'Egulias\\EmailValidator\\Result\\Reason\\ExpectingDTEXT' => $vendorDir . '/egulias/email-validator/src/Result/Reason/ExpectingDTEXT.php',
    'Egulias\\EmailValidator\\Result\\Reason\\ExpectingDomainLiteralClose' => $vendorDir . '/egulias/email-validator/src/Result/Reason/ExpectingDomainLiteralClose.php',
    'Egulias\\EmailValidator\\Result\\Reason\\LabelTooLong' => $vendorDir . '/egulias/email-validator/src/Result/Reason/LabelTooLong.php',
    'Egulias\\EmailValidator\\Result\\Reason\\LocalOrReservedDomain' => $vendorDir . '/egulias/email-validator/src/Result/Reason/LocalOrReservedDomain.php',
    'Egulias\\EmailValidator\\Result\\Reason\\NoDNSRecord' => $vendorDir . '/egulias/email-validator/src/Result/Reason/NoDNSRecord.php',
    'Egulias\\EmailValidator\\Result\\Reason\\NoDomainPart' => $vendorDir . '/egulias/email-validator/src/Result/Reason/NoDomainPart.php',
    'Egulias\\EmailValidator\\Result\\Reason\\NoLocalPart' => $vendorDir . '/egulias/email-validator/src/Result/Reason/NoLocalPart.php',
    'Egulias\\EmailValidator\\Result\\Reason\\RFCWarnings' => $vendorDir . '/egulias/email-validator/src/Result/Reason/RFCWarnings.php',
    'Egulias\\EmailValidator\\Result\\Reason\\Reason' => $vendorDir . '/egulias/email-validator/src/Result/Reason/Reason.php',
    'Egulias\\EmailValidator\\Result\\Reason\\SpoofEmail' => $vendorDir . '/egulias/email-validator/src/Result/Reason/SpoofEmail.php',
    'Egulias\\EmailValidator\\Result\\Reason\\UnOpenedComment' => $vendorDir . '/egulias/email-validator/src/Result/Reason/UnOpenedComment.php',
    'Egulias\\EmailValidator\\Result\\Reason\\UnableToGetDNSRecord' => $vendorDir . '/egulias/email-validator/src/Result/Reason/UnableToGetDNSRecord.php',
    'Egulias\\EmailValidator\\Result\\Reason\\UnclosedComment' => $vendorDir . '/egulias/email-validator/src/Result/Reason/UnclosedComment.php',
    'Egulias\\EmailValidator\\Result\\Reason\\UnclosedQuotedString' => $vendorDir . '/egulias/email-validator/src/Result/Reason/UnclosedQuotedString.php',
    'Egulias\\EmailValidator\\Result\\Reason\\UnusualElements' => $vendorDir . '/egulias/email-validator/src/Result/Reason/UnusualElements.php',
    'Egulias\\EmailValidator\\Result\\Result' => $vendorDir . '/egulias/email-validator/src/Result/Result.php',
    'Egulias\\EmailValidator\\Result\\SpoofEmail' => $vendorDir . '/egulias/email-validator/src/Result/SpoofEmail.php',
    'Egulias\\EmailValidator\\Result\\ValidEmail' => $vendorDir . '/egulias/email-validator/src/Result/ValidEmail.php',
    'Egulias\\EmailValidator\\Validation\\DNSCheckValidation' => $vendorDir . '/egulias/email-validator/src/Validation/DNSCheckValidation.php',
    'Egulias\\EmailValidator\\Validation\\DNSGetRecordWrapper' => $vendorDir . '/egulias/email-validator/src/Validation/DNSGetRecordWrapper.php',
    'Egulias\\EmailValidator\\Validation\\DNSRecords' => $vendorDir . '/egulias/email-validator/src/Validation/DNSRecords.php',
    'Egulias\\EmailValidator\\Validation\\EmailValidation' => $vendorDir . '/egulias/email-validator/src/Validation/EmailValidation.php',
    'Egulias\\EmailValidator\\Validation\\Exception\\EmptyValidationList' => $vendorDir . '/egulias/email-validator/src/Validation/Exception/EmptyValidationList.php',
    'Egulias\\EmailValidator\\Validation\\Extra\\SpoofCheckValidation' => $vendorDir . '/egulias/email-validator/src/Validation/Extra/SpoofCheckValidation.php',
    'Egulias\\EmailValidator\\Validation\\MessageIDValidation' => $vendorDir . '/egulias/email-validator/src/Validation/MessageIDValidation.php',
    'Egulias\\EmailValidator\\Validation\\MultipleValidationWithAnd' => $vendorDir . '/egulias/email-validator/src/Validation/MultipleValidationWithAnd.php',
    'Egulias\\EmailValidator\\Validation\\NoRFCWarningsValidation' => $vendorDir . '/egulias/email-validator/src/Validation/NoRFCWarningsValidation.php',
    'Egulias\\EmailValidator\\Validation\\RFCValidation' => $vendorDir . '/egulias/email-validator/src/Validation/RFCValidation.php',
    'Egulias\\EmailValidator\\Warning\\AddressLiteral' => $vendorDir . '/egulias/email-validator/src/Warning/AddressLiteral.php',
    'Egulias\\EmailValidator\\Warning\\CFWSNearAt' => $vendorDir . '/egulias/email-validator/src/Warning/CFWSNearAt.php',
    'Egulias\\EmailValidator\\Warning\\CFWSWithFWS' => $vendorDir . '/egulias/email-validator/src/Warning/CFWSWithFWS.php',
    'Egulias\\EmailValidator\\Warning\\Comment' => $vendorDir . '/egulias/email-validator/src/Warning/Comment.php',
    'Egulias\\EmailValidator\\Warning\\DeprecatedComment' => $vendorDir . '/egulias/email-validator/src/Warning/DeprecatedComment.php',
    'Egulias\\EmailValidator\\Warning\\DomainLiteral' => $vendorDir . '/egulias/email-validator/src/Warning/DomainLiteral.php',
    'Egulias\\EmailValidator\\Warning\\EmailTooLong' => $vendorDir . '/egulias/email-validator/src/Warning/EmailTooLong.php',
    'Egulias\\EmailValidator\\Warning\\IPV6BadChar' => $vendorDir . '/egulias/email-validator/src/Warning/IPV6BadChar.php',
    'Egulias\\EmailValidator\\Warning\\IPV6ColonEnd' => $vendorDir . '/egulias/email-validator/src/Warning/IPV6ColonEnd.php',
    'Egulias\\EmailValidator\\Warning\\IPV6ColonStart' => $vendorDir . '/egulias/email-validator/src/Warning/IPV6ColonStart.php',
    'Egulias\\EmailValidator\\Warning\\IPV6Deprecated' => $vendorDir . '/egulias/email-validator/src/Warning/IPV6Deprecated.php',
    'Egulias\\EmailValidator\\Warning\\IPV6DoubleColon' => $vendorDir . '/egulias/email-validator/src/Warning/IPV6DoubleColon.php',
    'Egulias\\EmailValidator\\Warning\\IPV6GroupCount' => $vendorDir . '/egulias/email-validator/src/Warning/IPV6GroupCount.php',
    'Egulias\\EmailValidator\\Warning\\IPV6MaxGroups' => $vendorDir . '/egulias/email-validator/src/Warning/IPV6MaxGroups.php',
    'Egulias\\EmailValidator\\Warning\\LocalTooLong' => $vendorDir . '/egulias/email-validator/src/Warning/LocalTooLong.php',
    'Egulias\\EmailValidator\\Warning\\NoDNSMXRecord' => $vendorDir . '/egulias/email-validator/src/Warning/NoDNSMXRecord.php',
    'Egulias\\EmailValidator\\Warning\\ObsoleteDTEXT' => $vendorDir . '/egulias/email-validator/src/Warning/ObsoleteDTEXT.php',
    'Egulias\\EmailValidator\\Warning\\QuotedPart' => $vendorDir . '/egulias/email-validator/src/Warning/QuotedPart.php',
    'Egulias\\EmailValidator\\Warning\\QuotedString' => $vendorDir . '/egulias/email-validator/src/Warning/QuotedString.php',
    'Egulias\\EmailValidator\\Warning\\TLD' => $vendorDir . '/egulias/email-validator/src/Warning/TLD.php',
    'Egulias\\EmailValidator\\Warning\\Warning' => $vendorDir . '/egulias/email-validator/src/Warning/Warning.php',
    'EticSimple\\Core\\Application' => $baseDir . '/api/core/Application.php',
    'EticSimple\\Core\\Cache\\CacheManager' => $baseDir . '/api/core/Cache/CacheManager.php',
    'EticSimple\\Core\\Database\\DatabaseManager' => $baseDir . '/api/core/Database/DatabaseManager.php',
    'EticSimple\\Core\\Router\\Route' => $baseDir . '/api/core/Router/Route.php',
    'EticSimple\\Core\\Router\\Router' => $baseDir . '/api/core/Router/Router.php',
    'EticSimple\\Core\\Theme\\ThemeManager' => $baseDir . '/api/core/Theme/ThemeManager.php',
    'Evenement\\EventEmitter' => $vendorDir . '/evenement/evenement/src/EventEmitter.php',
    'Evenement\\EventEmitterInterface' => $vendorDir . '/evenement/evenement/src/EventEmitterInterface.php',
    'Evenement\\EventEmitterTrait' => $vendorDir . '/evenement/evenement/src/EventEmitterTrait.php',
    'Fidry\\CpuCoreCounter\\CpuCoreCounter' => $vendorDir . '/fidry/cpu-core-counter/src/CpuCoreCounter.php',
    'Fidry\\CpuCoreCounter\\Diagnoser' => $vendorDir . '/fidry/cpu-core-counter/src/Diagnoser.php',
    'Fidry\\CpuCoreCounter\\Executor\\ProcOpenExecutor' => $vendorDir . '/fidry/cpu-core-counter/src/Executor/ProcOpenExecutor.php',
    'Fidry\\CpuCoreCounter\\Executor\\ProcessExecutor' => $vendorDir . '/fidry/cpu-core-counter/src/Executor/ProcessExecutor.php',
    'Fidry\\CpuCoreCounter\\Finder\\CmiCmdletLogicalFinder' => $vendorDir . '/fidry/cpu-core-counter/src/Finder/CmiCmdletLogicalFinder.php',
    'Fidry\\CpuCoreCounter\\Finder\\CmiCmdletPhysicalFinder' => $vendorDir . '/fidry/cpu-core-counter/src/Finder/CmiCmdletPhysicalFinder.php',
    'Fidry\\CpuCoreCounter\\Finder\\CpuCoreFinder' => $vendorDir . '/fidry/cpu-core-counter/src/Finder/CpuCoreFinder.php',
    'Fidry\\CpuCoreCounter\\Finder\\CpuInfoFinder' => $vendorDir . '/fidry/cpu-core-counter/src/Finder/CpuInfoFinder.php',
    'Fidry\\CpuCoreCounter\\Finder\\DummyCpuCoreFinder' => $vendorDir . '/fidry/cpu-core-counter/src/Finder/DummyCpuCoreFinder.php',
    'Fidry\\CpuCoreCounter\\Finder\\EnvVariableFinder' => $vendorDir . '/fidry/cpu-core-counter/src/Finder/EnvVariableFinder.php',
    'Fidry\\CpuCoreCounter\\Finder\\FinderRegistry' => $vendorDir . '/fidry/cpu-core-counter/src/Finder/FinderRegistry.php',
    'Fidry\\CpuCoreCounter\\Finder\\HwLogicalFinder' => $vendorDir . '/fidry/cpu-core-counter/src/Finder/HwLogicalFinder.php',
    'Fidry\\CpuCoreCounter\\Finder\\HwPhysicalFinder' => $vendorDir . '/fidry/cpu-core-counter/src/Finder/HwPhysicalFinder.php',
    'Fidry\\CpuCoreCounter\\Finder\\LscpuLogicalFinder' => $vendorDir . '/fidry/cpu-core-counter/src/Finder/LscpuLogicalFinder.php',
    'Fidry\\CpuCoreCounter\\Finder\\LscpuPhysicalFinder' => $vendorDir . '/fidry/cpu-core-counter/src/Finder/LscpuPhysicalFinder.php',
    'Fidry\\CpuCoreCounter\\Finder\\NProcFinder' => $vendorDir . '/fidry/cpu-core-counter/src/Finder/NProcFinder.php',
    'Fidry\\CpuCoreCounter\\Finder\\NProcessorFinder' => $vendorDir . '/fidry/cpu-core-counter/src/Finder/NProcessorFinder.php',
    'Fidry\\CpuCoreCounter\\Finder\\NullCpuCoreFinder' => $vendorDir . '/fidry/cpu-core-counter/src/Finder/NullCpuCoreFinder.php',
    'Fidry\\CpuCoreCounter\\Finder\\OnlyInPowerShellFinder' => $vendorDir . '/fidry/cpu-core-counter/src/Finder/OnlyInPowerShellFinder.php',
    'Fidry\\CpuCoreCounter\\Finder\\OnlyOnOSFamilyFinder' => $vendorDir . '/fidry/cpu-core-counter/src/Finder/OnlyOnOSFamilyFinder.php',
    'Fidry\\CpuCoreCounter\\Finder\\ProcOpenBasedFinder' => $vendorDir . '/fidry/cpu-core-counter/src/Finder/ProcOpenBasedFinder.php',
    'Fidry\\CpuCoreCounter\\Finder\\SkipOnOSFamilyFinder' => $vendorDir . '/fidry/cpu-core-counter/src/Finder/SkipOnOSFamilyFinder.php',
    'Fidry\\CpuCoreCounter\\Finder\\WindowsRegistryLogicalFinder' => $vendorDir . '/fidry/cpu-core-counter/src/Finder/WindowsRegistryLogicalFinder.php',
    'Fidry\\CpuCoreCounter\\Finder\\WmicLogicalFinder' => $vendorDir . '/fidry/cpu-core-counter/src/Finder/WmicLogicalFinder.php',
    'Fidry\\CpuCoreCounter\\Finder\\WmicPhysicalFinder' => $vendorDir . '/fidry/cpu-core-counter/src/Finder/WmicPhysicalFinder.php',
    'Fidry\\CpuCoreCounter\\Finder\\_NProcessorFinder' => $vendorDir . '/fidry/cpu-core-counter/src/Finder/_NProcessorFinder.php',
    'Fidry\\CpuCoreCounter\\NumberOfCpuCoreNotFound' => $vendorDir . '/fidry/cpu-core-counter/src/NumberOfCpuCoreNotFound.php',
    'Fidry\\CpuCoreCounter\\ParallelisationResult' => $vendorDir . '/fidry/cpu-core-counter/src/ParallelisationResult.php',
    'Firebase\\JWT\\BeforeValidException' => $vendorDir . '/firebase/php-jwt/src/BeforeValidException.php',
    'Firebase\\JWT\\CachedKeySet' => $vendorDir . '/firebase/php-jwt/src/CachedKeySet.php',
    'Firebase\\JWT\\ExpiredException' => $vendorDir . '/firebase/php-jwt/src/ExpiredException.php',
    'Firebase\\JWT\\JWK' => $vendorDir . '/firebase/php-jwt/src/JWK.php',
    'Firebase\\JWT\\JWT' => $vendorDir . '/firebase/php-jwt/src/JWT.php',
    'Firebase\\JWT\\JWTExceptionWithPayloadInterface' => $vendorDir . '/firebase/php-jwt/src/JWTExceptionWithPayloadInterface.php',
    'Firebase\\JWT\\Key' => $vendorDir . '/firebase/php-jwt/src/Key.php',
    'Firebase\\JWT\\SignatureInvalidException' => $vendorDir . '/firebase/php-jwt/src/SignatureInvalidException.php',
    'GrahamCampbell\\ResultType\\Error' => $vendorDir . '/graham-campbell/result-type/src/Error.php',
    'GrahamCampbell\\ResultType\\Result' => $vendorDir . '/graham-campbell/result-type/src/Result.php',
    'GrahamCampbell\\ResultType\\Success' => $vendorDir . '/graham-campbell/result-type/src/Success.php',
    'Intervention\\Gif\\AbstractEntity' => $vendorDir . '/intervention/gif/src/AbstractEntity.php',
    'Intervention\\Gif\\AbstractExtension' => $vendorDir . '/intervention/gif/src/AbstractExtension.php',
    'Intervention\\Gif\\Blocks\\ApplicationExtension' => $vendorDir . '/intervention/gif/src/Blocks/ApplicationExtension.php',
    'Intervention\\Gif\\Blocks\\Color' => $vendorDir . '/intervention/gif/src/Blocks/Color.php',
    'Intervention\\Gif\\Blocks\\ColorTable' => $vendorDir . '/intervention/gif/src/Blocks/ColorTable.php',
    'Intervention\\Gif\\Blocks\\CommentExtension' => $vendorDir . '/intervention/gif/src/Blocks/CommentExtension.php',
    'Intervention\\Gif\\Blocks\\DataSubBlock' => $vendorDir . '/intervention/gif/src/Blocks/DataSubBlock.php',
    'Intervention\\Gif\\Blocks\\FrameBlock' => $vendorDir . '/intervention/gif/src/Blocks/FrameBlock.php',
    'Intervention\\Gif\\Blocks\\GraphicControlExtension' => $vendorDir . '/intervention/gif/src/Blocks/GraphicControlExtension.php',
    'Intervention\\Gif\\Blocks\\Header' => $vendorDir . '/intervention/gif/src/Blocks/Header.php',
    'Intervention\\Gif\\Blocks\\ImageData' => $vendorDir . '/intervention/gif/src/Blocks/ImageData.php',
    'Intervention\\Gif\\Blocks\\ImageDescriptor' => $vendorDir . '/intervention/gif/src/Blocks/ImageDescriptor.php',
    'Intervention\\Gif\\Blocks\\LogicalScreenDescriptor' => $vendorDir . '/intervention/gif/src/Blocks/LogicalScreenDescriptor.php',
    'Intervention\\Gif\\Blocks\\NetscapeApplicationExtension' => $vendorDir . '/intervention/gif/src/Blocks/NetscapeApplicationExtension.php',
    'Intervention\\Gif\\Blocks\\PlainTextExtension' => $vendorDir . '/intervention/gif/src/Blocks/PlainTextExtension.php',
    'Intervention\\Gif\\Blocks\\TableBasedImage' => $vendorDir . '/intervention/gif/src/Blocks/TableBasedImage.php',
    'Intervention\\Gif\\Blocks\\Trailer' => $vendorDir . '/intervention/gif/src/Blocks/Trailer.php',
    'Intervention\\Gif\\Builder' => $vendorDir . '/intervention/gif/src/Builder.php',
    'Intervention\\Gif\\Decoder' => $vendorDir . '/intervention/gif/src/Decoder.php',
    'Intervention\\Gif\\Decoders\\AbstractDecoder' => $vendorDir . '/intervention/gif/src/Decoders/AbstractDecoder.php',
    'Intervention\\Gif\\Decoders\\AbstractPackedBitDecoder' => $vendorDir . '/intervention/gif/src/Decoders/AbstractPackedBitDecoder.php',
    'Intervention\\Gif\\Decoders\\ApplicationExtensionDecoder' => $vendorDir . '/intervention/gif/src/Decoders/ApplicationExtensionDecoder.php',
    'Intervention\\Gif\\Decoders\\ColorDecoder' => $vendorDir . '/intervention/gif/src/Decoders/ColorDecoder.php',
    'Intervention\\Gif\\Decoders\\ColorTableDecoder' => $vendorDir . '/intervention/gif/src/Decoders/ColorTableDecoder.php',
    'Intervention\\Gif\\Decoders\\CommentExtensionDecoder' => $vendorDir . '/intervention/gif/src/Decoders/CommentExtensionDecoder.php',
    'Intervention\\Gif\\Decoders\\DataSubBlockDecoder' => $vendorDir . '/intervention/gif/src/Decoders/DataSubBlockDecoder.php',
    'Intervention\\Gif\\Decoders\\FrameBlockDecoder' => $vendorDir . '/intervention/gif/src/Decoders/FrameBlockDecoder.php',
    'Intervention\\Gif\\Decoders\\GifDataStreamDecoder' => $vendorDir . '/intervention/gif/src/Decoders/GifDataStreamDecoder.php',
    'Intervention\\Gif\\Decoders\\GraphicControlExtensionDecoder' => $vendorDir . '/intervention/gif/src/Decoders/GraphicControlExtensionDecoder.php',
    'Intervention\\Gif\\Decoders\\HeaderDecoder' => $vendorDir . '/intervention/gif/src/Decoders/HeaderDecoder.php',
    'Intervention\\Gif\\Decoders\\ImageDataDecoder' => $vendorDir . '/intervention/gif/src/Decoders/ImageDataDecoder.php',
    'Intervention\\Gif\\Decoders\\ImageDescriptorDecoder' => $vendorDir . '/intervention/gif/src/Decoders/ImageDescriptorDecoder.php',
    'Intervention\\Gif\\Decoders\\LogicalScreenDescriptorDecoder' => $vendorDir . '/intervention/gif/src/Decoders/LogicalScreenDescriptorDecoder.php',
    'Intervention\\Gif\\Decoders\\NetscapeApplicationExtensionDecoder' => $vendorDir . '/intervention/gif/src/Decoders/NetscapeApplicationExtensionDecoder.php',
    'Intervention\\Gif\\Decoders\\PlainTextExtensionDecoder' => $vendorDir . '/intervention/gif/src/Decoders/PlainTextExtensionDecoder.php',
    'Intervention\\Gif\\Decoders\\TableBasedImageDecoder' => $vendorDir . '/intervention/gif/src/Decoders/TableBasedImageDecoder.php',
    'Intervention\\Gif\\DisposalMethod' => $vendorDir . '/intervention/gif/src/DisposalMethod.php',
    'Intervention\\Gif\\Encoders\\AbstractEncoder' => $vendorDir . '/intervention/gif/src/Encoders/AbstractEncoder.php',
    'Intervention\\Gif\\Encoders\\ApplicationExtensionEncoder' => $vendorDir . '/intervention/gif/src/Encoders/ApplicationExtensionEncoder.php',
    'Intervention\\Gif\\Encoders\\ColorEncoder' => $vendorDir . '/intervention/gif/src/Encoders/ColorEncoder.php',
    'Intervention\\Gif\\Encoders\\ColorTableEncoder' => $vendorDir . '/intervention/gif/src/Encoders/ColorTableEncoder.php',
    'Intervention\\Gif\\Encoders\\CommentExtensionEncoder' => $vendorDir . '/intervention/gif/src/Encoders/CommentExtensionEncoder.php',
    'Intervention\\Gif\\Encoders\\DataSubBlockEncoder' => $vendorDir . '/intervention/gif/src/Encoders/DataSubBlockEncoder.php',
    'Intervention\\Gif\\Encoders\\FrameBlockEncoder' => $vendorDir . '/intervention/gif/src/Encoders/FrameBlockEncoder.php',
    'Intervention\\Gif\\Encoders\\GifDataStreamEncoder' => $vendorDir . '/intervention/gif/src/Encoders/GifDataStreamEncoder.php',
    'Intervention\\Gif\\Encoders\\GraphicControlExtensionEncoder' => $vendorDir . '/intervention/gif/src/Encoders/GraphicControlExtensionEncoder.php',
    'Intervention\\Gif\\Encoders\\HeaderEncoder' => $vendorDir . '/intervention/gif/src/Encoders/HeaderEncoder.php',
    'Intervention\\Gif\\Encoders\\ImageDataEncoder' => $vendorDir . '/intervention/gif/src/Encoders/ImageDataEncoder.php',
    'Intervention\\Gif\\Encoders\\ImageDescriptorEncoder' => $vendorDir . '/intervention/gif/src/Encoders/ImageDescriptorEncoder.php',
    'Intervention\\Gif\\Encoders\\LogicalScreenDescriptorEncoder' => $vendorDir . '/intervention/gif/src/Encoders/LogicalScreenDescriptorEncoder.php',
    'Intervention\\Gif\\Encoders\\NetscapeApplicationExtensionEncoder' => $vendorDir . '/intervention/gif/src/Encoders/NetscapeApplicationExtensionEncoder.php',
    'Intervention\\Gif\\Encoders\\PlainTextExtensionEncoder' => $vendorDir . '/intervention/gif/src/Encoders/PlainTextExtensionEncoder.php',
    'Intervention\\Gif\\Encoders\\TableBasedImageEncoder' => $vendorDir . '/intervention/gif/src/Encoders/TableBasedImageEncoder.php',
    'Intervention\\Gif\\Encoders\\TrailerEncoder' => $vendorDir . '/intervention/gif/src/Encoders/TrailerEncoder.php',
    'Intervention\\Gif\\Exceptions\\DecoderException' => $vendorDir . '/intervention/gif/src/Exceptions/DecoderException.php',
    'Intervention\\Gif\\Exceptions\\EncoderException' => $vendorDir . '/intervention/gif/src/Exceptions/EncoderException.php',
    'Intervention\\Gif\\Exceptions\\FormatException' => $vendorDir . '/intervention/gif/src/Exceptions/FormatException.php',
    'Intervention\\Gif\\Exceptions\\NotReadableException' => $vendorDir . '/intervention/gif/src/Exceptions/NotReadableException.php',
    'Intervention\\Gif\\GifDataStream' => $vendorDir . '/intervention/gif/src/GifDataStream.php',
    'Intervention\\Gif\\Splitter' => $vendorDir . '/intervention/gif/src/Splitter.php',
    'Intervention\\Gif\\Traits\\CanDecode' => $vendorDir . '/intervention/gif/src/Traits/CanDecode.php',
    'Intervention\\Gif\\Traits\\CanEncode' => $vendorDir . '/intervention/gif/src/Traits/CanEncode.php',
    'Intervention\\Gif\\Traits\\CanHandleFiles' => $vendorDir . '/intervention/gif/src/Traits/CanHandleFiles.php',
    'Intervention\\Image\\Analyzers\\ColorspaceAnalyzer' => $vendorDir . '/intervention/image/src/Analyzers/ColorspaceAnalyzer.php',
    'Intervention\\Image\\Analyzers\\HeightAnalyzer' => $vendorDir . '/intervention/image/src/Analyzers/HeightAnalyzer.php',
    'Intervention\\Image\\Analyzers\\PixelColorAnalyzer' => $vendorDir . '/intervention/image/src/Analyzers/PixelColorAnalyzer.php',
    'Intervention\\Image\\Analyzers\\PixelColorsAnalyzer' => $vendorDir . '/intervention/image/src/Analyzers/PixelColorsAnalyzer.php',
    'Intervention\\Image\\Analyzers\\ProfileAnalyzer' => $vendorDir . '/intervention/image/src/Analyzers/ProfileAnalyzer.php',
    'Intervention\\Image\\Analyzers\\ResolutionAnalyzer' => $vendorDir . '/intervention/image/src/Analyzers/ResolutionAnalyzer.php',
    'Intervention\\Image\\Analyzers\\WidthAnalyzer' => $vendorDir . '/intervention/image/src/Analyzers/WidthAnalyzer.php',
    'Intervention\\Image\\Collection' => $vendorDir . '/intervention/image/src/Collection.php',
    'Intervention\\Image\\Colors\\AbstractColor' => $vendorDir . '/intervention/image/src/Colors/AbstractColor.php',
    'Intervention\\Image\\Colors\\AbstractColorChannel' => $vendorDir . '/intervention/image/src/Colors/AbstractColorChannel.php',
    'Intervention\\Image\\Colors\\Cmyk\\Channels\\Cyan' => $vendorDir . '/intervention/image/src/Colors/Cmyk/Channels/Cyan.php',
    'Intervention\\Image\\Colors\\Cmyk\\Channels\\Key' => $vendorDir . '/intervention/image/src/Colors/Cmyk/Channels/Key.php',
    'Intervention\\Image\\Colors\\Cmyk\\Channels\\Magenta' => $vendorDir . '/intervention/image/src/Colors/Cmyk/Channels/Magenta.php',
    'Intervention\\Image\\Colors\\Cmyk\\Channels\\Yellow' => $vendorDir . '/intervention/image/src/Colors/Cmyk/Channels/Yellow.php',
    'Intervention\\Image\\Colors\\Cmyk\\Color' => $vendorDir . '/intervention/image/src/Colors/Cmyk/Color.php',
    'Intervention\\Image\\Colors\\Cmyk\\Colorspace' => $vendorDir . '/intervention/image/src/Colors/Cmyk/Colorspace.php',
    'Intervention\\Image\\Colors\\Cmyk\\Decoders\\StringColorDecoder' => $vendorDir . '/intervention/image/src/Colors/Cmyk/Decoders/StringColorDecoder.php',
    'Intervention\\Image\\Colors\\Hsl\\Channels\\Hue' => $vendorDir . '/intervention/image/src/Colors/Hsl/Channels/Hue.php',
    'Intervention\\Image\\Colors\\Hsl\\Channels\\Luminance' => $vendorDir . '/intervention/image/src/Colors/Hsl/Channels/Luminance.php',
    'Intervention\\Image\\Colors\\Hsl\\Channels\\Saturation' => $vendorDir . '/intervention/image/src/Colors/Hsl/Channels/Saturation.php',
    'Intervention\\Image\\Colors\\Hsl\\Color' => $vendorDir . '/intervention/image/src/Colors/Hsl/Color.php',
    'Intervention\\Image\\Colors\\Hsl\\Colorspace' => $vendorDir . '/intervention/image/src/Colors/Hsl/Colorspace.php',
    'Intervention\\Image\\Colors\\Hsl\\Decoders\\StringColorDecoder' => $vendorDir . '/intervention/image/src/Colors/Hsl/Decoders/StringColorDecoder.php',
    'Intervention\\Image\\Colors\\Hsv\\Channels\\Hue' => $vendorDir . '/intervention/image/src/Colors/Hsv/Channels/Hue.php',
    'Intervention\\Image\\Colors\\Hsv\\Channels\\Saturation' => $vendorDir . '/intervention/image/src/Colors/Hsv/Channels/Saturation.php',
    'Intervention\\Image\\Colors\\Hsv\\Channels\\Value' => $vendorDir . '/intervention/image/src/Colors/Hsv/Channels/Value.php',
    'Intervention\\Image\\Colors\\Hsv\\Color' => $vendorDir . '/intervention/image/src/Colors/Hsv/Color.php',
    'Intervention\\Image\\Colors\\Hsv\\Colorspace' => $vendorDir . '/intervention/image/src/Colors/Hsv/Colorspace.php',
    'Intervention\\Image\\Colors\\Hsv\\Decoders\\StringColorDecoder' => $vendorDir . '/intervention/image/src/Colors/Hsv/Decoders/StringColorDecoder.php',
    'Intervention\\Image\\Colors\\Profile' => $vendorDir . '/intervention/image/src/Colors/Profile.php',
    'Intervention\\Image\\Colors\\Rgb\\Channels\\Alpha' => $vendorDir . '/intervention/image/src/Colors/Rgb/Channels/Alpha.php',
    'Intervention\\Image\\Colors\\Rgb\\Channels\\Blue' => $vendorDir . '/intervention/image/src/Colors/Rgb/Channels/Blue.php',
    'Intervention\\Image\\Colors\\Rgb\\Channels\\Green' => $vendorDir . '/intervention/image/src/Colors/Rgb/Channels/Green.php',
    'Intervention\\Image\\Colors\\Rgb\\Channels\\Red' => $vendorDir . '/intervention/image/src/Colors/Rgb/Channels/Red.php',
    'Intervention\\Image\\Colors\\Rgb\\Color' => $vendorDir . '/intervention/image/src/Colors/Rgb/Color.php',
    'Intervention\\Image\\Colors\\Rgb\\Colorspace' => $vendorDir . '/intervention/image/src/Colors/Rgb/Colorspace.php',
    'Intervention\\Image\\Colors\\Rgb\\Decoders\\HexColorDecoder' => $vendorDir . '/intervention/image/src/Colors/Rgb/Decoders/HexColorDecoder.php',
    'Intervention\\Image\\Colors\\Rgb\\Decoders\\HtmlColornameDecoder' => $vendorDir . '/intervention/image/src/Colors/Rgb/Decoders/HtmlColornameDecoder.php',
    'Intervention\\Image\\Colors\\Rgb\\Decoders\\StringColorDecoder' => $vendorDir . '/intervention/image/src/Colors/Rgb/Decoders/StringColorDecoder.php',
    'Intervention\\Image\\Colors\\Rgb\\Decoders\\TransparentColorDecoder' => $vendorDir . '/intervention/image/src/Colors/Rgb/Decoders/TransparentColorDecoder.php',
    'Intervention\\Image\\Config' => $vendorDir . '/intervention/image/src/Config.php',
    'Intervention\\Image\\Decoders\\Base64ImageDecoder' => $vendorDir . '/intervention/image/src/Decoders/Base64ImageDecoder.php',
    'Intervention\\Image\\Decoders\\BinaryImageDecoder' => $vendorDir . '/intervention/image/src/Decoders/BinaryImageDecoder.php',
    'Intervention\\Image\\Decoders\\ColorObjectDecoder' => $vendorDir . '/intervention/image/src/Decoders/ColorObjectDecoder.php',
    'Intervention\\Image\\Decoders\\DataUriImageDecoder' => $vendorDir . '/intervention/image/src/Decoders/DataUriImageDecoder.php',
    'Intervention\\Image\\Decoders\\EncodedImageObjectDecoder' => $vendorDir . '/intervention/image/src/Decoders/EncodedImageObjectDecoder.php',
    'Intervention\\Image\\Decoders\\FilePathImageDecoder' => $vendorDir . '/intervention/image/src/Decoders/FilePathImageDecoder.php',
    'Intervention\\Image\\Decoders\\FilePointerImageDecoder' => $vendorDir . '/intervention/image/src/Decoders/FilePointerImageDecoder.php',
    'Intervention\\Image\\Decoders\\ImageObjectDecoder' => $vendorDir . '/intervention/image/src/Decoders/ImageObjectDecoder.php',
    'Intervention\\Image\\Decoders\\NativeObjectDecoder' => $vendorDir . '/intervention/image/src/Decoders/NativeObjectDecoder.php',
    'Intervention\\Image\\Decoders\\SplFileInfoImageDecoder' => $vendorDir . '/intervention/image/src/Decoders/SplFileInfoImageDecoder.php',
    'Intervention\\Image\\Drivers\\AbstractDecoder' => $vendorDir . '/intervention/image/src/Drivers/AbstractDecoder.php',
    'Intervention\\Image\\Drivers\\AbstractDriver' => $vendorDir . '/intervention/image/src/Drivers/AbstractDriver.php',
    'Intervention\\Image\\Drivers\\AbstractEncoder' => $vendorDir . '/intervention/image/src/Drivers/AbstractEncoder.php',
    'Intervention\\Image\\Drivers\\AbstractFontProcessor' => $vendorDir . '/intervention/image/src/Drivers/AbstractFontProcessor.php',
    'Intervention\\Image\\Drivers\\AbstractFrame' => $vendorDir . '/intervention/image/src/Drivers/AbstractFrame.php',
    'Intervention\\Image\\Drivers\\Gd\\Analyzers\\ColorspaceAnalyzer' => $vendorDir . '/intervention/image/src/Drivers/Gd/Analyzers/ColorspaceAnalyzer.php',
    'Intervention\\Image\\Drivers\\Gd\\Analyzers\\HeightAnalyzer' => $vendorDir . '/intervention/image/src/Drivers/Gd/Analyzers/HeightAnalyzer.php',
    'Intervention\\Image\\Drivers\\Gd\\Analyzers\\PixelColorAnalyzer' => $vendorDir . '/intervention/image/src/Drivers/Gd/Analyzers/PixelColorAnalyzer.php',
    'Intervention\\Image\\Drivers\\Gd\\Analyzers\\PixelColorsAnalyzer' => $vendorDir . '/intervention/image/src/Drivers/Gd/Analyzers/PixelColorsAnalyzer.php',
    'Intervention\\Image\\Drivers\\Gd\\Analyzers\\ResolutionAnalyzer' => $vendorDir . '/intervention/image/src/Drivers/Gd/Analyzers/ResolutionAnalyzer.php',
    'Intervention\\Image\\Drivers\\Gd\\Analyzers\\WidthAnalyzer' => $vendorDir . '/intervention/image/src/Drivers/Gd/Analyzers/WidthAnalyzer.php',
    'Intervention\\Image\\Drivers\\Gd\\Cloner' => $vendorDir . '/intervention/image/src/Drivers/Gd/Cloner.php',
    'Intervention\\Image\\Drivers\\Gd\\ColorProcessor' => $vendorDir . '/intervention/image/src/Drivers/Gd/ColorProcessor.php',
    'Intervention\\Image\\Drivers\\Gd\\Core' => $vendorDir . '/intervention/image/src/Drivers/Gd/Core.php',
    'Intervention\\Image\\Drivers\\Gd\\Decoders\\AbstractDecoder' => $vendorDir . '/intervention/image/src/Drivers/Gd/Decoders/AbstractDecoder.php',
    'Intervention\\Image\\Drivers\\Gd\\Decoders\\Base64ImageDecoder' => $vendorDir . '/intervention/image/src/Drivers/Gd/Decoders/Base64ImageDecoder.php',
    'Intervention\\Image\\Drivers\\Gd\\Decoders\\BinaryImageDecoder' => $vendorDir . '/intervention/image/src/Drivers/Gd/Decoders/BinaryImageDecoder.php',
    'Intervention\\Image\\Drivers\\Gd\\Decoders\\DataUriImageDecoder' => $vendorDir . '/intervention/image/src/Drivers/Gd/Decoders/DataUriImageDecoder.php',
    'Intervention\\Image\\Drivers\\Gd\\Decoders\\EncodedImageObjectDecoder' => $vendorDir . '/intervention/image/src/Drivers/Gd/Decoders/EncodedImageObjectDecoder.php',
    'Intervention\\Image\\Drivers\\Gd\\Decoders\\FilePathImageDecoder' => $vendorDir . '/intervention/image/src/Drivers/Gd/Decoders/FilePathImageDecoder.php',
    'Intervention\\Image\\Drivers\\Gd\\Decoders\\FilePointerImageDecoder' => $vendorDir . '/intervention/image/src/Drivers/Gd/Decoders/FilePointerImageDecoder.php',
    'Intervention\\Image\\Drivers\\Gd\\Decoders\\NativeObjectDecoder' => $vendorDir . '/intervention/image/src/Drivers/Gd/Decoders/NativeObjectDecoder.php',
    'Intervention\\Image\\Drivers\\Gd\\Decoders\\SplFileInfoImageDecoder' => $vendorDir . '/intervention/image/src/Drivers/Gd/Decoders/SplFileInfoImageDecoder.php',
    'Intervention\\Image\\Drivers\\Gd\\Driver' => $vendorDir . '/intervention/image/src/Drivers/Gd/Driver.php',
    'Intervention\\Image\\Drivers\\Gd\\Encoders\\AvifEncoder' => $vendorDir . '/intervention/image/src/Drivers/Gd/Encoders/AvifEncoder.php',
    'Intervention\\Image\\Drivers\\Gd\\Encoders\\BmpEncoder' => $vendorDir . '/intervention/image/src/Drivers/Gd/Encoders/BmpEncoder.php',
    'Intervention\\Image\\Drivers\\Gd\\Encoders\\GifEncoder' => $vendorDir . '/intervention/image/src/Drivers/Gd/Encoders/GifEncoder.php',
    'Intervention\\Image\\Drivers\\Gd\\Encoders\\JpegEncoder' => $vendorDir . '/intervention/image/src/Drivers/Gd/Encoders/JpegEncoder.php',
    'Intervention\\Image\\Drivers\\Gd\\Encoders\\PngEncoder' => $vendorDir . '/intervention/image/src/Drivers/Gd/Encoders/PngEncoder.php',
    'Intervention\\Image\\Drivers\\Gd\\Encoders\\WebpEncoder' => $vendorDir . '/intervention/image/src/Drivers/Gd/Encoders/WebpEncoder.php',
    'Intervention\\Image\\Drivers\\Gd\\FontProcessor' => $vendorDir . '/intervention/image/src/Drivers/Gd/FontProcessor.php',
    'Intervention\\Image\\Drivers\\Gd\\Frame' => $vendorDir . '/intervention/image/src/Drivers/Gd/Frame.php',
    'Intervention\\Image\\Drivers\\Gd\\Modifiers\\AlignRotationModifier' => $vendorDir . '/intervention/image/src/Drivers/Gd/Modifiers/AlignRotationModifier.php',
    'Intervention\\Image\\Drivers\\Gd\\Modifiers\\BlendTransparencyModifier' => $vendorDir . '/intervention/image/src/Drivers/Gd/Modifiers/BlendTransparencyModifier.php',
    'Intervention\\Image\\Drivers\\Gd\\Modifiers\\BlurModifier' => $vendorDir . '/intervention/image/src/Drivers/Gd/Modifiers/BlurModifier.php',
    'Intervention\\Image\\Drivers\\Gd\\Modifiers\\BrightnessModifier' => $vendorDir . '/intervention/image/src/Drivers/Gd/Modifiers/BrightnessModifier.php',
    'Intervention\\Image\\Drivers\\Gd\\Modifiers\\ColorizeModifier' => $vendorDir . '/intervention/image/src/Drivers/Gd/Modifiers/ColorizeModifier.php',
    'Intervention\\Image\\Drivers\\Gd\\Modifiers\\ColorspaceModifier' => $vendorDir . '/intervention/image/src/Drivers/Gd/Modifiers/ColorspaceModifier.php',
    'Intervention\\Image\\Drivers\\Gd\\Modifiers\\ContainModifier' => $vendorDir . '/intervention/image/src/Drivers/Gd/Modifiers/ContainModifier.php',
    'Intervention\\Image\\Drivers\\Gd\\Modifiers\\ContrastModifier' => $vendorDir . '/intervention/image/src/Drivers/Gd/Modifiers/ContrastModifier.php',
    'Intervention\\Image\\Drivers\\Gd\\Modifiers\\CoverDownModifier' => $vendorDir . '/intervention/image/src/Drivers/Gd/Modifiers/CoverDownModifier.php',
    'Intervention\\Image\\Drivers\\Gd\\Modifiers\\CoverModifier' => $vendorDir . '/intervention/image/src/Drivers/Gd/Modifiers/CoverModifier.php',
    'Intervention\\Image\\Drivers\\Gd\\Modifiers\\CropModifier' => $vendorDir . '/intervention/image/src/Drivers/Gd/Modifiers/CropModifier.php',
    'Intervention\\Image\\Drivers\\Gd\\Modifiers\\DrawBezierModifier' => $vendorDir . '/intervention/image/src/Drivers/Gd/Modifiers/DrawBezierModifier.php',
    'Intervention\\Image\\Drivers\\Gd\\Modifiers\\DrawEllipseModifier' => $vendorDir . '/intervention/image/src/Drivers/Gd/Modifiers/DrawEllipseModifier.php',
    'Intervention\\Image\\Drivers\\Gd\\Modifiers\\DrawLineModifier' => $vendorDir . '/intervention/image/src/Drivers/Gd/Modifiers/DrawLineModifier.php',
    'Intervention\\Image\\Drivers\\Gd\\Modifiers\\DrawPixelModifier' => $vendorDir . '/intervention/image/src/Drivers/Gd/Modifiers/DrawPixelModifier.php',
    'Intervention\\Image\\Drivers\\Gd\\Modifiers\\DrawPolygonModifier' => $vendorDir . '/intervention/image/src/Drivers/Gd/Modifiers/DrawPolygonModifier.php',
    'Intervention\\Image\\Drivers\\Gd\\Modifiers\\DrawRectangleModifier' => $vendorDir . '/intervention/image/src/Drivers/Gd/Modifiers/DrawRectangleModifier.php',
    'Intervention\\Image\\Drivers\\Gd\\Modifiers\\FillModifier' => $vendorDir . '/intervention/image/src/Drivers/Gd/Modifiers/FillModifier.php',
    'Intervention\\Image\\Drivers\\Gd\\Modifiers\\FlipModifier' => $vendorDir . '/intervention/image/src/Drivers/Gd/Modifiers/FlipModifier.php',
    'Intervention\\Image\\Drivers\\Gd\\Modifiers\\FlopModifier' => $vendorDir . '/intervention/image/src/Drivers/Gd/Modifiers/FlopModifier.php',
    'Intervention\\Image\\Drivers\\Gd\\Modifiers\\GammaModifier' => $vendorDir . '/intervention/image/src/Drivers/Gd/Modifiers/GammaModifier.php',
    'Intervention\\Image\\Drivers\\Gd\\Modifiers\\GreyscaleModifier' => $vendorDir . '/intervention/image/src/Drivers/Gd/Modifiers/GreyscaleModifier.php',
    'Intervention\\Image\\Drivers\\Gd\\Modifiers\\InvertModifier' => $vendorDir . '/intervention/image/src/Drivers/Gd/Modifiers/InvertModifier.php',
    'Intervention\\Image\\Drivers\\Gd\\Modifiers\\PadModifier' => $vendorDir . '/intervention/image/src/Drivers/Gd/Modifiers/PadModifier.php',
    'Intervention\\Image\\Drivers\\Gd\\Modifiers\\PixelateModifier' => $vendorDir . '/intervention/image/src/Drivers/Gd/Modifiers/PixelateModifier.php',
    'Intervention\\Image\\Drivers\\Gd\\Modifiers\\PlaceModifier' => $vendorDir . '/intervention/image/src/Drivers/Gd/Modifiers/PlaceModifier.php',
    'Intervention\\Image\\Drivers\\Gd\\Modifiers\\ProfileModifier' => $vendorDir . '/intervention/image/src/Drivers/Gd/Modifiers/ProfileModifier.php',
    'Intervention\\Image\\Drivers\\Gd\\Modifiers\\ProfileRemovalModifier' => $vendorDir . '/intervention/image/src/Drivers/Gd/Modifiers/ProfileRemovalModifier.php',
    'Intervention\\Image\\Drivers\\Gd\\Modifiers\\QuantizeColorsModifier' => $vendorDir . '/intervention/image/src/Drivers/Gd/Modifiers/QuantizeColorsModifier.php',
    'Intervention\\Image\\Drivers\\Gd\\Modifiers\\RemoveAnimationModifier' => $vendorDir . '/intervention/image/src/Drivers/Gd/Modifiers/RemoveAnimationModifier.php',
    'Intervention\\Image\\Drivers\\Gd\\Modifiers\\ResizeCanvasModifier' => $vendorDir . '/intervention/image/src/Drivers/Gd/Modifiers/ResizeCanvasModifier.php',
    'Intervention\\Image\\Drivers\\Gd\\Modifiers\\ResizeCanvasRelativeModifier' => $vendorDir . '/intervention/image/src/Drivers/Gd/Modifiers/ResizeCanvasRelativeModifier.php',
    'Intervention\\Image\\Drivers\\Gd\\Modifiers\\ResizeDownModifier' => $vendorDir . '/intervention/image/src/Drivers/Gd/Modifiers/ResizeDownModifier.php',
    'Intervention\\Image\\Drivers\\Gd\\Modifiers\\ResizeModifier' => $vendorDir . '/intervention/image/src/Drivers/Gd/Modifiers/ResizeModifier.php',
    'Intervention\\Image\\Drivers\\Gd\\Modifiers\\ResolutionModifier' => $vendorDir . '/intervention/image/src/Drivers/Gd/Modifiers/ResolutionModifier.php',
    'Intervention\\Image\\Drivers\\Gd\\Modifiers\\RotateModifier' => $vendorDir . '/intervention/image/src/Drivers/Gd/Modifiers/RotateModifier.php',
    'Intervention\\Image\\Drivers\\Gd\\Modifiers\\ScaleDownModifier' => $vendorDir . '/intervention/image/src/Drivers/Gd/Modifiers/ScaleDownModifier.php',
    'Intervention\\Image\\Drivers\\Gd\\Modifiers\\ScaleModifier' => $vendorDir . '/intervention/image/src/Drivers/Gd/Modifiers/ScaleModifier.php',
    'Intervention\\Image\\Drivers\\Gd\\Modifiers\\SharpenModifier' => $vendorDir . '/intervention/image/src/Drivers/Gd/Modifiers/SharpenModifier.php',
    'Intervention\\Image\\Drivers\\Gd\\Modifiers\\SliceAnimationModifier' => $vendorDir . '/intervention/image/src/Drivers/Gd/Modifiers/SliceAnimationModifier.php',
    'Intervention\\Image\\Drivers\\Gd\\Modifiers\\TextModifier' => $vendorDir . '/intervention/image/src/Drivers/Gd/Modifiers/TextModifier.php',
    'Intervention\\Image\\Drivers\\Gd\\Modifiers\\TrimModifier' => $vendorDir . '/intervention/image/src/Drivers/Gd/Modifiers/TrimModifier.php',
    'Intervention\\Image\\Drivers\\Imagick\\Analyzers\\ColorspaceAnalyzer' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Analyzers/ColorspaceAnalyzer.php',
    'Intervention\\Image\\Drivers\\Imagick\\Analyzers\\HeightAnalyzer' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Analyzers/HeightAnalyzer.php',
    'Intervention\\Image\\Drivers\\Imagick\\Analyzers\\PixelColorAnalyzer' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Analyzers/PixelColorAnalyzer.php',
    'Intervention\\Image\\Drivers\\Imagick\\Analyzers\\PixelColorsAnalyzer' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Analyzers/PixelColorsAnalyzer.php',
    'Intervention\\Image\\Drivers\\Imagick\\Analyzers\\ProfileAnalyzer' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Analyzers/ProfileAnalyzer.php',
    'Intervention\\Image\\Drivers\\Imagick\\Analyzers\\ResolutionAnalyzer' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Analyzers/ResolutionAnalyzer.php',
    'Intervention\\Image\\Drivers\\Imagick\\Analyzers\\WidthAnalyzer' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Analyzers/WidthAnalyzer.php',
    'Intervention\\Image\\Drivers\\Imagick\\ColorProcessor' => $vendorDir . '/intervention/image/src/Drivers/Imagick/ColorProcessor.php',
    'Intervention\\Image\\Drivers\\Imagick\\Core' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Core.php',
    'Intervention\\Image\\Drivers\\Imagick\\Decoders\\Base64ImageDecoder' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Decoders/Base64ImageDecoder.php',
    'Intervention\\Image\\Drivers\\Imagick\\Decoders\\BinaryImageDecoder' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Decoders/BinaryImageDecoder.php',
    'Intervention\\Image\\Drivers\\Imagick\\Decoders\\DataUriImageDecoder' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Decoders/DataUriImageDecoder.php',
    'Intervention\\Image\\Drivers\\Imagick\\Decoders\\EncodedImageObjectDecoder' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Decoders/EncodedImageObjectDecoder.php',
    'Intervention\\Image\\Drivers\\Imagick\\Decoders\\FilePathImageDecoder' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Decoders/FilePathImageDecoder.php',
    'Intervention\\Image\\Drivers\\Imagick\\Decoders\\FilePointerImageDecoder' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Decoders/FilePointerImageDecoder.php',
    'Intervention\\Image\\Drivers\\Imagick\\Decoders\\NativeObjectDecoder' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Decoders/NativeObjectDecoder.php',
    'Intervention\\Image\\Drivers\\Imagick\\Decoders\\SplFileInfoImageDecoder' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Decoders/SplFileInfoImageDecoder.php',
    'Intervention\\Image\\Drivers\\Imagick\\Driver' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Driver.php',
    'Intervention\\Image\\Drivers\\Imagick\\Encoders\\AvifEncoder' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Encoders/AvifEncoder.php',
    'Intervention\\Image\\Drivers\\Imagick\\Encoders\\BmpEncoder' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Encoders/BmpEncoder.php',
    'Intervention\\Image\\Drivers\\Imagick\\Encoders\\GifEncoder' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Encoders/GifEncoder.php',
    'Intervention\\Image\\Drivers\\Imagick\\Encoders\\HeicEncoder' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Encoders/HeicEncoder.php',
    'Intervention\\Image\\Drivers\\Imagick\\Encoders\\Jpeg2000Encoder' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Encoders/Jpeg2000Encoder.php',
    'Intervention\\Image\\Drivers\\Imagick\\Encoders\\JpegEncoder' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Encoders/JpegEncoder.php',
    'Intervention\\Image\\Drivers\\Imagick\\Encoders\\PngEncoder' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Encoders/PngEncoder.php',
    'Intervention\\Image\\Drivers\\Imagick\\Encoders\\TiffEncoder' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Encoders/TiffEncoder.php',
    'Intervention\\Image\\Drivers\\Imagick\\Encoders\\WebpEncoder' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Encoders/WebpEncoder.php',
    'Intervention\\Image\\Drivers\\Imagick\\FontProcessor' => $vendorDir . '/intervention/image/src/Drivers/Imagick/FontProcessor.php',
    'Intervention\\Image\\Drivers\\Imagick\\Frame' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Frame.php',
    'Intervention\\Image\\Drivers\\Imagick\\Modifiers\\AlignRotationModifier' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Modifiers/AlignRotationModifier.php',
    'Intervention\\Image\\Drivers\\Imagick\\Modifiers\\BlendTransparencyModifier' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Modifiers/BlendTransparencyModifier.php',
    'Intervention\\Image\\Drivers\\Imagick\\Modifiers\\BlurModifier' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Modifiers/BlurModifier.php',
    'Intervention\\Image\\Drivers\\Imagick\\Modifiers\\BrightnessModifier' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Modifiers/BrightnessModifier.php',
    'Intervention\\Image\\Drivers\\Imagick\\Modifiers\\ColorizeModifier' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Modifiers/ColorizeModifier.php',
    'Intervention\\Image\\Drivers\\Imagick\\Modifiers\\ColorspaceModifier' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Modifiers/ColorspaceModifier.php',
    'Intervention\\Image\\Drivers\\Imagick\\Modifiers\\ContainModifier' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Modifiers/ContainModifier.php',
    'Intervention\\Image\\Drivers\\Imagick\\Modifiers\\ContrastModifier' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Modifiers/ContrastModifier.php',
    'Intervention\\Image\\Drivers\\Imagick\\Modifiers\\CoverDownModifier' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Modifiers/CoverDownModifier.php',
    'Intervention\\Image\\Drivers\\Imagick\\Modifiers\\CoverModifier' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Modifiers/CoverModifier.php',
    'Intervention\\Image\\Drivers\\Imagick\\Modifiers\\CropModifier' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Modifiers/CropModifier.php',
    'Intervention\\Image\\Drivers\\Imagick\\Modifiers\\DrawBezierModifier' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Modifiers/DrawBezierModifier.php',
    'Intervention\\Image\\Drivers\\Imagick\\Modifiers\\DrawEllipseModifier' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Modifiers/DrawEllipseModifier.php',
    'Intervention\\Image\\Drivers\\Imagick\\Modifiers\\DrawLineModifier' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Modifiers/DrawLineModifier.php',
    'Intervention\\Image\\Drivers\\Imagick\\Modifiers\\DrawPixelModifier' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Modifiers/DrawPixelModifier.php',
    'Intervention\\Image\\Drivers\\Imagick\\Modifiers\\DrawPolygonModifier' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Modifiers/DrawPolygonModifier.php',
    'Intervention\\Image\\Drivers\\Imagick\\Modifiers\\DrawRectangleModifier' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Modifiers/DrawRectangleModifier.php',
    'Intervention\\Image\\Drivers\\Imagick\\Modifiers\\FillModifier' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Modifiers/FillModifier.php',
    'Intervention\\Image\\Drivers\\Imagick\\Modifiers\\FlipModifier' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Modifiers/FlipModifier.php',
    'Intervention\\Image\\Drivers\\Imagick\\Modifiers\\FlopModifier' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Modifiers/FlopModifier.php',
    'Intervention\\Image\\Drivers\\Imagick\\Modifiers\\GammaModifier' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Modifiers/GammaModifier.php',
    'Intervention\\Image\\Drivers\\Imagick\\Modifiers\\GreyscaleModifier' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Modifiers/GreyscaleModifier.php',
    'Intervention\\Image\\Drivers\\Imagick\\Modifiers\\InvertModifier' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Modifiers/InvertModifier.php',
    'Intervention\\Image\\Drivers\\Imagick\\Modifiers\\PadModifier' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Modifiers/PadModifier.php',
    'Intervention\\Image\\Drivers\\Imagick\\Modifiers\\PixelateModifier' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Modifiers/PixelateModifier.php',
    'Intervention\\Image\\Drivers\\Imagick\\Modifiers\\PlaceModifier' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Modifiers/PlaceModifier.php',
    'Intervention\\Image\\Drivers\\Imagick\\Modifiers\\ProfileModifier' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Modifiers/ProfileModifier.php',
    'Intervention\\Image\\Drivers\\Imagick\\Modifiers\\ProfileRemovalModifier' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Modifiers/ProfileRemovalModifier.php',
    'Intervention\\Image\\Drivers\\Imagick\\Modifiers\\QuantizeColorsModifier' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Modifiers/QuantizeColorsModifier.php',
    'Intervention\\Image\\Drivers\\Imagick\\Modifiers\\RemoveAnimationModifier' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Modifiers/RemoveAnimationModifier.php',
    'Intervention\\Image\\Drivers\\Imagick\\Modifiers\\ResizeCanvasModifier' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Modifiers/ResizeCanvasModifier.php',
    'Intervention\\Image\\Drivers\\Imagick\\Modifiers\\ResizeCanvasRelativeModifier' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Modifiers/ResizeCanvasRelativeModifier.php',
    'Intervention\\Image\\Drivers\\Imagick\\Modifiers\\ResizeDownModifier' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Modifiers/ResizeDownModifier.php',
    'Intervention\\Image\\Drivers\\Imagick\\Modifiers\\ResizeModifier' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Modifiers/ResizeModifier.php',
    'Intervention\\Image\\Drivers\\Imagick\\Modifiers\\ResolutionModifier' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Modifiers/ResolutionModifier.php',
    'Intervention\\Image\\Drivers\\Imagick\\Modifiers\\RotateModifier' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Modifiers/RotateModifier.php',
    'Intervention\\Image\\Drivers\\Imagick\\Modifiers\\ScaleDownModifier' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Modifiers/ScaleDownModifier.php',
    'Intervention\\Image\\Drivers\\Imagick\\Modifiers\\ScaleModifier' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Modifiers/ScaleModifier.php',
    'Intervention\\Image\\Drivers\\Imagick\\Modifiers\\SharpenModifier' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Modifiers/SharpenModifier.php',
    'Intervention\\Image\\Drivers\\Imagick\\Modifiers\\SliceAnimationModifier' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Modifiers/SliceAnimationModifier.php',
    'Intervention\\Image\\Drivers\\Imagick\\Modifiers\\StripMetaModifier' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Modifiers/StripMetaModifier.php',
    'Intervention\\Image\\Drivers\\Imagick\\Modifiers\\TextModifier' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Modifiers/TextModifier.php',
    'Intervention\\Image\\Drivers\\Imagick\\Modifiers\\TrimModifier' => $vendorDir . '/intervention/image/src/Drivers/Imagick/Modifiers/TrimModifier.php',
    'Intervention\\Image\\Drivers\\Specializable' => $vendorDir . '/intervention/image/src/Drivers/Specializable.php',
    'Intervention\\Image\\Drivers\\SpecializableAnalyzer' => $vendorDir . '/intervention/image/src/Drivers/SpecializableAnalyzer.php',
    'Intervention\\Image\\Drivers\\SpecializableDecoder' => $vendorDir . '/intervention/image/src/Drivers/SpecializableDecoder.php',
    'Intervention\\Image\\Drivers\\SpecializableEncoder' => $vendorDir . '/intervention/image/src/Drivers/SpecializableEncoder.php',
    'Intervention\\Image\\Drivers\\SpecializableModifier' => $vendorDir . '/intervention/image/src/Drivers/SpecializableModifier.php',
    'Intervention\\Image\\EncodedImage' => $vendorDir . '/intervention/image/src/EncodedImage.php',
    'Intervention\\Image\\Encoders\\AutoEncoder' => $vendorDir . '/intervention/image/src/Encoders/AutoEncoder.php',
    'Intervention\\Image\\Encoders\\AvifEncoder' => $vendorDir . '/intervention/image/src/Encoders/AvifEncoder.php',
    'Intervention\\Image\\Encoders\\BmpEncoder' => $vendorDir . '/intervention/image/src/Encoders/BmpEncoder.php',
    'Intervention\\Image\\Encoders\\FileExtensionEncoder' => $vendorDir . '/intervention/image/src/Encoders/FileExtensionEncoder.php',
    'Intervention\\Image\\Encoders\\FilePathEncoder' => $vendorDir . '/intervention/image/src/Encoders/FilePathEncoder.php',
    'Intervention\\Image\\Encoders\\GifEncoder' => $vendorDir . '/intervention/image/src/Encoders/GifEncoder.php',
    'Intervention\\Image\\Encoders\\HeicEncoder' => $vendorDir . '/intervention/image/src/Encoders/HeicEncoder.php',
    'Intervention\\Image\\Encoders\\Jpeg2000Encoder' => $vendorDir . '/intervention/image/src/Encoders/Jpeg2000Encoder.php',
    'Intervention\\Image\\Encoders\\JpegEncoder' => $vendorDir . '/intervention/image/src/Encoders/JpegEncoder.php',
    'Intervention\\Image\\Encoders\\MediaTypeEncoder' => $vendorDir . '/intervention/image/src/Encoders/MediaTypeEncoder.php',
    'Intervention\\Image\\Encoders\\PngEncoder' => $vendorDir . '/intervention/image/src/Encoders/PngEncoder.php',
    'Intervention\\Image\\Encoders\\TiffEncoder' => $vendorDir . '/intervention/image/src/Encoders/TiffEncoder.php',
    'Intervention\\Image\\Encoders\\WebpEncoder' => $vendorDir . '/intervention/image/src/Encoders/WebpEncoder.php',
    'Intervention\\Image\\Exceptions\\AnimationException' => $vendorDir . '/intervention/image/src/Exceptions/AnimationException.php',
    'Intervention\\Image\\Exceptions\\ColorException' => $vendorDir . '/intervention/image/src/Exceptions/ColorException.php',
    'Intervention\\Image\\Exceptions\\DecoderException' => $vendorDir . '/intervention/image/src/Exceptions/DecoderException.php',
    'Intervention\\Image\\Exceptions\\DriverException' => $vendorDir . '/intervention/image/src/Exceptions/DriverException.php',
    'Intervention\\Image\\Exceptions\\EncoderException' => $vendorDir . '/intervention/image/src/Exceptions/EncoderException.php',
    'Intervention\\Image\\Exceptions\\FontException' => $vendorDir . '/intervention/image/src/Exceptions/FontException.php',
    'Intervention\\Image\\Exceptions\\GeometryException' => $vendorDir . '/intervention/image/src/Exceptions/GeometryException.php',
    'Intervention\\Image\\Exceptions\\InputException' => $vendorDir . '/intervention/image/src/Exceptions/InputException.php',
    'Intervention\\Image\\Exceptions\\NotSupportedException' => $vendorDir . '/intervention/image/src/Exceptions/NotSupportedException.php',
    'Intervention\\Image\\Exceptions\\NotWritableException' => $vendorDir . '/intervention/image/src/Exceptions/NotWritableException.php',
    'Intervention\\Image\\Exceptions\\RuntimeException' => $vendorDir . '/intervention/image/src/Exceptions/RuntimeException.php',
    'Intervention\\Image\\File' => $vendorDir . '/intervention/image/src/File.php',
    'Intervention\\Image\\FileExtension' => $vendorDir . '/intervention/image/src/FileExtension.php',
    'Intervention\\Image\\Format' => $vendorDir . '/intervention/image/src/Format.php',
    'Intervention\\Image\\Geometry\\Bezier' => $vendorDir . '/intervention/image/src/Geometry/Bezier.php',
    'Intervention\\Image\\Geometry\\Circle' => $vendorDir . '/intervention/image/src/Geometry/Circle.php',
    'Intervention\\Image\\Geometry\\Ellipse' => $vendorDir . '/intervention/image/src/Geometry/Ellipse.php',
    'Intervention\\Image\\Geometry\\Factories\\BezierFactory' => $vendorDir . '/intervention/image/src/Geometry/Factories/BezierFactory.php',
    'Intervention\\Image\\Geometry\\Factories\\CircleFactory' => $vendorDir . '/intervention/image/src/Geometry/Factories/CircleFactory.php',
    'Intervention\\Image\\Geometry\\Factories\\Drawable' => $vendorDir . '/intervention/image/src/Geometry/Factories/Drawable.php',
    'Intervention\\Image\\Geometry\\Factories\\EllipseFactory' => $vendorDir . '/intervention/image/src/Geometry/Factories/EllipseFactory.php',
    'Intervention\\Image\\Geometry\\Factories\\LineFactory' => $vendorDir . '/intervention/image/src/Geometry/Factories/LineFactory.php',
    'Intervention\\Image\\Geometry\\Factories\\PolygonFactory' => $vendorDir . '/intervention/image/src/Geometry/Factories/PolygonFactory.php',
    'Intervention\\Image\\Geometry\\Factories\\RectangleFactory' => $vendorDir . '/intervention/image/src/Geometry/Factories/RectangleFactory.php',
    'Intervention\\Image\\Geometry\\Line' => $vendorDir . '/intervention/image/src/Geometry/Line.php',
    'Intervention\\Image\\Geometry\\Pixel' => $vendorDir . '/intervention/image/src/Geometry/Pixel.php',
    'Intervention\\Image\\Geometry\\Point' => $vendorDir . '/intervention/image/src/Geometry/Point.php',
    'Intervention\\Image\\Geometry\\Polygon' => $vendorDir . '/intervention/image/src/Geometry/Polygon.php',
    'Intervention\\Image\\Geometry\\Rectangle' => $vendorDir . '/intervention/image/src/Geometry/Rectangle.php',
    'Intervention\\Image\\Geometry\\Tools\\RectangleResizer' => $vendorDir . '/intervention/image/src/Geometry/Tools/RectangleResizer.php',
    'Intervention\\Image\\Geometry\\Traits\\HasBackgroundColor' => $vendorDir . '/intervention/image/src/Geometry/Traits/HasBackgroundColor.php',
    'Intervention\\Image\\Geometry\\Traits\\HasBorder' => $vendorDir . '/intervention/image/src/Geometry/Traits/HasBorder.php',
    'Intervention\\Image\\Image' => $vendorDir . '/intervention/image/src/Image.php',
    'Intervention\\Image\\ImageManager' => $vendorDir . '/intervention/image/src/ImageManager.php',
    'Intervention\\Image\\InputHandler' => $vendorDir . '/intervention/image/src/InputHandler.php',
    'Intervention\\Image\\Interfaces\\AnalyzerInterface' => $vendorDir . '/intervention/image/src/Interfaces/AnalyzerInterface.php',
    'Intervention\\Image\\Interfaces\\CollectionInterface' => $vendorDir . '/intervention/image/src/Interfaces/CollectionInterface.php',
    'Intervention\\Image\\Interfaces\\ColorChannelInterface' => $vendorDir . '/intervention/image/src/Interfaces/ColorChannelInterface.php',
    'Intervention\\Image\\Interfaces\\ColorInterface' => $vendorDir . '/intervention/image/src/Interfaces/ColorInterface.php',
    'Intervention\\Image\\Interfaces\\ColorProcessorInterface' => $vendorDir . '/intervention/image/src/Interfaces/ColorProcessorInterface.php',
    'Intervention\\Image\\Interfaces\\ColorspaceInterface' => $vendorDir . '/intervention/image/src/Interfaces/ColorspaceInterface.php',
    'Intervention\\Image\\Interfaces\\CoreInterface' => $vendorDir . '/intervention/image/src/Interfaces/CoreInterface.php',
    'Intervention\\Image\\Interfaces\\DecoderInterface' => $vendorDir . '/intervention/image/src/Interfaces/DecoderInterface.php',
    'Intervention\\Image\\Interfaces\\DrawableFactoryInterface' => $vendorDir . '/intervention/image/src/Interfaces/DrawableFactoryInterface.php',
    'Intervention\\Image\\Interfaces\\DrawableInterface' => $vendorDir . '/intervention/image/src/Interfaces/DrawableInterface.php',
    'Intervention\\Image\\Interfaces\\DriverInterface' => $vendorDir . '/intervention/image/src/Interfaces/DriverInterface.php',
    'Intervention\\Image\\Interfaces\\EncodedImageInterface' => $vendorDir . '/intervention/image/src/Interfaces/EncodedImageInterface.php',
    'Intervention\\Image\\Interfaces\\EncoderInterface' => $vendorDir . '/intervention/image/src/Interfaces/EncoderInterface.php',
    'Intervention\\Image\\Interfaces\\FileInterface' => $vendorDir . '/intervention/image/src/Interfaces/FileInterface.php',
    'Intervention\\Image\\Interfaces\\FontInterface' => $vendorDir . '/intervention/image/src/Interfaces/FontInterface.php',
    'Intervention\\Image\\Interfaces\\FontProcessorInterface' => $vendorDir . '/intervention/image/src/Interfaces/FontProcessorInterface.php',
    'Intervention\\Image\\Interfaces\\FrameInterface' => $vendorDir . '/intervention/image/src/Interfaces/FrameInterface.php',
    'Intervention\\Image\\Interfaces\\ImageInterface' => $vendorDir . '/intervention/image/src/Interfaces/ImageInterface.php',
    'Intervention\\Image\\Interfaces\\ImageManagerInterface' => $vendorDir . '/intervention/image/src/Interfaces/ImageManagerInterface.php',
    'Intervention\\Image\\Interfaces\\InputHandlerInterface' => $vendorDir . '/intervention/image/src/Interfaces/InputHandlerInterface.php',
    'Intervention\\Image\\Interfaces\\ModifierInterface' => $vendorDir . '/intervention/image/src/Interfaces/ModifierInterface.php',
    'Intervention\\Image\\Interfaces\\PointInterface' => $vendorDir . '/intervention/image/src/Interfaces/PointInterface.php',
    'Intervention\\Image\\Interfaces\\ProfileInterface' => $vendorDir . '/intervention/image/src/Interfaces/ProfileInterface.php',
    'Intervention\\Image\\Interfaces\\ResolutionInterface' => $vendorDir . '/intervention/image/src/Interfaces/ResolutionInterface.php',
    'Intervention\\Image\\Interfaces\\SizeInterface' => $vendorDir . '/intervention/image/src/Interfaces/SizeInterface.php',
    'Intervention\\Image\\Interfaces\\SpecializableInterface' => $vendorDir . '/intervention/image/src/Interfaces/SpecializableInterface.php',
    'Intervention\\Image\\Interfaces\\SpecializedInterface' => $vendorDir . '/intervention/image/src/Interfaces/SpecializedInterface.php',
    'Intervention\\Image\\MediaType' => $vendorDir . '/intervention/image/src/MediaType.php',
    'Intervention\\Image\\ModifierStack' => $vendorDir . '/intervention/image/src/ModifierStack.php',
    'Intervention\\Image\\Modifiers\\AbstractDrawModifier' => $vendorDir . '/intervention/image/src/Modifiers/AbstractDrawModifier.php',
    'Intervention\\Image\\Modifiers\\AlignRotationModifier' => $vendorDir . '/intervention/image/src/Modifiers/AlignRotationModifier.php',
    'Intervention\\Image\\Modifiers\\BlendTransparencyModifier' => $vendorDir . '/intervention/image/src/Modifiers/BlendTransparencyModifier.php',
    'Intervention\\Image\\Modifiers\\BlurModifier' => $vendorDir . '/intervention/image/src/Modifiers/BlurModifier.php',
    'Intervention\\Image\\Modifiers\\BrightnessModifier' => $vendorDir . '/intervention/image/src/Modifiers/BrightnessModifier.php',
    'Intervention\\Image\\Modifiers\\ColorizeModifier' => $vendorDir . '/intervention/image/src/Modifiers/ColorizeModifier.php',
    'Intervention\\Image\\Modifiers\\ColorspaceModifier' => $vendorDir . '/intervention/image/src/Modifiers/ColorspaceModifier.php',
    'Intervention\\Image\\Modifiers\\ContainModifier' => $vendorDir . '/intervention/image/src/Modifiers/ContainModifier.php',
    'Intervention\\Image\\Modifiers\\ContrastModifier' => $vendorDir . '/intervention/image/src/Modifiers/ContrastModifier.php',
    'Intervention\\Image\\Modifiers\\CoverDownModifier' => $vendorDir . '/intervention/image/src/Modifiers/CoverDownModifier.php',
    'Intervention\\Image\\Modifiers\\CoverModifier' => $vendorDir . '/intervention/image/src/Modifiers/CoverModifier.php',
    'Intervention\\Image\\Modifiers\\CropModifier' => $vendorDir . '/intervention/image/src/Modifiers/CropModifier.php',
    'Intervention\\Image\\Modifiers\\DrawBezierModifier' => $vendorDir . '/intervention/image/src/Modifiers/DrawBezierModifier.php',
    'Intervention\\Image\\Modifiers\\DrawEllipseModifier' => $vendorDir . '/intervention/image/src/Modifiers/DrawEllipseModifier.php',
    'Intervention\\Image\\Modifiers\\DrawLineModifier' => $vendorDir . '/intervention/image/src/Modifiers/DrawLineModifier.php',
    'Intervention\\Image\\Modifiers\\DrawPixelModifier' => $vendorDir . '/intervention/image/src/Modifiers/DrawPixelModifier.php',
    'Intervention\\Image\\Modifiers\\DrawPolygonModifier' => $vendorDir . '/intervention/image/src/Modifiers/DrawPolygonModifier.php',
    'Intervention\\Image\\Modifiers\\DrawRectangleModifier' => $vendorDir . '/intervention/image/src/Modifiers/DrawRectangleModifier.php',
    'Intervention\\Image\\Modifiers\\FillModifier' => $vendorDir . '/intervention/image/src/Modifiers/FillModifier.php',
    'Intervention\\Image\\Modifiers\\FlipModifier' => $vendorDir . '/intervention/image/src/Modifiers/FlipModifier.php',
    'Intervention\\Image\\Modifiers\\FlopModifier' => $vendorDir . '/intervention/image/src/Modifiers/FlopModifier.php',
    'Intervention\\Image\\Modifiers\\GammaModifier' => $vendorDir . '/intervention/image/src/Modifiers/GammaModifier.php',
    'Intervention\\Image\\Modifiers\\GreyscaleModifier' => $vendorDir . '/intervention/image/src/Modifiers/GreyscaleModifier.php',
    'Intervention\\Image\\Modifiers\\InvertModifier' => $vendorDir . '/intervention/image/src/Modifiers/InvertModifier.php',
    'Intervention\\Image\\Modifiers\\PadModifier' => $vendorDir . '/intervention/image/src/Modifiers/PadModifier.php',
    'Intervention\\Image\\Modifiers\\PixelateModifier' => $vendorDir . '/intervention/image/src/Modifiers/PixelateModifier.php',
    'Intervention\\Image\\Modifiers\\PlaceModifier' => $vendorDir . '/intervention/image/src/Modifiers/PlaceModifier.php',
    'Intervention\\Image\\Modifiers\\ProfileModifier' => $vendorDir . '/intervention/image/src/Modifiers/ProfileModifier.php',
    'Intervention\\Image\\Modifiers\\ProfileRemovalModifier' => $vendorDir . '/intervention/image/src/Modifiers/ProfileRemovalModifier.php',
    'Intervention\\Image\\Modifiers\\QuantizeColorsModifier' => $vendorDir . '/intervention/image/src/Modifiers/QuantizeColorsModifier.php',
    'Intervention\\Image\\Modifiers\\RemoveAnimationModifier' => $vendorDir . '/intervention/image/src/Modifiers/RemoveAnimationModifier.php',
    'Intervention\\Image\\Modifiers\\ResizeCanvasModifier' => $vendorDir . '/intervention/image/src/Modifiers/ResizeCanvasModifier.php',
    'Intervention\\Image\\Modifiers\\ResizeCanvasRelativeModifier' => $vendorDir . '/intervention/image/src/Modifiers/ResizeCanvasRelativeModifier.php',
    'Intervention\\Image\\Modifiers\\ResizeDownModifier' => $vendorDir . '/intervention/image/src/Modifiers/ResizeDownModifier.php',
    'Intervention\\Image\\Modifiers\\ResizeModifier' => $vendorDir . '/intervention/image/src/Modifiers/ResizeModifier.php',
    'Intervention\\Image\\Modifiers\\ResolutionModifier' => $vendorDir . '/intervention/image/src/Modifiers/ResolutionModifier.php',
    'Intervention\\Image\\Modifiers\\RotateModifier' => $vendorDir . '/intervention/image/src/Modifiers/RotateModifier.php',
    'Intervention\\Image\\Modifiers\\ScaleDownModifier' => $vendorDir . '/intervention/image/src/Modifiers/ScaleDownModifier.php',
    'Intervention\\Image\\Modifiers\\ScaleModifier' => $vendorDir . '/intervention/image/src/Modifiers/ScaleModifier.php',
    'Intervention\\Image\\Modifiers\\SharpenModifier' => $vendorDir . '/intervention/image/src/Modifiers/SharpenModifier.php',
    'Intervention\\Image\\Modifiers\\SliceAnimationModifier' => $vendorDir . '/intervention/image/src/Modifiers/SliceAnimationModifier.php',
    'Intervention\\Image\\Modifiers\\TextModifier' => $vendorDir . '/intervention/image/src/Modifiers/TextModifier.php',
    'Intervention\\Image\\Modifiers\\TrimModifier' => $vendorDir . '/intervention/image/src/Modifiers/TrimModifier.php',
    'Intervention\\Image\\Origin' => $vendorDir . '/intervention/image/src/Origin.php',
    'Intervention\\Image\\Resolution' => $vendorDir . '/intervention/image/src/Resolution.php',
    'Intervention\\Image\\Traits\\CanBeDriverSpecialized' => $vendorDir . '/intervention/image/src/Traits/CanBeDriverSpecialized.php',
    'Intervention\\Image\\Traits\\CanBuildFilePointer' => $vendorDir . '/intervention/image/src/Traits/CanBuildFilePointer.php',
    'Intervention\\Image\\Typography\\Font' => $vendorDir . '/intervention/image/src/Typography/Font.php',
    'Intervention\\Image\\Typography\\FontFactory' => $vendorDir . '/intervention/image/src/Typography/FontFactory.php',
    'Intervention\\Image\\Typography\\Line' => $vendorDir . '/intervention/image/src/Typography/Line.php',
    'Intervention\\Image\\Typography\\TextBlock' => $vendorDir . '/intervention/image/src/Typography/TextBlock.php',
    'League\\Flysystem\\CalculateChecksumFromStream' => $vendorDir . '/league/flysystem/src/CalculateChecksumFromStream.php',
    'League\\Flysystem\\ChecksumAlgoIsNotSupported' => $vendorDir . '/league/flysystem/src/ChecksumAlgoIsNotSupported.php',
    'League\\Flysystem\\ChecksumProvider' => $vendorDir . '/league/flysystem/src/ChecksumProvider.php',
    'League\\Flysystem\\Config' => $vendorDir . '/league/flysystem/src/Config.php',
    'League\\Flysystem\\CorruptedPathDetected' => $vendorDir . '/league/flysystem/src/CorruptedPathDetected.php',
    'League\\Flysystem\\DecoratedAdapter' => $vendorDir . '/league/flysystem/src/DecoratedAdapter.php',
    'League\\Flysystem\\DirectoryAttributes' => $vendorDir . '/league/flysystem/src/DirectoryAttributes.php',
    'League\\Flysystem\\DirectoryListing' => $vendorDir . '/league/flysystem/src/DirectoryListing.php',
    'League\\Flysystem\\FileAttributes' => $vendorDir . '/league/flysystem/src/FileAttributes.php',
    'League\\Flysystem\\Filesystem' => $vendorDir . '/league/flysystem/src/Filesystem.php',
    'League\\Flysystem\\FilesystemAdapter' => $vendorDir . '/league/flysystem/src/FilesystemAdapter.php',
    'League\\Flysystem\\FilesystemException' => $vendorDir . '/league/flysystem/src/FilesystemException.php',
    'League\\Flysystem\\FilesystemOperationFailed' => $vendorDir . '/league/flysystem/src/FilesystemOperationFailed.php',
    'League\\Flysystem\\FilesystemOperator' => $vendorDir . '/league/flysystem/src/FilesystemOperator.php',
    'League\\Flysystem\\FilesystemReader' => $vendorDir . '/league/flysystem/src/FilesystemReader.php',
    'League\\Flysystem\\FilesystemWriter' => $vendorDir . '/league/flysystem/src/FilesystemWriter.php',
    'League\\Flysystem\\InvalidStreamProvided' => $vendorDir . '/league/flysystem/src/InvalidStreamProvided.php',
    'League\\Flysystem\\InvalidVisibilityProvided' => $vendorDir . '/league/flysystem/src/InvalidVisibilityProvided.php',
    'League\\Flysystem\\Local\\FallbackMimeTypeDetector' => $vendorDir . '/league/flysystem-local/FallbackMimeTypeDetector.php',
    'League\\Flysystem\\Local\\LocalFilesystemAdapter' => $vendorDir . '/league/flysystem-local/LocalFilesystemAdapter.php',
    'League\\Flysystem\\MountManager' => $vendorDir . '/league/flysystem/src/MountManager.php',
    'League\\Flysystem\\PathNormalizer' => $vendorDir . '/league/flysystem/src/PathNormalizer.php',
    'League\\Flysystem\\PathPrefixer' => $vendorDir . '/league/flysystem/src/PathPrefixer.php',
    'League\\Flysystem\\PathTraversalDetected' => $vendorDir . '/league/flysystem/src/PathTraversalDetected.php',
    'League\\Flysystem\\PortableVisibilityGuard' => $vendorDir . '/league/flysystem/src/PortableVisibilityGuard.php',
    'League\\Flysystem\\ProxyArrayAccessToProperties' => $vendorDir . '/league/flysystem/src/ProxyArrayAccessToProperties.php',
    'League\\Flysystem\\ResolveIdenticalPathConflict' => $vendorDir . '/league/flysystem/src/ResolveIdenticalPathConflict.php',
    'League\\Flysystem\\StorageAttributes' => $vendorDir . '/league/flysystem/src/StorageAttributes.php',
    'League\\Flysystem\\SymbolicLinkEncountered' => $vendorDir . '/league/flysystem/src/SymbolicLinkEncountered.php',
    'League\\Flysystem\\UnableToCheckDirectoryExistence' => $vendorDir . '/league/flysystem/src/UnableToCheckDirectoryExistence.php',
    'League\\Flysystem\\UnableToCheckExistence' => $vendorDir . '/league/flysystem/src/UnableToCheckExistence.php',
    'League\\Flysystem\\UnableToCheckFileExistence' => $vendorDir . '/league/flysystem/src/UnableToCheckFileExistence.php',
    'League\\Flysystem\\UnableToCopyFile' => $vendorDir . '/league/flysystem/src/UnableToCopyFile.php',
    'League\\Flysystem\\UnableToCreateDirectory' => $vendorDir . '/league/flysystem/src/UnableToCreateDirectory.php',
    'League\\Flysystem\\UnableToDeleteDirectory' => $vendorDir . '/league/flysystem/src/UnableToDeleteDirectory.php',
    'League\\Flysystem\\UnableToDeleteFile' => $vendorDir . '/league/flysystem/src/UnableToDeleteFile.php',
    'League\\Flysystem\\UnableToGeneratePublicUrl' => $vendorDir . '/league/flysystem/src/UnableToGeneratePublicUrl.php',
    'League\\Flysystem\\UnableToGenerateTemporaryUrl' => $vendorDir . '/league/flysystem/src/UnableToGenerateTemporaryUrl.php',
    'League\\Flysystem\\UnableToListContents' => $vendorDir . '/league/flysystem/src/UnableToListContents.php',
    'League\\Flysystem\\UnableToMountFilesystem' => $vendorDir . '/league/flysystem/src/UnableToMountFilesystem.php',
    'League\\Flysystem\\UnableToMoveFile' => $vendorDir . '/league/flysystem/src/UnableToMoveFile.php',
    'League\\Flysystem\\UnableToProvideChecksum' => $vendorDir . '/league/flysystem/src/UnableToProvideChecksum.php',
    'League\\Flysystem\\UnableToReadFile' => $vendorDir . '/league/flysystem/src/UnableToReadFile.php',
    'League\\Flysystem\\UnableToResolveFilesystemMount' => $vendorDir . '/league/flysystem/src/UnableToResolveFilesystemMount.php',
    'League\\Flysystem\\UnableToRetrieveMetadata' => $vendorDir . '/league/flysystem/src/UnableToRetrieveMetadata.php',
    'League\\Flysystem\\UnableToSetVisibility' => $vendorDir . '/league/flysystem/src/UnableToSetVisibility.php',
    'League\\Flysystem\\UnableToWriteFile' => $vendorDir . '/league/flysystem/src/UnableToWriteFile.php',
    'League\\Flysystem\\UnixVisibility\\PortableVisibilityConverter' => $vendorDir . '/league/flysystem/src/UnixVisibility/PortableVisibilityConverter.php',
    'League\\Flysystem\\UnixVisibility\\VisibilityConverter' => $vendorDir . '/league/flysystem/src/UnixVisibility/VisibilityConverter.php',
    'League\\Flysystem\\UnreadableFileEncountered' => $vendorDir . '/league/flysystem/src/UnreadableFileEncountered.php',
    'League\\Flysystem\\UrlGeneration\\ChainedPublicUrlGenerator' => $vendorDir . '/league/flysystem/src/UrlGeneration/ChainedPublicUrlGenerator.php',
    'League\\Flysystem\\UrlGeneration\\PrefixPublicUrlGenerator' => $vendorDir . '/league/flysystem/src/UrlGeneration/PrefixPublicUrlGenerator.php',
    'League\\Flysystem\\UrlGeneration\\PublicUrlGenerator' => $vendorDir . '/league/flysystem/src/UrlGeneration/PublicUrlGenerator.php',
    'League\\Flysystem\\UrlGeneration\\ShardedPrefixPublicUrlGenerator' => $vendorDir . '/league/flysystem/src/UrlGeneration/ShardedPrefixPublicUrlGenerator.php',
    'League\\Flysystem\\UrlGeneration\\TemporaryUrlGenerator' => $vendorDir . '/league/flysystem/src/UrlGeneration/TemporaryUrlGenerator.php',
    'League\\Flysystem\\Visibility' => $vendorDir . '/league/flysystem/src/Visibility.php',
    'League\\Flysystem\\WhitespacePathNormalizer' => $vendorDir . '/league/flysystem/src/WhitespacePathNormalizer.php',
    'League\\MimeTypeDetection\\EmptyExtensionToMimeTypeMap' => $vendorDir . '/league/mime-type-detection/src/EmptyExtensionToMimeTypeMap.php',
    'League\\MimeTypeDetection\\ExtensionLookup' => $vendorDir . '/league/mime-type-detection/src/ExtensionLookup.php',
    'League\\MimeTypeDetection\\ExtensionMimeTypeDetector' => $vendorDir . '/league/mime-type-detection/src/ExtensionMimeTypeDetector.php',
    'League\\MimeTypeDetection\\ExtensionToMimeTypeMap' => $vendorDir . '/league/mime-type-detection/src/ExtensionToMimeTypeMap.php',
    'League\\MimeTypeDetection\\FinfoMimeTypeDetector' => $vendorDir . '/league/mime-type-detection/src/FinfoMimeTypeDetector.php',
    'League\\MimeTypeDetection\\GeneratedExtensionToMimeTypeMap' => $vendorDir . '/league/mime-type-detection/src/GeneratedExtensionToMimeTypeMap.php',
    'League\\MimeTypeDetection\\MimeTypeDetector' => $vendorDir . '/league/mime-type-detection/src/MimeTypeDetector.php',
    'League\\MimeTypeDetection\\OverridingExtensionToMimeTypeMap' => $vendorDir . '/league/mime-type-detection/src/OverridingExtensionToMimeTypeMap.php',
    'Monolog\\Attribute\\AsMonologProcessor' => $vendorDir . '/monolog/monolog/src/Monolog/Attribute/AsMonologProcessor.php',
    'Monolog\\Attribute\\WithMonologChannel' => $vendorDir . '/monolog/monolog/src/Monolog/Attribute/WithMonologChannel.php',
    'Monolog\\DateTimeImmutable' => $vendorDir . '/monolog/monolog/src/Monolog/DateTimeImmutable.php',
    'Monolog\\ErrorHandler' => $vendorDir . '/monolog/monolog/src/Monolog/ErrorHandler.php',
    'Monolog\\Formatter\\ChromePHPFormatter' => $vendorDir . '/monolog/monolog/src/Monolog/Formatter/ChromePHPFormatter.php',
    'Monolog\\Formatter\\ElasticaFormatter' => $vendorDir . '/monolog/monolog/src/Monolog/Formatter/ElasticaFormatter.php',
    'Monolog\\Formatter\\ElasticsearchFormatter' => $vendorDir . '/monolog/monolog/src/Monolog/Formatter/ElasticsearchFormatter.php',
    'Monolog\\Formatter\\FlowdockFormatter' => $vendorDir . '/monolog/monolog/src/Monolog/Formatter/FlowdockFormatter.php',
    'Monolog\\Formatter\\FluentdFormatter' => $vendorDir . '/monolog/monolog/src/Monolog/Formatter/FluentdFormatter.php',
    'Monolog\\Formatter\\FormatterInterface' => $vendorDir . '/monolog/monolog/src/Monolog/Formatter/FormatterInterface.php',
    'Monolog\\Formatter\\GelfMessageFormatter' => $vendorDir . '/monolog/monolog/src/Monolog/Formatter/GelfMessageFormatter.php',
    'Monolog\\Formatter\\GoogleCloudLoggingFormatter' => $vendorDir . '/monolog/monolog/src/Monolog/Formatter/GoogleCloudLoggingFormatter.php',
    'Monolog\\Formatter\\HtmlFormatter' => $vendorDir . '/monolog/monolog/src/Monolog/Formatter/HtmlFormatter.php',
    'Monolog\\Formatter\\JsonFormatter' => $vendorDir . '/monolog/monolog/src/Monolog/Formatter/JsonFormatter.php',
    'Monolog\\Formatter\\LineFormatter' => $vendorDir . '/monolog/monolog/src/Monolog/Formatter/LineFormatter.php',
    'Monolog\\Formatter\\LogglyFormatter' => $vendorDir . '/monolog/monolog/src/Monolog/Formatter/LogglyFormatter.php',
    'Monolog\\Formatter\\LogmaticFormatter' => $vendorDir . '/monolog/monolog/src/Monolog/Formatter/LogmaticFormatter.php',
    'Monolog\\Formatter\\LogstashFormatter' => $vendorDir . '/monolog/monolog/src/Monolog/Formatter/LogstashFormatter.php',
    'Monolog\\Formatter\\MongoDBFormatter' => $vendorDir . '/monolog/monolog/src/Monolog/Formatter/MongoDBFormatter.php',
    'Monolog\\Formatter\\NormalizerFormatter' => $vendorDir . '/monolog/monolog/src/Monolog/Formatter/NormalizerFormatter.php',
    'Monolog\\Formatter\\ScalarFormatter' => $vendorDir . '/monolog/monolog/src/Monolog/Formatter/ScalarFormatter.php',
    'Monolog\\Formatter\\SyslogFormatter' => $vendorDir . '/monolog/monolog/src/Monolog/Formatter/SyslogFormatter.php',
    'Monolog\\Formatter\\WildfireFormatter' => $vendorDir . '/monolog/monolog/src/Monolog/Formatter/WildfireFormatter.php',
    'Monolog\\Handler\\AbstractHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/AbstractHandler.php',
    'Monolog\\Handler\\AbstractProcessingHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/AbstractProcessingHandler.php',
    'Monolog\\Handler\\AbstractSyslogHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/AbstractSyslogHandler.php',
    'Monolog\\Handler\\AmqpHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/AmqpHandler.php',
    'Monolog\\Handler\\BrowserConsoleHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/BrowserConsoleHandler.php',
    'Monolog\\Handler\\BufferHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/BufferHandler.php',
    'Monolog\\Handler\\ChromePHPHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/ChromePHPHandler.php',
    'Monolog\\Handler\\CouchDBHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/CouchDBHandler.php',
    'Monolog\\Handler\\CubeHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/CubeHandler.php',
    'Monolog\\Handler\\Curl\\Util' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/Curl/Util.php',
    'Monolog\\Handler\\DeduplicationHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/DeduplicationHandler.php',
    'Monolog\\Handler\\DoctrineCouchDBHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/DoctrineCouchDBHandler.php',
    'Monolog\\Handler\\DynamoDbHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/DynamoDbHandler.php',
    'Monolog\\Handler\\ElasticaHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/ElasticaHandler.php',
    'Monolog\\Handler\\ElasticsearchHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/ElasticsearchHandler.php',
    'Monolog\\Handler\\ErrorLogHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/ErrorLogHandler.php',
    'Monolog\\Handler\\FallbackGroupHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/FallbackGroupHandler.php',
    'Monolog\\Handler\\FilterHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/FilterHandler.php',
    'Monolog\\Handler\\FingersCrossedHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/FingersCrossedHandler.php',
    'Monolog\\Handler\\FingersCrossed\\ActivationStrategyInterface' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/FingersCrossed/ActivationStrategyInterface.php',
    'Monolog\\Handler\\FingersCrossed\\ChannelLevelActivationStrategy' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/FingersCrossed/ChannelLevelActivationStrategy.php',
    'Monolog\\Handler\\FingersCrossed\\ErrorLevelActivationStrategy' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/FingersCrossed/ErrorLevelActivationStrategy.php',
    'Monolog\\Handler\\FirePHPHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/FirePHPHandler.php',
    'Monolog\\Handler\\FleepHookHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/FleepHookHandler.php',
    'Monolog\\Handler\\FlowdockHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/FlowdockHandler.php',
    'Monolog\\Handler\\FormattableHandlerInterface' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/FormattableHandlerInterface.php',
    'Monolog\\Handler\\FormattableHandlerTrait' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/FormattableHandlerTrait.php',
    'Monolog\\Handler\\GelfHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/GelfHandler.php',
    'Monolog\\Handler\\GroupHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/GroupHandler.php',
    'Monolog\\Handler\\Handler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/Handler.php',
    'Monolog\\Handler\\HandlerInterface' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/HandlerInterface.php',
    'Monolog\\Handler\\HandlerWrapper' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/HandlerWrapper.php',
    'Monolog\\Handler\\IFTTTHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/IFTTTHandler.php',
    'Monolog\\Handler\\InsightOpsHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/InsightOpsHandler.php',
    'Monolog\\Handler\\LogEntriesHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/LogEntriesHandler.php',
    'Monolog\\Handler\\LogglyHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/LogglyHandler.php',
    'Monolog\\Handler\\LogmaticHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/LogmaticHandler.php',
    'Monolog\\Handler\\MailHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/MailHandler.php',
    'Monolog\\Handler\\MandrillHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/MandrillHandler.php',
    'Monolog\\Handler\\MissingExtensionException' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/MissingExtensionException.php',
    'Monolog\\Handler\\MongoDBHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/MongoDBHandler.php',
    'Monolog\\Handler\\NativeMailerHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/NativeMailerHandler.php',
    'Monolog\\Handler\\NewRelicHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/NewRelicHandler.php',
    'Monolog\\Handler\\NoopHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/NoopHandler.php',
    'Monolog\\Handler\\NullHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/NullHandler.php',
    'Monolog\\Handler\\OverflowHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/OverflowHandler.php',
    'Monolog\\Handler\\PHPConsoleHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/PHPConsoleHandler.php',
    'Monolog\\Handler\\ProcessHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/ProcessHandler.php',
    'Monolog\\Handler\\ProcessableHandlerInterface' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/ProcessableHandlerInterface.php',
    'Monolog\\Handler\\ProcessableHandlerTrait' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/ProcessableHandlerTrait.php',
    'Monolog\\Handler\\PsrHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/PsrHandler.php',
    'Monolog\\Handler\\PushoverHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/PushoverHandler.php',
    'Monolog\\Handler\\RedisHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/RedisHandler.php',
    'Monolog\\Handler\\RedisPubSubHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/RedisPubSubHandler.php',
    'Monolog\\Handler\\RollbarHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/RollbarHandler.php',
    'Monolog\\Handler\\RotatingFileHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/RotatingFileHandler.php',
    'Monolog\\Handler\\SamplingHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/SamplingHandler.php',
    'Monolog\\Handler\\SendGridHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/SendGridHandler.php',
    'Monolog\\Handler\\SlackHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/SlackHandler.php',
    'Monolog\\Handler\\SlackWebhookHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/SlackWebhookHandler.php',
    'Monolog\\Handler\\Slack\\SlackRecord' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/Slack/SlackRecord.php',
    'Monolog\\Handler\\SocketHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/SocketHandler.php',
    'Monolog\\Handler\\SqsHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/SqsHandler.php',
    'Monolog\\Handler\\StreamHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/StreamHandler.php',
    'Monolog\\Handler\\SymfonyMailerHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/SymfonyMailerHandler.php',
    'Monolog\\Handler\\SyslogHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/SyslogHandler.php',
    'Monolog\\Handler\\SyslogUdpHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/SyslogUdpHandler.php',
    'Monolog\\Handler\\SyslogUdp\\UdpSocket' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/SyslogUdp/UdpSocket.php',
    'Monolog\\Handler\\TelegramBotHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/TelegramBotHandler.php',
    'Monolog\\Handler\\TestHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/TestHandler.php',
    'Monolog\\Handler\\WebRequestRecognizerTrait' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/WebRequestRecognizerTrait.php',
    'Monolog\\Handler\\WhatFailureGroupHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/WhatFailureGroupHandler.php',
    'Monolog\\Handler\\ZendMonitorHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/ZendMonitorHandler.php',
    'Monolog\\JsonSerializableDateTimeImmutable' => $vendorDir . '/monolog/monolog/src/Monolog/JsonSerializableDateTimeImmutable.php',
    'Monolog\\Level' => $vendorDir . '/monolog/monolog/src/Monolog/Level.php',
    'Monolog\\LogRecord' => $vendorDir . '/monolog/monolog/src/Monolog/LogRecord.php',
    'Monolog\\Logger' => $vendorDir . '/monolog/monolog/src/Monolog/Logger.php',
    'Monolog\\Processor\\ClosureContextProcessor' => $vendorDir . '/monolog/monolog/src/Monolog/Processor/ClosureContextProcessor.php',
    'Monolog\\Processor\\GitProcessor' => $vendorDir . '/monolog/monolog/src/Monolog/Processor/GitProcessor.php',
    'Monolog\\Processor\\HostnameProcessor' => $vendorDir . '/monolog/monolog/src/Monolog/Processor/HostnameProcessor.php',
    'Monolog\\Processor\\IntrospectionProcessor' => $vendorDir . '/monolog/monolog/src/Monolog/Processor/IntrospectionProcessor.php',
    'Monolog\\Processor\\LoadAverageProcessor' => $vendorDir . '/monolog/monolog/src/Monolog/Processor/LoadAverageProcessor.php',
    'Monolog\\Processor\\MemoryPeakUsageProcessor' => $vendorDir . '/monolog/monolog/src/Monolog/Processor/MemoryPeakUsageProcessor.php',
    'Monolog\\Processor\\MemoryProcessor' => $vendorDir . '/monolog/monolog/src/Monolog/Processor/MemoryProcessor.php',
    'Monolog\\Processor\\MemoryUsageProcessor' => $vendorDir . '/monolog/monolog/src/Monolog/Processor/MemoryUsageProcessor.php',
    'Monolog\\Processor\\MercurialProcessor' => $vendorDir . '/monolog/monolog/src/Monolog/Processor/MercurialProcessor.php',
    'Monolog\\Processor\\ProcessIdProcessor' => $vendorDir . '/monolog/monolog/src/Monolog/Processor/ProcessIdProcessor.php',
    'Monolog\\Processor\\ProcessorInterface' => $vendorDir . '/monolog/monolog/src/Monolog/Processor/ProcessorInterface.php',
    'Monolog\\Processor\\PsrLogMessageProcessor' => $vendorDir . '/monolog/monolog/src/Monolog/Processor/PsrLogMessageProcessor.php',
    'Monolog\\Processor\\TagProcessor' => $vendorDir . '/monolog/monolog/src/Monolog/Processor/TagProcessor.php',
    'Monolog\\Processor\\UidProcessor' => $vendorDir . '/monolog/monolog/src/Monolog/Processor/UidProcessor.php',
    'Monolog\\Processor\\WebProcessor' => $vendorDir . '/monolog/monolog/src/Monolog/Processor/WebProcessor.php',
    'Monolog\\Registry' => $vendorDir . '/monolog/monolog/src/Monolog/Registry.php',
    'Monolog\\ResettableInterface' => $vendorDir . '/monolog/monolog/src/Monolog/ResettableInterface.php',
    'Monolog\\SignalHandler' => $vendorDir . '/monolog/monolog/src/Monolog/SignalHandler.php',
    'Monolog\\Test\\MonologTestCase' => $vendorDir . '/monolog/monolog/src/Monolog/Test/MonologTestCase.php',
    'Monolog\\Test\\TestCase' => $vendorDir . '/monolog/monolog/src/Monolog/Test/TestCase.php',
    'Monolog\\Utils' => $vendorDir . '/monolog/monolog/src/Monolog/Utils.php',
    'Normalizer' => $vendorDir . '/symfony/polyfill-intl-normalizer/Resources/stubs/Normalizer.php',
    'PHPUnit\\Event\\Application\\Finished' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Application/Finished.php',
    'PHPUnit\\Event\\Application\\FinishedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Application/FinishedSubscriber.php',
    'PHPUnit\\Event\\Application\\Started' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Application/Started.php',
    'PHPUnit\\Event\\Application\\StartedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Application/StartedSubscriber.php',
    'PHPUnit\\Event\\Code\\ClassMethod' => $vendorDir . '/phpunit/phpunit/src/Event/Value/ClassMethod.php',
    'PHPUnit\\Event\\Code\\ComparisonFailure' => $vendorDir . '/phpunit/phpunit/src/Event/Value/ComparisonFailure.php',
    'PHPUnit\\Event\\Code\\ComparisonFailureBuilder' => $vendorDir . '/phpunit/phpunit/src/Event/Value/ComparisonFailureBuilder.php',
    'PHPUnit\\Event\\Code\\IssueTrigger\\DirectTrigger' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Test/Issue/DirectTrigger.php',
    'PHPUnit\\Event\\Code\\IssueTrigger\\IndirectTrigger' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Test/Issue/IndirectTrigger.php',
    'PHPUnit\\Event\\Code\\IssueTrigger\\IssueTrigger' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Test/Issue/IssueTrigger.php',
    'PHPUnit\\Event\\Code\\IssueTrigger\\SelfTrigger' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Test/Issue/SelfTrigger.php',
    'PHPUnit\\Event\\Code\\IssueTrigger\\TestTrigger' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Test/Issue/TestTrigger.php',
    'PHPUnit\\Event\\Code\\IssueTrigger\\UnknownTrigger' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Test/Issue/UnknownTrigger.php',
    'PHPUnit\\Event\\Code\\NoTestCaseObjectOnCallStackException' => $vendorDir . '/phpunit/phpunit/src/Event/Exception/NoTestCaseObjectOnCallStackException.php',
    'PHPUnit\\Event\\Code\\Phpt' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Test/Phpt.php',
    'PHPUnit\\Event\\Code\\Test' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Test/Test.php',
    'PHPUnit\\Event\\Code\\TestCollection' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Test/TestCollection.php',
    'PHPUnit\\Event\\Code\\TestCollectionIterator' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Test/TestCollectionIterator.php',
    'PHPUnit\\Event\\Code\\TestDox' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Test/TestDox.php',
    'PHPUnit\\Event\\Code\\TestDoxBuilder' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Test/TestDoxBuilder.php',
    'PHPUnit\\Event\\Code\\TestMethod' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Test/TestMethod.php',
    'PHPUnit\\Event\\Code\\TestMethodBuilder' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Test/TestMethodBuilder.php',
    'PHPUnit\\Event\\Code\\Throwable' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Throwable.php',
    'PHPUnit\\Event\\Code\\ThrowableBuilder' => $vendorDir . '/phpunit/phpunit/src/Event/Value/ThrowableBuilder.php',
    'PHPUnit\\Event\\CollectingDispatcher' => $vendorDir . '/phpunit/phpunit/src/Event/Dispatcher/CollectingDispatcher.php',
    'PHPUnit\\Event\\DeferringDispatcher' => $vendorDir . '/phpunit/phpunit/src/Event/Dispatcher/DeferringDispatcher.php',
    'PHPUnit\\Event\\DirectDispatcher' => $vendorDir . '/phpunit/phpunit/src/Event/Dispatcher/DirectDispatcher.php',
    'PHPUnit\\Event\\Dispatcher' => $vendorDir . '/phpunit/phpunit/src/Event/Dispatcher/Dispatcher.php',
    'PHPUnit\\Event\\DispatchingEmitter' => $vendorDir . '/phpunit/phpunit/src/Event/Emitter/DispatchingEmitter.php',
    'PHPUnit\\Event\\Emitter' => $vendorDir . '/phpunit/phpunit/src/Event/Emitter/Emitter.php',
    'PHPUnit\\Event\\Event' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Event.php',
    'PHPUnit\\Event\\EventAlreadyAssignedException' => $vendorDir . '/phpunit/phpunit/src/Event/Exception/EventAlreadyAssignedException.php',
    'PHPUnit\\Event\\EventCollection' => $vendorDir . '/phpunit/phpunit/src/Event/Events/EventCollection.php',
    'PHPUnit\\Event\\EventCollectionIterator' => $vendorDir . '/phpunit/phpunit/src/Event/Events/EventCollectionIterator.php',
    'PHPUnit\\Event\\EventFacadeIsSealedException' => $vendorDir . '/phpunit/phpunit/src/Event/Exception/EventFacadeIsSealedException.php',
    'PHPUnit\\Event\\Exception' => $vendorDir . '/phpunit/phpunit/src/Event/Exception/Exception.php',
    'PHPUnit\\Event\\Facade' => $vendorDir . '/phpunit/phpunit/src/Event/Facade.php',
    'PHPUnit\\Event\\InvalidArgumentException' => $vendorDir . '/phpunit/phpunit/src/Event/Exception/InvalidArgumentException.php',
    'PHPUnit\\Event\\InvalidEventException' => $vendorDir . '/phpunit/phpunit/src/Event/Exception/InvalidEventException.php',
    'PHPUnit\\Event\\InvalidSubscriberException' => $vendorDir . '/phpunit/phpunit/src/Event/Exception/InvalidSubscriberException.php',
    'PHPUnit\\Event\\MapError' => $vendorDir . '/phpunit/phpunit/src/Event/Exception/MapError.php',
    'PHPUnit\\Event\\NoPreviousThrowableException' => $vendorDir . '/phpunit/phpunit/src/Event/Exception/NoPreviousThrowableException.php',
    'PHPUnit\\Event\\RuntimeException' => $vendorDir . '/phpunit/phpunit/src/Event/Exception/RuntimeException.php',
    'PHPUnit\\Event\\Runtime\\OperatingSystem' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Runtime/OperatingSystem.php',
    'PHPUnit\\Event\\Runtime\\PHP' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Runtime/PHP.php',
    'PHPUnit\\Event\\Runtime\\PHPUnit' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Runtime/PHPUnit.php',
    'PHPUnit\\Event\\Runtime\\Runtime' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Runtime/Runtime.php',
    'PHPUnit\\Event\\SubscribableDispatcher' => $vendorDir . '/phpunit/phpunit/src/Event/Dispatcher/SubscribableDispatcher.php',
    'PHPUnit\\Event\\Subscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Subscriber.php',
    'PHPUnit\\Event\\SubscriberTypeAlreadyRegisteredException' => $vendorDir . '/phpunit/phpunit/src/Event/Exception/SubscriberTypeAlreadyRegisteredException.php',
    'PHPUnit\\Event\\Telemetry\\Duration' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Telemetry/Duration.php',
    'PHPUnit\\Event\\Telemetry\\GarbageCollectorStatus' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Telemetry/GarbageCollectorStatus.php',
    'PHPUnit\\Event\\Telemetry\\GarbageCollectorStatusProvider' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Telemetry/GarbageCollectorStatusProvider.php',
    'PHPUnit\\Event\\Telemetry\\HRTime' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Telemetry/HRTime.php',
    'PHPUnit\\Event\\Telemetry\\Info' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Telemetry/Info.php',
    'PHPUnit\\Event\\Telemetry\\MemoryMeter' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Telemetry/MemoryMeter.php',
    'PHPUnit\\Event\\Telemetry\\MemoryUsage' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Telemetry/MemoryUsage.php',
    'PHPUnit\\Event\\Telemetry\\Php81GarbageCollectorStatusProvider' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Telemetry/Php81GarbageCollectorStatusProvider.php',
    'PHPUnit\\Event\\Telemetry\\Php83GarbageCollectorStatusProvider' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Telemetry/Php83GarbageCollectorStatusProvider.php',
    'PHPUnit\\Event\\Telemetry\\Snapshot' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Telemetry/Snapshot.php',
    'PHPUnit\\Event\\Telemetry\\StopWatch' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Telemetry/StopWatch.php',
    'PHPUnit\\Event\\Telemetry\\System' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Telemetry/System.php',
    'PHPUnit\\Event\\Telemetry\\SystemMemoryMeter' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Telemetry/SystemMemoryMeter.php',
    'PHPUnit\\Event\\Telemetry\\SystemStopWatch' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Telemetry/SystemStopWatch.php',
    'PHPUnit\\Event\\Telemetry\\SystemStopWatchWithOffset' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Telemetry/SystemStopWatchWithOffset.php',
    'PHPUnit\\Event\\TestData\\DataFromDataProvider' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Test/TestData/DataFromDataProvider.php',
    'PHPUnit\\Event\\TestData\\DataFromTestDependency' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Test/TestData/DataFromTestDependency.php',
    'PHPUnit\\Event\\TestData\\NoDataSetFromDataProviderException' => $vendorDir . '/phpunit/phpunit/src/Event/Exception/NoDataSetFromDataProviderException.php',
    'PHPUnit\\Event\\TestData\\TestData' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Test/TestData/TestData.php',
    'PHPUnit\\Event\\TestData\\TestDataCollection' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Test/TestData/TestDataCollection.php',
    'PHPUnit\\Event\\TestData\\TestDataCollectionIterator' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Test/TestData/TestDataCollectionIterator.php',
    'PHPUnit\\Event\\TestRunner\\BootstrapFinished' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/BootstrapFinished.php',
    'PHPUnit\\Event\\TestRunner\\BootstrapFinishedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/BootstrapFinishedSubscriber.php',
    'PHPUnit\\Event\\TestRunner\\ChildProcessFinished' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/ChildProcessFinished.php',
    'PHPUnit\\Event\\TestRunner\\ChildProcessFinishedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/ChildProcessFinishedSubscriber.php',
    'PHPUnit\\Event\\TestRunner\\ChildProcessStarted' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/ChildProcessStarted.php',
    'PHPUnit\\Event\\TestRunner\\ChildProcessStartedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/ChildProcessStartedSubscriber.php',
    'PHPUnit\\Event\\TestRunner\\Configured' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/Configured.php',
    'PHPUnit\\Event\\TestRunner\\ConfiguredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/ConfiguredSubscriber.php',
    'PHPUnit\\Event\\TestRunner\\DeprecationTriggered' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/DeprecationTriggered.php',
    'PHPUnit\\Event\\TestRunner\\DeprecationTriggeredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/DeprecationTriggeredSubscriber.php',
    'PHPUnit\\Event\\TestRunner\\EventFacadeSealed' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/EventFacadeSealed.php',
    'PHPUnit\\Event\\TestRunner\\EventFacadeSealedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/EventFacadeSealedSubscriber.php',
    'PHPUnit\\Event\\TestRunner\\ExecutionAborted' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/ExecutionAborted.php',
    'PHPUnit\\Event\\TestRunner\\ExecutionAbortedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/ExecutionAbortedSubscriber.php',
    'PHPUnit\\Event\\TestRunner\\ExecutionFinished' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/ExecutionFinished.php',
    'PHPUnit\\Event\\TestRunner\\ExecutionFinishedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/ExecutionFinishedSubscriber.php',
    'PHPUnit\\Event\\TestRunner\\ExecutionStarted' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/ExecutionStarted.php',
    'PHPUnit\\Event\\TestRunner\\ExecutionStartedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/ExecutionStartedSubscriber.php',
    'PHPUnit\\Event\\TestRunner\\ExtensionBootstrapped' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/ExtensionBootstrapped.php',
    'PHPUnit\\Event\\TestRunner\\ExtensionBootstrappedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/ExtensionBootstrappedSubscriber.php',
    'PHPUnit\\Event\\TestRunner\\ExtensionLoadedFromPhar' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/ExtensionLoadedFromPhar.php',
    'PHPUnit\\Event\\TestRunner\\ExtensionLoadedFromPharSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/ExtensionLoadedFromPharSubscriber.php',
    'PHPUnit\\Event\\TestRunner\\Finished' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/Finished.php',
    'PHPUnit\\Event\\TestRunner\\FinishedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/FinishedSubscriber.php',
    'PHPUnit\\Event\\TestRunner\\GarbageCollectionDisabled' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/GarbageCollectionDisabled.php',
    'PHPUnit\\Event\\TestRunner\\GarbageCollectionDisabledSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/GarbageCollectionDisabledSubscriber.php',
    'PHPUnit\\Event\\TestRunner\\GarbageCollectionEnabled' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/GarbageCollectionEnabled.php',
    'PHPUnit\\Event\\TestRunner\\GarbageCollectionEnabledSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/GarbageCollectionEnabledSubscriber.php',
    'PHPUnit\\Event\\TestRunner\\GarbageCollectionTriggered' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/GarbageCollectionTriggered.php',
    'PHPUnit\\Event\\TestRunner\\GarbageCollectionTriggeredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/GarbageCollectionTriggeredSubscriber.php',
    'PHPUnit\\Event\\TestRunner\\Started' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/Started.php',
    'PHPUnit\\Event\\TestRunner\\StartedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/StartedSubscriber.php',
    'PHPUnit\\Event\\TestRunner\\WarningTriggered' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/WarningTriggered.php',
    'PHPUnit\\Event\\TestRunner\\WarningTriggeredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/WarningTriggeredSubscriber.php',
    'PHPUnit\\Event\\TestSuite\\Filtered' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestSuite/Filtered.php',
    'PHPUnit\\Event\\TestSuite\\FilteredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestSuite/FilteredSubscriber.php',
    'PHPUnit\\Event\\TestSuite\\Finished' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestSuite/Finished.php',
    'PHPUnit\\Event\\TestSuite\\FinishedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestSuite/FinishedSubscriber.php',
    'PHPUnit\\Event\\TestSuite\\Loaded' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestSuite/Loaded.php',
    'PHPUnit\\Event\\TestSuite\\LoadedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestSuite/LoadedSubscriber.php',
    'PHPUnit\\Event\\TestSuite\\Skipped' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestSuite/Skipped.php',
    'PHPUnit\\Event\\TestSuite\\SkippedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestSuite/SkippedSubscriber.php',
    'PHPUnit\\Event\\TestSuite\\Sorted' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestSuite/Sorted.php',
    'PHPUnit\\Event\\TestSuite\\SortedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestSuite/SortedSubscriber.php',
    'PHPUnit\\Event\\TestSuite\\Started' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestSuite/Started.php',
    'PHPUnit\\Event\\TestSuite\\StartedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestSuite/StartedSubscriber.php',
    'PHPUnit\\Event\\TestSuite\\TestSuite' => $vendorDir . '/phpunit/phpunit/src/Event/Value/TestSuite/TestSuite.php',
    'PHPUnit\\Event\\TestSuite\\TestSuiteBuilder' => $vendorDir . '/phpunit/phpunit/src/Event/Value/TestSuite/TestSuiteBuilder.php',
    'PHPUnit\\Event\\TestSuite\\TestSuiteForTestClass' => $vendorDir . '/phpunit/phpunit/src/Event/Value/TestSuite/TestSuiteForTestClass.php',
    'PHPUnit\\Event\\TestSuite\\TestSuiteForTestMethodWithDataProvider' => $vendorDir . '/phpunit/phpunit/src/Event/Value/TestSuite/TestSuiteForTestMethodWithDataProvider.php',
    'PHPUnit\\Event\\TestSuite\\TestSuiteWithName' => $vendorDir . '/phpunit/phpunit/src/Event/Value/TestSuite/TestSuiteWithName.php',
    'PHPUnit\\Event\\Test\\AfterLastTestMethodCalled' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/AfterLastTestMethodCalled.php',
    'PHPUnit\\Event\\Test\\AfterLastTestMethodCalledSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/AfterLastTestMethodCalledSubscriber.php',
    'PHPUnit\\Event\\Test\\AfterLastTestMethodErrored' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/AfterLastTestMethodErrored.php',
    'PHPUnit\\Event\\Test\\AfterLastTestMethodErroredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/AfterLastTestMethodErroredSubscriber.php',
    'PHPUnit\\Event\\Test\\AfterLastTestMethodFinished' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/AfterLastTestMethodFinished.php',
    'PHPUnit\\Event\\Test\\AfterLastTestMethodFinishedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/AfterLastTestMethodFinishedSubscriber.php',
    'PHPUnit\\Event\\Test\\AfterTestMethodCalled' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/AfterTestMethodCalled.php',
    'PHPUnit\\Event\\Test\\AfterTestMethodCalledSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/AfterTestMethodCalledSubscriber.php',
    'PHPUnit\\Event\\Test\\AfterTestMethodErrored' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/AfterTestMethodErrored.php',
    'PHPUnit\\Event\\Test\\AfterTestMethodErroredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/AfterTestMethodErroredSubscriber.php',
    'PHPUnit\\Event\\Test\\AfterTestMethodFinished' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/AfterTestMethodFinished.php',
    'PHPUnit\\Event\\Test\\AfterTestMethodFinishedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/AfterTestMethodFinishedSubscriber.php',
    'PHPUnit\\Event\\Test\\BeforeFirstTestMethodCalled' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/BeforeFirstTestMethodCalled.php',
    'PHPUnit\\Event\\Test\\BeforeFirstTestMethodCalledSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/BeforeFirstTestMethodCalledSubscriber.php',
    'PHPUnit\\Event\\Test\\BeforeFirstTestMethodErrored' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/BeforeFirstTestMethodErrored.php',
    'PHPUnit\\Event\\Test\\BeforeFirstTestMethodErroredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/BeforeFirstTestMethodErroredSubscriber.php',
    'PHPUnit\\Event\\Test\\BeforeFirstTestMethodFinished' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/BeforeFirstTestMethodFinished.php',
    'PHPUnit\\Event\\Test\\BeforeFirstTestMethodFinishedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/BeforeFirstTestMethodFinishedSubscriber.php',
    'PHPUnit\\Event\\Test\\BeforeTestMethodCalled' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/BeforeTestMethodCalled.php',
    'PHPUnit\\Event\\Test\\BeforeTestMethodCalledSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/BeforeTestMethodCalledSubscriber.php',
    'PHPUnit\\Event\\Test\\BeforeTestMethodErrored' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/BeforeTestMethodErrored.php',
    'PHPUnit\\Event\\Test\\BeforeTestMethodErroredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/BeforeTestMethodErroredSubscriber.php',
    'PHPUnit\\Event\\Test\\BeforeTestMethodFinished' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/BeforeTestMethodFinished.php',
    'PHPUnit\\Event\\Test\\BeforeTestMethodFinishedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/BeforeTestMethodFinishedSubscriber.php',
    'PHPUnit\\Event\\Test\\ComparatorRegistered' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/ComparatorRegistered.php',
    'PHPUnit\\Event\\Test\\ComparatorRegisteredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/ComparatorRegisteredSubscriber.php',
    'PHPUnit\\Event\\Test\\ConsideredRisky' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Issue/ConsideredRisky.php',
    'PHPUnit\\Event\\Test\\ConsideredRiskySubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Issue/ConsideredRiskySubscriber.php',
    'PHPUnit\\Event\\Test\\DataProviderMethodCalled' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Lifecycle/DataProviderMethodCalled.php',
    'PHPUnit\\Event\\Test\\DataProviderMethodCalledSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Lifecycle/DataProviderMethodCalledSubscriber.php',
    'PHPUnit\\Event\\Test\\DataProviderMethodFinished' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Lifecycle/DataProviderMethodFinished.php',
    'PHPUnit\\Event\\Test\\DataProviderMethodFinishedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Lifecycle/DataProviderMethodFinishedSubscriber.php',
    'PHPUnit\\Event\\Test\\DeprecationTriggered' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Issue/DeprecationTriggered.php',
    'PHPUnit\\Event\\Test\\DeprecationTriggeredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Issue/DeprecationTriggeredSubscriber.php',
    'PHPUnit\\Event\\Test\\ErrorTriggered' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Issue/ErrorTriggered.php',
    'PHPUnit\\Event\\Test\\ErrorTriggeredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Issue/ErrorTriggeredSubscriber.php',
    'PHPUnit\\Event\\Test\\Errored' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Outcome/Errored.php',
    'PHPUnit\\Event\\Test\\ErroredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Outcome/ErroredSubscriber.php',
    'PHPUnit\\Event\\Test\\Failed' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Outcome/Failed.php',
    'PHPUnit\\Event\\Test\\FailedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Outcome/FailedSubscriber.php',
    'PHPUnit\\Event\\Test\\Finished' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Lifecycle/Finished.php',
    'PHPUnit\\Event\\Test\\FinishedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Lifecycle/FinishedSubscriber.php',
    'PHPUnit\\Event\\Test\\MarkedIncomplete' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Outcome/MarkedIncomplete.php',
    'PHPUnit\\Event\\Test\\MarkedIncompleteSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Outcome/MarkedIncompleteSubscriber.php',
    'PHPUnit\\Event\\Test\\MockObjectCreated' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/TestDouble/MockObjectCreated.php',
    'PHPUnit\\Event\\Test\\MockObjectCreatedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/TestDouble/MockObjectCreatedSubscriber.php',
    'PHPUnit\\Event\\Test\\MockObjectForAbstractClassCreated' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/TestDouble/MockObjectForAbstractClassCreated.php',
    'PHPUnit\\Event\\Test\\MockObjectForAbstractClassCreatedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/TestDouble/MockObjectForAbstractClassCreatedSubscriber.php',
    'PHPUnit\\Event\\Test\\MockObjectForIntersectionOfInterfacesCreated' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/TestDouble/MockObjectForIntersectionOfInterfacesCreated.php',
    'PHPUnit\\Event\\Test\\MockObjectForIntersectionOfInterfacesCreatedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/TestDouble/MockObjectForIntersectionOfInterfacesCreatedSubscriber.php',
    'PHPUnit\\Event\\Test\\MockObjectForTraitCreated' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/TestDouble/MockObjectForTraitCreated.php',
    'PHPUnit\\Event\\Test\\MockObjectForTraitCreatedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/TestDouble/MockObjectForTraitCreatedSubscriber.php',
    'PHPUnit\\Event\\Test\\MockObjectFromWsdlCreated' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/TestDouble/MockObjectFromWsdlCreated.php',
    'PHPUnit\\Event\\Test\\MockObjectFromWsdlCreatedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/TestDouble/MockObjectFromWsdlCreatedSubscriber.php',
    'PHPUnit\\Event\\Test\\NoComparisonFailureException' => $vendorDir . '/phpunit/phpunit/src/Event/Exception/NoComparisonFailureException.php',
    'PHPUnit\\Event\\Test\\NoticeTriggered' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Issue/NoticeTriggered.php',
    'PHPUnit\\Event\\Test\\NoticeTriggeredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Issue/NoticeTriggeredSubscriber.php',
    'PHPUnit\\Event\\Test\\PartialMockObjectCreated' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/TestDouble/PartialMockObjectCreated.php',
    'PHPUnit\\Event\\Test\\PartialMockObjectCreatedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/TestDouble/PartialMockObjectCreatedSubscriber.php',
    'PHPUnit\\Event\\Test\\Passed' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Outcome/Passed.php',
    'PHPUnit\\Event\\Test\\PassedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Outcome/PassedSubscriber.php',
    'PHPUnit\\Event\\Test\\PhpDeprecationTriggered' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Issue/PhpDeprecationTriggered.php',
    'PHPUnit\\Event\\Test\\PhpDeprecationTriggeredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Issue/PhpDeprecationTriggeredSubscriber.php',
    'PHPUnit\\Event\\Test\\PhpNoticeTriggered' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Issue/PhpNoticeTriggered.php',
    'PHPUnit\\Event\\Test\\PhpNoticeTriggeredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Issue/PhpNoticeTriggeredSubscriber.php',
    'PHPUnit\\Event\\Test\\PhpWarningTriggered' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Issue/PhpWarningTriggered.php',
    'PHPUnit\\Event\\Test\\PhpWarningTriggeredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Issue/PhpWarningTriggeredSubscriber.php',
    'PHPUnit\\Event\\Test\\PhpunitDeprecationTriggered' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Issue/PhpunitDeprecationTriggered.php',
    'PHPUnit\\Event\\Test\\PhpunitDeprecationTriggeredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Issue/PhpunitDeprecationTriggeredSubscriber.php',
    'PHPUnit\\Event\\Test\\PhpunitErrorTriggered' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Issue/PhpunitErrorTriggered.php',
    'PHPUnit\\Event\\Test\\PhpunitErrorTriggeredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Issue/PhpunitErrorTriggeredSubscriber.php',
    'PHPUnit\\Event\\Test\\PhpunitWarningTriggered' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Issue/PhpunitWarningTriggered.php',
    'PHPUnit\\Event\\Test\\PhpunitWarningTriggeredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Issue/PhpunitWarningTriggeredSubscriber.php',
    'PHPUnit\\Event\\Test\\PostConditionCalled' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/PostConditionCalled.php',
    'PHPUnit\\Event\\Test\\PostConditionCalledSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/PostConditionCalledSubscriber.php',
    'PHPUnit\\Event\\Test\\PostConditionErrored' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/PostConditionErrored.php',
    'PHPUnit\\Event\\Test\\PostConditionErroredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/PostConditionErroredSubscriber.php',
    'PHPUnit\\Event\\Test\\PostConditionFinished' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/PostConditionFinished.php',
    'PHPUnit\\Event\\Test\\PostConditionFinishedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/PostConditionFinishedSubscriber.php',
    'PHPUnit\\Event\\Test\\PreConditionCalled' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/PreConditionCalled.php',
    'PHPUnit\\Event\\Test\\PreConditionCalledSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/PreConditionCalledSubscriber.php',
    'PHPUnit\\Event\\Test\\PreConditionErrored' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/PreConditionErrored.php',
    'PHPUnit\\Event\\Test\\PreConditionErroredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/PreConditionErroredSubscriber.php',
    'PHPUnit\\Event\\Test\\PreConditionFinished' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/PreConditionFinished.php',
    'PHPUnit\\Event\\Test\\PreConditionFinishedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/PreConditionFinishedSubscriber.php',
    'PHPUnit\\Event\\Test\\PreparationFailed' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Lifecycle/PreparationFailed.php',
    'PHPUnit\\Event\\Test\\PreparationFailedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Lifecycle/PreparationFailedSubscriber.php',
    'PHPUnit\\Event\\Test\\PreparationStarted' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Lifecycle/PreparationStarted.php',
    'PHPUnit\\Event\\Test\\PreparationStartedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Lifecycle/PreparationStartedSubscriber.php',
    'PHPUnit\\Event\\Test\\Prepared' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Lifecycle/Prepared.php',
    'PHPUnit\\Event\\Test\\PreparedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Lifecycle/PreparedSubscriber.php',
    'PHPUnit\\Event\\Test\\PrintedUnexpectedOutput' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/PrintedUnexpectedOutput.php',
    'PHPUnit\\Event\\Test\\PrintedUnexpectedOutputSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/PrintedUnexpectedOutputSubscriber.php',
    'PHPUnit\\Event\\Test\\Skipped' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Outcome/Skipped.php',
    'PHPUnit\\Event\\Test\\SkippedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Outcome/SkippedSubscriber.php',
    'PHPUnit\\Event\\Test\\TestProxyCreated' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/TestDouble/TestProxyCreated.php',
    'PHPUnit\\Event\\Test\\TestProxyCreatedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/TestDouble/TestProxyCreatedSubscriber.php',
    'PHPUnit\\Event\\Test\\TestStubCreated' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/TestDouble/TestStubCreated.php',
    'PHPUnit\\Event\\Test\\TestStubCreatedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/TestDouble/TestStubCreatedSubscriber.php',
    'PHPUnit\\Event\\Test\\TestStubForIntersectionOfInterfacesCreated' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/TestDouble/TestStubForIntersectionOfInterfacesCreated.php',
    'PHPUnit\\Event\\Test\\TestStubForIntersectionOfInterfacesCreatedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/TestDouble/TestStubForIntersectionOfInterfacesCreatedSubscriber.php',
    'PHPUnit\\Event\\Test\\WarningTriggered' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Issue/WarningTriggered.php',
    'PHPUnit\\Event\\Test\\WarningTriggeredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Issue/WarningTriggeredSubscriber.php',
    'PHPUnit\\Event\\Tracer\\Tracer' => $vendorDir . '/phpunit/phpunit/src/Event/Tracer.php',
    'PHPUnit\\Event\\TypeMap' => $vendorDir . '/phpunit/phpunit/src/Event/TypeMap.php',
    'PHPUnit\\Event\\UnknownEventException' => $vendorDir . '/phpunit/phpunit/src/Event/Exception/UnknownEventException.php',
    'PHPUnit\\Event\\UnknownEventTypeException' => $vendorDir . '/phpunit/phpunit/src/Event/Exception/UnknownEventTypeException.php',
    'PHPUnit\\Event\\UnknownSubscriberException' => $vendorDir . '/phpunit/phpunit/src/Event/Exception/UnknownSubscriberException.php',
    'PHPUnit\\Event\\UnknownSubscriberTypeException' => $vendorDir . '/phpunit/phpunit/src/Event/Exception/UnknownSubscriberTypeException.php',
    'PHPUnit\\Exception' => $vendorDir . '/phpunit/phpunit/src/Exception.php',
    'PHPUnit\\Framework\\ActualValueIsNotAnObjectException' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/ObjectEquals/ActualValueIsNotAnObjectException.php',
    'PHPUnit\\Framework\\Assert' => $vendorDir . '/phpunit/phpunit/src/Framework/Assert.php',
    'PHPUnit\\Framework\\AssertionFailedError' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/AssertionFailedError.php',
    'PHPUnit\\Framework\\Attributes\\After' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/After.php',
    'PHPUnit\\Framework\\Attributes\\AfterClass' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/AfterClass.php',
    'PHPUnit\\Framework\\Attributes\\BackupGlobals' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/BackupGlobals.php',
    'PHPUnit\\Framework\\Attributes\\BackupStaticProperties' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/BackupStaticProperties.php',
    'PHPUnit\\Framework\\Attributes\\Before' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/Before.php',
    'PHPUnit\\Framework\\Attributes\\BeforeClass' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/BeforeClass.php',
    'PHPUnit\\Framework\\Attributes\\CoversClass' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/CoversClass.php',
    'PHPUnit\\Framework\\Attributes\\CoversFunction' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/CoversFunction.php',
    'PHPUnit\\Framework\\Attributes\\CoversMethod' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/CoversMethod.php',
    'PHPUnit\\Framework\\Attributes\\CoversNothing' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/CoversNothing.php',
    'PHPUnit\\Framework\\Attributes\\CoversTrait' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/CoversTrait.php',
    'PHPUnit\\Framework\\Attributes\\DataProvider' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/DataProvider.php',
    'PHPUnit\\Framework\\Attributes\\DataProviderExternal' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/DataProviderExternal.php',
    'PHPUnit\\Framework\\Attributes\\Depends' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/Depends.php',
    'PHPUnit\\Framework\\Attributes\\DependsExternal' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/DependsExternal.php',
    'PHPUnit\\Framework\\Attributes\\DependsExternalUsingDeepClone' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/DependsExternalUsingDeepClone.php',
    'PHPUnit\\Framework\\Attributes\\DependsExternalUsingShallowClone' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/DependsExternalUsingShallowClone.php',
    'PHPUnit\\Framework\\Attributes\\DependsOnClass' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/DependsOnClass.php',
    'PHPUnit\\Framework\\Attributes\\DependsOnClassUsingDeepClone' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/DependsOnClassUsingDeepClone.php',
    'PHPUnit\\Framework\\Attributes\\DependsOnClassUsingShallowClone' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/DependsOnClassUsingShallowClone.php',
    'PHPUnit\\Framework\\Attributes\\DependsUsingDeepClone' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/DependsUsingDeepClone.php',
    'PHPUnit\\Framework\\Attributes\\DependsUsingShallowClone' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/DependsUsingShallowClone.php',
    'PHPUnit\\Framework\\Attributes\\DisableReturnValueGenerationForTestDoubles' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/DisableReturnValueGenerationForTestDoubles.php',
    'PHPUnit\\Framework\\Attributes\\DoesNotPerformAssertions' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/DoesNotPerformAssertions.php',
    'PHPUnit\\Framework\\Attributes\\ExcludeGlobalVariableFromBackup' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/ExcludeGlobalVariableFromBackup.php',
    'PHPUnit\\Framework\\Attributes\\ExcludeStaticPropertyFromBackup' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/ExcludeStaticPropertyFromBackup.php',
    'PHPUnit\\Framework\\Attributes\\Group' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/Group.php',
    'PHPUnit\\Framework\\Attributes\\IgnoreDeprecations' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/IgnoreDeprecations.php',
    'PHPUnit\\Framework\\Attributes\\IgnorePhpunitDeprecations' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/IgnorePhpunitDeprecations.php',
    'PHPUnit\\Framework\\Attributes\\Large' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/Large.php',
    'PHPUnit\\Framework\\Attributes\\Medium' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/Medium.php',
    'PHPUnit\\Framework\\Attributes\\PostCondition' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/PostCondition.php',
    'PHPUnit\\Framework\\Attributes\\PreCondition' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/PreCondition.php',
    'PHPUnit\\Framework\\Attributes\\PreserveGlobalState' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/PreserveGlobalState.php',
    'PHPUnit\\Framework\\Attributes\\RequiresFunction' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/RequiresFunction.php',
    'PHPUnit\\Framework\\Attributes\\RequiresMethod' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/RequiresMethod.php',
    'PHPUnit\\Framework\\Attributes\\RequiresOperatingSystem' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/RequiresOperatingSystem.php',
    'PHPUnit\\Framework\\Attributes\\RequiresOperatingSystemFamily' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/RequiresOperatingSystemFamily.php',
    'PHPUnit\\Framework\\Attributes\\RequiresPhp' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/RequiresPhp.php',
    'PHPUnit\\Framework\\Attributes\\RequiresPhpExtension' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/RequiresPhpExtension.php',
    'PHPUnit\\Framework\\Attributes\\RequiresPhpunit' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/RequiresPhpunit.php',
    'PHPUnit\\Framework\\Attributes\\RequiresPhpunitExtension' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/RequiresPhpunitExtension.php',
    'PHPUnit\\Framework\\Attributes\\RequiresSetting' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/RequiresSetting.php',
    'PHPUnit\\Framework\\Attributes\\RunClassInSeparateProcess' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/RunClassInSeparateProcess.php',
    'PHPUnit\\Framework\\Attributes\\RunInSeparateProcess' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/RunInSeparateProcess.php',
    'PHPUnit\\Framework\\Attributes\\RunTestsInSeparateProcesses' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/RunTestsInSeparateProcesses.php',
    'PHPUnit\\Framework\\Attributes\\Small' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/Small.php',
    'PHPUnit\\Framework\\Attributes\\Test' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/Test.php',
    'PHPUnit\\Framework\\Attributes\\TestDox' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/TestDox.php',
    'PHPUnit\\Framework\\Attributes\\TestWith' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/TestWith.php',
    'PHPUnit\\Framework\\Attributes\\TestWithJson' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/TestWithJson.php',
    'PHPUnit\\Framework\\Attributes\\Ticket' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/Ticket.php',
    'PHPUnit\\Framework\\Attributes\\UsesClass' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/UsesClass.php',
    'PHPUnit\\Framework\\Attributes\\UsesFunction' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/UsesFunction.php',
    'PHPUnit\\Framework\\Attributes\\UsesMethod' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/UsesMethod.php',
    'PHPUnit\\Framework\\Attributes\\UsesTrait' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/UsesTrait.php',
    'PHPUnit\\Framework\\Attributes\\WithoutErrorHandler' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/WithoutErrorHandler.php',
    'PHPUnit\\Framework\\ChildProcessResultProcessor' => $vendorDir . '/phpunit/phpunit/src/Framework/TestRunner/ChildProcessResultProcessor.php',
    'PHPUnit\\Framework\\CodeCoverageException' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/CodeCoverageException.php',
    'PHPUnit\\Framework\\ComparisonMethodDoesNotAcceptParameterTypeException' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/ObjectEquals/ComparisonMethodDoesNotAcceptParameterTypeException.php',
    'PHPUnit\\Framework\\ComparisonMethodDoesNotDeclareBoolReturnTypeException' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/ObjectEquals/ComparisonMethodDoesNotDeclareBoolReturnTypeException.php',
    'PHPUnit\\Framework\\ComparisonMethodDoesNotDeclareExactlyOneParameterException' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/ObjectEquals/ComparisonMethodDoesNotDeclareExactlyOneParameterException.php',
    'PHPUnit\\Framework\\ComparisonMethodDoesNotDeclareParameterTypeException' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/ObjectEquals/ComparisonMethodDoesNotDeclareParameterTypeException.php',
    'PHPUnit\\Framework\\ComparisonMethodDoesNotExistException' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/ObjectEquals/ComparisonMethodDoesNotExistException.php',
    'PHPUnit\\Framework\\Constraint\\ArrayHasKey' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Traversable/ArrayHasKey.php',
    'PHPUnit\\Framework\\Constraint\\BinaryOperator' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Operator/BinaryOperator.php',
    'PHPUnit\\Framework\\Constraint\\Callback' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Callback.php',
    'PHPUnit\\Framework\\Constraint\\Constraint' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Constraint.php',
    'PHPUnit\\Framework\\Constraint\\Count' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Cardinality/Count.php',
    'PHPUnit\\Framework\\Constraint\\DirectoryExists' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Filesystem/DirectoryExists.php',
    'PHPUnit\\Framework\\Constraint\\Exception' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Exception/Exception.php',
    'PHPUnit\\Framework\\Constraint\\ExceptionCode' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Exception/ExceptionCode.php',
    'PHPUnit\\Framework\\Constraint\\ExceptionMessageIsOrContains' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Exception/ExceptionMessageIsOrContains.php',
    'PHPUnit\\Framework\\Constraint\\ExceptionMessageMatchesRegularExpression' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Exception/ExceptionMessageMatchesRegularExpression.php',
    'PHPUnit\\Framework\\Constraint\\FileExists' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Filesystem/FileExists.php',
    'PHPUnit\\Framework\\Constraint\\GreaterThan' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Cardinality/GreaterThan.php',
    'PHPUnit\\Framework\\Constraint\\IsAnything' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/IsAnything.php',
    'PHPUnit\\Framework\\Constraint\\IsEmpty' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Cardinality/IsEmpty.php',
    'PHPUnit\\Framework\\Constraint\\IsEqual' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Equality/IsEqual.php',
    'PHPUnit\\Framework\\Constraint\\IsEqualCanonicalizing' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Equality/IsEqualCanonicalizing.php',
    'PHPUnit\\Framework\\Constraint\\IsEqualIgnoringCase' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Equality/IsEqualIgnoringCase.php',
    'PHPUnit\\Framework\\Constraint\\IsEqualWithDelta' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Equality/IsEqualWithDelta.php',
    'PHPUnit\\Framework\\Constraint\\IsFalse' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Boolean/IsFalse.php',
    'PHPUnit\\Framework\\Constraint\\IsFinite' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Math/IsFinite.php',
    'PHPUnit\\Framework\\Constraint\\IsIdentical' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/IsIdentical.php',
    'PHPUnit\\Framework\\Constraint\\IsInfinite' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Math/IsInfinite.php',
    'PHPUnit\\Framework\\Constraint\\IsInstanceOf' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Type/IsInstanceOf.php',
    'PHPUnit\\Framework\\Constraint\\IsJson' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/String/IsJson.php',
    'PHPUnit\\Framework\\Constraint\\IsList' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Traversable/IsList.php',
    'PHPUnit\\Framework\\Constraint\\IsNan' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Math/IsNan.php',
    'PHPUnit\\Framework\\Constraint\\IsNull' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Type/IsNull.php',
    'PHPUnit\\Framework\\Constraint\\IsReadable' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Filesystem/IsReadable.php',
    'PHPUnit\\Framework\\Constraint\\IsTrue' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Boolean/IsTrue.php',
    'PHPUnit\\Framework\\Constraint\\IsType' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Type/IsType.php',
    'PHPUnit\\Framework\\Constraint\\IsWritable' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Filesystem/IsWritable.php',
    'PHPUnit\\Framework\\Constraint\\JsonMatches' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/JsonMatches.php',
    'PHPUnit\\Framework\\Constraint\\LessThan' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Cardinality/LessThan.php',
    'PHPUnit\\Framework\\Constraint\\LogicalAnd' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Operator/LogicalAnd.php',
    'PHPUnit\\Framework\\Constraint\\LogicalNot' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Operator/LogicalNot.php',
    'PHPUnit\\Framework\\Constraint\\LogicalOr' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Operator/LogicalOr.php',
    'PHPUnit\\Framework\\Constraint\\LogicalXor' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Operator/LogicalXor.php',
    'PHPUnit\\Framework\\Constraint\\ObjectEquals' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Object/ObjectEquals.php',
    'PHPUnit\\Framework\\Constraint\\ObjectHasProperty' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Object/ObjectHasProperty.php',
    'PHPUnit\\Framework\\Constraint\\Operator' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Operator/Operator.php',
    'PHPUnit\\Framework\\Constraint\\RegularExpression' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/String/RegularExpression.php',
    'PHPUnit\\Framework\\Constraint\\SameSize' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Cardinality/SameSize.php',
    'PHPUnit\\Framework\\Constraint\\StringContains' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/String/StringContains.php',
    'PHPUnit\\Framework\\Constraint\\StringEndsWith' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/String/StringEndsWith.php',
    'PHPUnit\\Framework\\Constraint\\StringEqualsStringIgnoringLineEndings' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/String/StringEqualsStringIgnoringLineEndings.php',
    'PHPUnit\\Framework\\Constraint\\StringMatchesFormatDescription' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/String/StringMatchesFormatDescription.php',
    'PHPUnit\\Framework\\Constraint\\StringStartsWith' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/String/StringStartsWith.php',
    'PHPUnit\\Framework\\Constraint\\TraversableContains' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Traversable/TraversableContains.php',
    'PHPUnit\\Framework\\Constraint\\TraversableContainsEqual' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Traversable/TraversableContainsEqual.php',
    'PHPUnit\\Framework\\Constraint\\TraversableContainsIdentical' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Traversable/TraversableContainsIdentical.php',
    'PHPUnit\\Framework\\Constraint\\TraversableContainsOnly' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Traversable/TraversableContainsOnly.php',
    'PHPUnit\\Framework\\Constraint\\UnaryOperator' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Operator/UnaryOperator.php',
    'PHPUnit\\Framework\\DataProviderTestSuite' => $vendorDir . '/phpunit/phpunit/src/Framework/DataProviderTestSuite.php',
    'PHPUnit\\Framework\\EmptyStringException' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/EmptyStringException.php',
    'PHPUnit\\Framework\\Exception' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/Exception.php',
    'PHPUnit\\Framework\\ExecutionOrderDependency' => $vendorDir . '/phpunit/phpunit/src/Framework/ExecutionOrderDependency.php',
    'PHPUnit\\Framework\\ExpectationFailedException' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/ExpectationFailedException.php',
    'PHPUnit\\Framework\\GeneratorNotSupportedException' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/GeneratorNotSupportedException.php',
    'PHPUnit\\Framework\\IncompleteTest' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/Incomplete/IncompleteTest.php',
    'PHPUnit\\Framework\\IncompleteTestError' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/Incomplete/IncompleteTestError.php',
    'PHPUnit\\Framework\\InvalidArgumentException' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/InvalidArgumentException.php',
    'PHPUnit\\Framework\\InvalidCoversTargetException' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/InvalidCoversTargetException.php',
    'PHPUnit\\Framework\\InvalidDataProviderException' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/InvalidDataProviderException.php',
    'PHPUnit\\Framework\\InvalidDependencyException' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/InvalidDependencyException.php',
    'PHPUnit\\Framework\\IsolatedTestRunner' => $vendorDir . '/phpunit/phpunit/src/Framework/TestRunner/IsolatedTestRunner.php',
    'PHPUnit\\Framework\\IsolatedTestRunnerRegistry' => $vendorDir . '/phpunit/phpunit/src/Framework/TestRunner/IsolatedTestRunnerRegistry.php',
    'PHPUnit\\Framework\\MockObject\\BadMethodCallException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Exception/BadMethodCallException.php',
    'PHPUnit\\Framework\\MockObject\\Builder\\Identity' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Builder/Identity.php',
    'PHPUnit\\Framework\\MockObject\\Builder\\InvocationMocker' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Builder/InvocationMocker.php',
    'PHPUnit\\Framework\\MockObject\\Builder\\InvocationStubber' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Builder/InvocationStubber.php',
    'PHPUnit\\Framework\\MockObject\\Builder\\MethodNameMatch' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Builder/MethodNameMatch.php',
    'PHPUnit\\Framework\\MockObject\\Builder\\ParametersMatch' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Builder/ParametersMatch.php',
    'PHPUnit\\Framework\\MockObject\\Builder\\Stub' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Builder/Stub.php',
    'PHPUnit\\Framework\\MockObject\\CannotCloneTestDoubleForReadonlyClassException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Exception/CannotCloneTestDoubleForReadonlyClassException.php',
    'PHPUnit\\Framework\\MockObject\\CannotUseOnlyMethodsException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Exception/CannotUseOnlyMethodsException.php',
    'PHPUnit\\Framework\\MockObject\\ConfigurableMethod' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/ConfigurableMethod.php',
    'PHPUnit\\Framework\\MockObject\\DoubledCloneMethod' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Api/DoubledCloneMethod.php',
    'PHPUnit\\Framework\\MockObject\\ErrorCloneMethod' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Api/ErrorCloneMethod.php',
    'PHPUnit\\Framework\\MockObject\\Exception' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Exception/Exception.php',
    'PHPUnit\\Framework\\MockObject\\GeneratedAsMockObject' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Api/GeneratedAsMockObject.php',
    'PHPUnit\\Framework\\MockObject\\GeneratedAsTestStub' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Api/GeneratedAsTestStub.php',
    'PHPUnit\\Framework\\MockObject\\Generator\\CannotUseAddMethodsException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Generator/Exception/CannotUseAddMethodsException.php',
    'PHPUnit\\Framework\\MockObject\\Generator\\ClassIsEnumerationException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Generator/Exception/ClassIsEnumerationException.php',
    'PHPUnit\\Framework\\MockObject\\Generator\\ClassIsFinalException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Generator/Exception/ClassIsFinalException.php',
    'PHPUnit\\Framework\\MockObject\\Generator\\DuplicateMethodException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Generator/Exception/DuplicateMethodException.php',
    'PHPUnit\\Framework\\MockObject\\Generator\\Exception' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Generator/Exception/Exception.php',
    'PHPUnit\\Framework\\MockObject\\Generator\\Generator' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Generator/Generator.php',
    'PHPUnit\\Framework\\MockObject\\Generator\\HookedProperty' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Generator/HookedProperty.php',
    'PHPUnit\\Framework\\MockObject\\Generator\\HookedPropertyGenerator' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Generator/HookedPropertyGenerator.php',
    'PHPUnit\\Framework\\MockObject\\Generator\\InvalidMethodNameException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Generator/Exception/InvalidMethodNameException.php',
    'PHPUnit\\Framework\\MockObject\\Generator\\MockClass' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Generator/MockClass.php',
    'PHPUnit\\Framework\\MockObject\\Generator\\MockMethod' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Generator/MockMethod.php',
    'PHPUnit\\Framework\\MockObject\\Generator\\MockMethodSet' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Generator/MockMethodSet.php',
    'PHPUnit\\Framework\\MockObject\\Generator\\MockTrait' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Generator/MockTrait.php',
    'PHPUnit\\Framework\\MockObject\\Generator\\MockType' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Generator/MockType.php',
    'PHPUnit\\Framework\\MockObject\\Generator\\NameAlreadyInUseException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Generator/Exception/NameAlreadyInUseException.php',
    'PHPUnit\\Framework\\MockObject\\Generator\\OriginalConstructorInvocationRequiredException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Generator/Exception/OriginalConstructorInvocationRequiredException.php',
    'PHPUnit\\Framework\\MockObject\\Generator\\ReflectionException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Generator/Exception/ReflectionException.php',
    'PHPUnit\\Framework\\MockObject\\Generator\\RuntimeException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Generator/Exception/RuntimeException.php',
    'PHPUnit\\Framework\\MockObject\\Generator\\SoapExtensionNotAvailableException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Generator/Exception/SoapExtensionNotAvailableException.php',
    'PHPUnit\\Framework\\MockObject\\Generator\\TemplateLoader' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Generator/TemplateLoader.php',
    'PHPUnit\\Framework\\MockObject\\Generator\\UnknownClassException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Generator/Exception/UnknownClassException.php',
    'PHPUnit\\Framework\\MockObject\\Generator\\UnknownInterfaceException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Generator/Exception/UnknownInterfaceException.php',
    'PHPUnit\\Framework\\MockObject\\Generator\\UnknownTraitException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Generator/Exception/UnknownTraitException.php',
    'PHPUnit\\Framework\\MockObject\\Generator\\UnknownTypeException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Generator/Exception/UnknownTypeException.php',
    'PHPUnit\\Framework\\MockObject\\IncompatibleReturnValueException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Exception/IncompatibleReturnValueException.php',
    'PHPUnit\\Framework\\MockObject\\Invocation' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Invocation.php',
    'PHPUnit\\Framework\\MockObject\\InvocationHandler' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/InvocationHandler.php',
    'PHPUnit\\Framework\\MockObject\\MatchBuilderNotFoundException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Exception/MatchBuilderNotFoundException.php',
    'PHPUnit\\Framework\\MockObject\\Matcher' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Matcher.php',
    'PHPUnit\\Framework\\MockObject\\MatcherAlreadyRegisteredException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Exception/MatcherAlreadyRegisteredException.php',
    'PHPUnit\\Framework\\MockObject\\Method' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Api/Method.php',
    'PHPUnit\\Framework\\MockObject\\MethodCannotBeConfiguredException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Exception/MethodCannotBeConfiguredException.php',
    'PHPUnit\\Framework\\MockObject\\MethodNameAlreadyConfiguredException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Exception/MethodNameAlreadyConfiguredException.php',
    'PHPUnit\\Framework\\MockObject\\MethodNameConstraint' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/MethodNameConstraint.php',
    'PHPUnit\\Framework\\MockObject\\MethodNameNotConfiguredException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Exception/MethodNameNotConfiguredException.php',
    'PHPUnit\\Framework\\MockObject\\MethodParametersAlreadyConfiguredException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Exception/MethodParametersAlreadyConfiguredException.php',
    'PHPUnit\\Framework\\MockObject\\MockBuilder' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/MockBuilder.php',
    'PHPUnit\\Framework\\MockObject\\MockObject' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Interface/MockObject.php',
    'PHPUnit\\Framework\\MockObject\\MockObjectApi' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Api/MockObjectApi.php',
    'PHPUnit\\Framework\\MockObject\\MockObjectInternal' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Interface/MockObjectInternal.php',
    'PHPUnit\\Framework\\MockObject\\MutableStubApi' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Api/MutableStubApi.php',
    'PHPUnit\\Framework\\MockObject\\NeverReturningMethodException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Exception/NeverReturningMethodException.php',
    'PHPUnit\\Framework\\MockObject\\NoMoreReturnValuesConfiguredException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Exception/NoMoreReturnValuesConfiguredException.php',
    'PHPUnit\\Framework\\MockObject\\ProxiedCloneMethod' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Api/ProxiedCloneMethod.php',
    'PHPUnit\\Framework\\MockObject\\ReturnValueGenerator' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/ReturnValueGenerator.php',
    'PHPUnit\\Framework\\MockObject\\ReturnValueNotConfiguredException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Exception/ReturnValueNotConfiguredException.php',
    'PHPUnit\\Framework\\MockObject\\Rule\\AnyInvokedCount' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Rule/AnyInvokedCount.php',
    'PHPUnit\\Framework\\MockObject\\Rule\\AnyParameters' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Rule/AnyParameters.php',
    'PHPUnit\\Framework\\MockObject\\Rule\\InvocationOrder' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Rule/InvocationOrder.php',
    'PHPUnit\\Framework\\MockObject\\Rule\\InvokedAtLeastCount' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Rule/InvokedAtLeastCount.php',
    'PHPUnit\\Framework\\MockObject\\Rule\\InvokedAtLeastOnce' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Rule/InvokedAtLeastOnce.php',
    'PHPUnit\\Framework\\MockObject\\Rule\\InvokedAtMostCount' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Rule/InvokedAtMostCount.php',
    'PHPUnit\\Framework\\MockObject\\Rule\\InvokedCount' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Rule/InvokedCount.php',
    'PHPUnit\\Framework\\MockObject\\Rule\\MethodName' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Rule/MethodName.php',
    'PHPUnit\\Framework\\MockObject\\Rule\\Parameters' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Rule/Parameters.php',
    'PHPUnit\\Framework\\MockObject\\Rule\\ParametersRule' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Rule/ParametersRule.php',
    'PHPUnit\\Framework\\MockObject\\RuntimeException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Exception/RuntimeException.php',
    'PHPUnit\\Framework\\MockObject\\Runtime\\PropertyGetHook' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/PropertyHook/PropertyGetHook.php',
    'PHPUnit\\Framework\\MockObject\\Runtime\\PropertyHook' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/PropertyHook/PropertyHook.php',
    'PHPUnit\\Framework\\MockObject\\Runtime\\PropertySetHook' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/PropertyHook/PropertySetHook.php',
    'PHPUnit\\Framework\\MockObject\\Stub' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Interface/Stub.php',
    'PHPUnit\\Framework\\MockObject\\StubApi' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Api/StubApi.php',
    'PHPUnit\\Framework\\MockObject\\StubInternal' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Interface/StubInternal.php',
    'PHPUnit\\Framework\\MockObject\\Stub\\ConsecutiveCalls' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Stub/ConsecutiveCalls.php',
    'PHPUnit\\Framework\\MockObject\\Stub\\Exception' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Stub/Exception.php',
    'PHPUnit\\Framework\\MockObject\\Stub\\ReturnArgument' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Stub/ReturnArgument.php',
    'PHPUnit\\Framework\\MockObject\\Stub\\ReturnCallback' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Stub/ReturnCallback.php',
    'PHPUnit\\Framework\\MockObject\\Stub\\ReturnReference' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Stub/ReturnReference.php',
    'PHPUnit\\Framework\\MockObject\\Stub\\ReturnSelf' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Stub/ReturnSelf.php',
    'PHPUnit\\Framework\\MockObject\\Stub\\ReturnStub' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Stub/ReturnStub.php',
    'PHPUnit\\Framework\\MockObject\\Stub\\ReturnValueMap' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Stub/ReturnValueMap.php',
    'PHPUnit\\Framework\\MockObject\\Stub\\Stub' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Stub/Stub.php',
    'PHPUnit\\Framework\\MockObject\\TestDoubleState' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Api/TestDoubleState.php',
    'PHPUnit\\Framework\\NativeType' => $vendorDir . '/phpunit/phpunit/src/Framework/NativeType.php',
    'PHPUnit\\Framework\\NoChildTestSuiteException' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/NoChildTestSuiteException.php',
    'PHPUnit\\Framework\\PhptAssertionFailedError' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/PhptAssertionFailedError.php',
    'PHPUnit\\Framework\\ProcessIsolationException' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/ProcessIsolationException.php',
    'PHPUnit\\Framework\\Reorderable' => $vendorDir . '/phpunit/phpunit/src/Framework/Reorderable.php',
    'PHPUnit\\Framework\\SelfDescribing' => $vendorDir . '/phpunit/phpunit/src/Framework/SelfDescribing.php',
    'PHPUnit\\Framework\\SeparateProcessTestRunner' => $vendorDir . '/phpunit/phpunit/src/Framework/TestRunner/SeparateProcessTestRunner.php',
    'PHPUnit\\Framework\\SkippedTest' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/Skipped/SkippedTest.php',
    'PHPUnit\\Framework\\SkippedTestSuiteError' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/Skipped/SkippedTestSuiteError.php',
    'PHPUnit\\Framework\\SkippedWithMessageException' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/Skipped/SkippedWithMessageException.php',
    'PHPUnit\\Framework\\Test' => $vendorDir . '/phpunit/phpunit/src/Framework/Test.php',
    'PHPUnit\\Framework\\TestBuilder' => $vendorDir . '/phpunit/phpunit/src/Framework/TestBuilder.php',
    'PHPUnit\\Framework\\TestCase' => $vendorDir . '/phpunit/phpunit/src/Framework/TestCase.php',
    'PHPUnit\\Framework\\TestRunner' => $vendorDir . '/phpunit/phpunit/src/Framework/TestRunner/TestRunner.php',
    'PHPUnit\\Framework\\TestSize\\Known' => $vendorDir . '/phpunit/phpunit/src/Framework/TestSize/Known.php',
    'PHPUnit\\Framework\\TestSize\\Large' => $vendorDir . '/phpunit/phpunit/src/Framework/TestSize/Large.php',
    'PHPUnit\\Framework\\TestSize\\Medium' => $vendorDir . '/phpunit/phpunit/src/Framework/TestSize/Medium.php',
    'PHPUnit\\Framework\\TestSize\\Small' => $vendorDir . '/phpunit/phpunit/src/Framework/TestSize/Small.php',
    'PHPUnit\\Framework\\TestSize\\TestSize' => $vendorDir . '/phpunit/phpunit/src/Framework/TestSize/TestSize.php',
    'PHPUnit\\Framework\\TestSize\\Unknown' => $vendorDir . '/phpunit/phpunit/src/Framework/TestSize/Unknown.php',
    'PHPUnit\\Framework\\TestStatus\\Deprecation' => $vendorDir . '/phpunit/phpunit/src/Framework/TestStatus/Deprecation.php',
    'PHPUnit\\Framework\\TestStatus\\Error' => $vendorDir . '/phpunit/phpunit/src/Framework/TestStatus/Error.php',
    'PHPUnit\\Framework\\TestStatus\\Failure' => $vendorDir . '/phpunit/phpunit/src/Framework/TestStatus/Failure.php',
    'PHPUnit\\Framework\\TestStatus\\Incomplete' => $vendorDir . '/phpunit/phpunit/src/Framework/TestStatus/Incomplete.php',
    'PHPUnit\\Framework\\TestStatus\\Known' => $vendorDir . '/phpunit/phpunit/src/Framework/TestStatus/Known.php',
    'PHPUnit\\Framework\\TestStatus\\Notice' => $vendorDir . '/phpunit/phpunit/src/Framework/TestStatus/Notice.php',
    'PHPUnit\\Framework\\TestStatus\\Risky' => $vendorDir . '/phpunit/phpunit/src/Framework/TestStatus/Risky.php',
    'PHPUnit\\Framework\\TestStatus\\Skipped' => $vendorDir . '/phpunit/phpunit/src/Framework/TestStatus/Skipped.php',
    'PHPUnit\\Framework\\TestStatus\\Success' => $vendorDir . '/phpunit/phpunit/src/Framework/TestStatus/Success.php',
    'PHPUnit\\Framework\\TestStatus\\TestStatus' => $vendorDir . '/phpunit/phpunit/src/Framework/TestStatus/TestStatus.php',
    'PHPUnit\\Framework\\TestStatus\\Unknown' => $vendorDir . '/phpunit/phpunit/src/Framework/TestStatus/Unknown.php',
    'PHPUnit\\Framework\\TestStatus\\Warning' => $vendorDir . '/phpunit/phpunit/src/Framework/TestStatus/Warning.php',
    'PHPUnit\\Framework\\TestSuite' => $vendorDir . '/phpunit/phpunit/src/Framework/TestSuite.php',
    'PHPUnit\\Framework\\TestSuiteIterator' => $vendorDir . '/phpunit/phpunit/src/Framework/TestSuiteIterator.php',
    'PHPUnit\\Framework\\UnknownClassOrInterfaceException' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/UnknownClassOrInterfaceException.php',
    'PHPUnit\\Framework\\UnknownTypeException' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/UnknownTypeException.php',
    'PHPUnit\\Logging\\EventLogger' => $vendorDir . '/phpunit/phpunit/src/Logging/EventLogger.php',
    'PHPUnit\\Logging\\JUnit\\JunitXmlLogger' => $vendorDir . '/phpunit/phpunit/src/Logging/JUnit/JunitXmlLogger.php',
    'PHPUnit\\Logging\\JUnit\\Subscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/JUnit/Subscriber/Subscriber.php',
    'PHPUnit\\Logging\\JUnit\\TestErroredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/JUnit/Subscriber/TestErroredSubscriber.php',
    'PHPUnit\\Logging\\JUnit\\TestFailedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/JUnit/Subscriber/TestFailedSubscriber.php',
    'PHPUnit\\Logging\\JUnit\\TestFinishedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/JUnit/Subscriber/TestFinishedSubscriber.php',
    'PHPUnit\\Logging\\JUnit\\TestMarkedIncompleteSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/JUnit/Subscriber/TestMarkedIncompleteSubscriber.php',
    'PHPUnit\\Logging\\JUnit\\TestPreparationFailedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/JUnit/Subscriber/TestPreparationFailedSubscriber.php',
    'PHPUnit\\Logging\\JUnit\\TestPreparationStartedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/JUnit/Subscriber/TestPreparationStartedSubscriber.php',
    'PHPUnit\\Logging\\JUnit\\TestPreparedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/JUnit/Subscriber/TestPreparedSubscriber.php',
    'PHPUnit\\Logging\\JUnit\\TestPrintedUnexpectedOutputSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/JUnit/Subscriber/TestPrintedUnexpectedOutputSubscriber.php',
    'PHPUnit\\Logging\\JUnit\\TestRunnerExecutionFinishedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/JUnit/Subscriber/TestRunnerExecutionFinishedSubscriber.php',
    'PHPUnit\\Logging\\JUnit\\TestSkippedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/JUnit/Subscriber/TestSkippedSubscriber.php',
    'PHPUnit\\Logging\\JUnit\\TestSuiteFinishedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/JUnit/Subscriber/TestSuiteFinishedSubscriber.php',
    'PHPUnit\\Logging\\JUnit\\TestSuiteStartedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/JUnit/Subscriber/TestSuiteStartedSubscriber.php',
    'PHPUnit\\Logging\\TeamCity\\Subscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TeamCity/Subscriber/Subscriber.php',
    'PHPUnit\\Logging\\TeamCity\\TeamCityLogger' => $vendorDir . '/phpunit/phpunit/src/Logging/TeamCity/TeamCityLogger.php',
    'PHPUnit\\Logging\\TeamCity\\TestConsideredRiskySubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TeamCity/Subscriber/TestConsideredRiskySubscriber.php',
    'PHPUnit\\Logging\\TeamCity\\TestErroredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TeamCity/Subscriber/TestErroredSubscriber.php',
    'PHPUnit\\Logging\\TeamCity\\TestFailedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TeamCity/Subscriber/TestFailedSubscriber.php',
    'PHPUnit\\Logging\\TeamCity\\TestFinishedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TeamCity/Subscriber/TestFinishedSubscriber.php',
    'PHPUnit\\Logging\\TeamCity\\TestMarkedIncompleteSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TeamCity/Subscriber/TestMarkedIncompleteSubscriber.php',
    'PHPUnit\\Logging\\TeamCity\\TestPreparedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TeamCity/Subscriber/TestPreparedSubscriber.php',
    'PHPUnit\\Logging\\TeamCity\\TestRunnerExecutionFinishedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TeamCity/Subscriber/TestRunnerExecutionFinishedSubscriber.php',
    'PHPUnit\\Logging\\TeamCity\\TestSkippedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TeamCity/Subscriber/TestSkippedSubscriber.php',
    'PHPUnit\\Logging\\TeamCity\\TestSuiteBeforeFirstTestMethodErroredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TeamCity/Subscriber/TestSuiteBeforeFirstTestMethodErroredSubscriber.php',
    'PHPUnit\\Logging\\TeamCity\\TestSuiteFinishedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TeamCity/Subscriber/TestSuiteFinishedSubscriber.php',
    'PHPUnit\\Logging\\TeamCity\\TestSuiteSkippedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TeamCity/Subscriber/TestSuiteSkippedSubscriber.php',
    'PHPUnit\\Logging\\TeamCity\\TestSuiteStartedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TeamCity/Subscriber/TestSuiteStartedSubscriber.php',
    'PHPUnit\\Logging\\TestDox\\HtmlRenderer' => $vendorDir . '/phpunit/phpunit/src/Logging/TestDox/HtmlRenderer.php',
    'PHPUnit\\Logging\\TestDox\\NamePrettifier' => $vendorDir . '/phpunit/phpunit/src/Logging/TestDox/NamePrettifier.php',
    'PHPUnit\\Logging\\TestDox\\PlainTextRenderer' => $vendorDir . '/phpunit/phpunit/src/Logging/TestDox/PlainTextRenderer.php',
    'PHPUnit\\Logging\\TestDox\\Subscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TestDox/TestResult/Subscriber/Subscriber.php',
    'PHPUnit\\Logging\\TestDox\\TestConsideredRiskySubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TestDox/TestResult/Subscriber/TestConsideredRiskySubscriber.php',
    'PHPUnit\\Logging\\TestDox\\TestErroredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TestDox/TestResult/Subscriber/TestErroredSubscriber.php',
    'PHPUnit\\Logging\\TestDox\\TestFailedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TestDox/TestResult/Subscriber/TestFailedSubscriber.php',
    'PHPUnit\\Logging\\TestDox\\TestFinishedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TestDox/TestResult/Subscriber/TestFinishedSubscriber.php',
    'PHPUnit\\Logging\\TestDox\\TestMarkedIncompleteSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TestDox/TestResult/Subscriber/TestMarkedIncompleteSubscriber.php',
    'PHPUnit\\Logging\\TestDox\\TestPassedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TestDox/TestResult/Subscriber/TestPassedSubscriber.php',
    'PHPUnit\\Logging\\TestDox\\TestPreparedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TestDox/TestResult/Subscriber/TestPreparedSubscriber.php',
    'PHPUnit\\Logging\\TestDox\\TestResult' => $vendorDir . '/phpunit/phpunit/src/Logging/TestDox/TestResult/TestResult.php',
    'PHPUnit\\Logging\\TestDox\\TestResultCollection' => $vendorDir . '/phpunit/phpunit/src/Logging/TestDox/TestResult/TestResultCollection.php',
    'PHPUnit\\Logging\\TestDox\\TestResultCollectionIterator' => $vendorDir . '/phpunit/phpunit/src/Logging/TestDox/TestResult/TestResultCollectionIterator.php',
    'PHPUnit\\Logging\\TestDox\\TestResultCollector' => $vendorDir . '/phpunit/phpunit/src/Logging/TestDox/TestResult/TestResultCollector.php',
    'PHPUnit\\Logging\\TestDox\\TestSkippedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TestDox/TestResult/Subscriber/TestSkippedSubscriber.php',
    'PHPUnit\\Logging\\TestDox\\TestTriggeredDeprecationSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TestDox/TestResult/Subscriber/TestTriggeredDeprecationSubscriber.php',
    'PHPUnit\\Logging\\TestDox\\TestTriggeredNoticeSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TestDox/TestResult/Subscriber/TestTriggeredNoticeSubscriber.php',
    'PHPUnit\\Logging\\TestDox\\TestTriggeredPhpDeprecationSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TestDox/TestResult/Subscriber/TestTriggeredPhpDeprecationSubscriber.php',
    'PHPUnit\\Logging\\TestDox\\TestTriggeredPhpNoticeSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TestDox/TestResult/Subscriber/TestTriggeredPhpNoticeSubscriber.php',
    'PHPUnit\\Logging\\TestDox\\TestTriggeredPhpWarningSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TestDox/TestResult/Subscriber/TestTriggeredPhpWarningSubscriber.php',
    'PHPUnit\\Logging\\TestDox\\TestTriggeredPhpunitDeprecationSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TestDox/TestResult/Subscriber/TestTriggeredPhpunitDeprecationSubscriber.php',
    'PHPUnit\\Logging\\TestDox\\TestTriggeredPhpunitErrorSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TestDox/TestResult/Subscriber/TestTriggeredPhpunitErrorSubscriber.php',
    'PHPUnit\\Logging\\TestDox\\TestTriggeredPhpunitWarningSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TestDox/TestResult/Subscriber/TestTriggeredPhpunitWarningSubscriber.php',
    'PHPUnit\\Logging\\TestDox\\TestTriggeredWarningSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TestDox/TestResult/Subscriber/TestTriggeredWarningSubscriber.php',
    'PHPUnit\\Metadata\\After' => $vendorDir . '/phpunit/phpunit/src/Metadata/After.php',
    'PHPUnit\\Metadata\\AfterClass' => $vendorDir . '/phpunit/phpunit/src/Metadata/AfterClass.php',
    'PHPUnit\\Metadata\\Annotation\\Parser\\DocBlock' => $vendorDir . '/phpunit/phpunit/src/Metadata/Parser/Annotation/DocBlock.php',
    'PHPUnit\\Metadata\\Annotation\\Parser\\Registry' => $vendorDir . '/phpunit/phpunit/src/Metadata/Parser/Annotation/Registry.php',
    'PHPUnit\\Metadata\\AnnotationsAreNotSupportedForInternalClassesException' => $vendorDir . '/phpunit/phpunit/src/Metadata/Exception/AnnotationsAreNotSupportedForInternalClassesException.php',
    'PHPUnit\\Metadata\\Api\\CodeCoverage' => $vendorDir . '/phpunit/phpunit/src/Metadata/Api/CodeCoverage.php',
    'PHPUnit\\Metadata\\Api\\DataProvider' => $vendorDir . '/phpunit/phpunit/src/Metadata/Api/DataProvider.php',
    'PHPUnit\\Metadata\\Api\\Dependencies' => $vendorDir . '/phpunit/phpunit/src/Metadata/Api/Dependencies.php',
    'PHPUnit\\Metadata\\Api\\Groups' => $vendorDir . '/phpunit/phpunit/src/Metadata/Api/Groups.php',
    'PHPUnit\\Metadata\\Api\\HookMethods' => $vendorDir . '/phpunit/phpunit/src/Metadata/Api/HookMethods.php',
    'PHPUnit\\Metadata\\Api\\Requirements' => $vendorDir . '/phpunit/phpunit/src/Metadata/Api/Requirements.php',
    'PHPUnit\\Metadata\\BackupGlobals' => $vendorDir . '/phpunit/phpunit/src/Metadata/BackupGlobals.php',
    'PHPUnit\\Metadata\\BackupStaticProperties' => $vendorDir . '/phpunit/phpunit/src/Metadata/BackupStaticProperties.php',
    'PHPUnit\\Metadata\\Before' => $vendorDir . '/phpunit/phpunit/src/Metadata/Before.php',
    'PHPUnit\\Metadata\\BeforeClass' => $vendorDir . '/phpunit/phpunit/src/Metadata/BeforeClass.php',
    'PHPUnit\\Metadata\\Covers' => $vendorDir . '/phpunit/phpunit/src/Metadata/Covers.php',
    'PHPUnit\\Metadata\\CoversClass' => $vendorDir . '/phpunit/phpunit/src/Metadata/CoversClass.php',
    'PHPUnit\\Metadata\\CoversDefaultClass' => $vendorDir . '/phpunit/phpunit/src/Metadata/CoversDefaultClass.php',
    'PHPUnit\\Metadata\\CoversFunction' => $vendorDir . '/phpunit/phpunit/src/Metadata/CoversFunction.php',
    'PHPUnit\\Metadata\\CoversMethod' => $vendorDir . '/phpunit/phpunit/src/Metadata/CoversMethod.php',
    'PHPUnit\\Metadata\\CoversNothing' => $vendorDir . '/phpunit/phpunit/src/Metadata/CoversNothing.php',
    'PHPUnit\\Metadata\\CoversTrait' => $vendorDir . '/phpunit/phpunit/src/Metadata/CoversTrait.php',
    'PHPUnit\\Metadata\\DataProvider' => $vendorDir . '/phpunit/phpunit/src/Metadata/DataProvider.php',
    'PHPUnit\\Metadata\\DependsOnClass' => $vendorDir . '/phpunit/phpunit/src/Metadata/DependsOnClass.php',
    'PHPUnit\\Metadata\\DependsOnMethod' => $vendorDir . '/phpunit/phpunit/src/Metadata/DependsOnMethod.php',
    'PHPUnit\\Metadata\\DisableReturnValueGenerationForTestDoubles' => $vendorDir . '/phpunit/phpunit/src/Metadata/DisableReturnValueGenerationForTestDoubles.php',
    'PHPUnit\\Metadata\\DoesNotPerformAssertions' => $vendorDir . '/phpunit/phpunit/src/Metadata/DoesNotPerformAssertions.php',
    'PHPUnit\\Metadata\\Exception' => $vendorDir . '/phpunit/phpunit/src/Metadata/Exception/Exception.php',
    'PHPUnit\\Metadata\\ExcludeGlobalVariableFromBackup' => $vendorDir . '/phpunit/phpunit/src/Metadata/ExcludeGlobalVariableFromBackup.php',
    'PHPUnit\\Metadata\\ExcludeStaticPropertyFromBackup' => $vendorDir . '/phpunit/phpunit/src/Metadata/ExcludeStaticPropertyFromBackup.php',
    'PHPUnit\\Metadata\\Group' => $vendorDir . '/phpunit/phpunit/src/Metadata/Group.php',
    'PHPUnit\\Metadata\\IgnoreDeprecations' => $vendorDir . '/phpunit/phpunit/src/Metadata/IgnoreDeprecations.php',
    'PHPUnit\\Metadata\\IgnorePhpunitDeprecations' => $vendorDir . '/phpunit/phpunit/src/Metadata/IgnorePhpunitDeprecations.php',
    'PHPUnit\\Metadata\\InvalidAttributeException' => $vendorDir . '/phpunit/phpunit/src/Metadata/Exception/InvalidAttributeException.php',
    'PHPUnit\\Metadata\\InvalidVersionRequirementException' => $vendorDir . '/phpunit/phpunit/src/Metadata/Exception/InvalidVersionRequirementException.php',
    'PHPUnit\\Metadata\\Metadata' => $vendorDir . '/phpunit/phpunit/src/Metadata/Metadata.php',
    'PHPUnit\\Metadata\\MetadataCollection' => $vendorDir . '/phpunit/phpunit/src/Metadata/MetadataCollection.php',
    'PHPUnit\\Metadata\\MetadataCollectionIterator' => $vendorDir . '/phpunit/phpunit/src/Metadata/MetadataCollectionIterator.php',
    'PHPUnit\\Metadata\\NoVersionRequirementException' => $vendorDir . '/phpunit/phpunit/src/Metadata/Exception/NoVersionRequirementException.php',
    'PHPUnit\\Metadata\\Parser\\AnnotationParser' => $vendorDir . '/phpunit/phpunit/src/Metadata/Parser/AnnotationParser.php',
    'PHPUnit\\Metadata\\Parser\\AttributeParser' => $vendorDir . '/phpunit/phpunit/src/Metadata/Parser/AttributeParser.php',
    'PHPUnit\\Metadata\\Parser\\CachingParser' => $vendorDir . '/phpunit/phpunit/src/Metadata/Parser/CachingParser.php',
    'PHPUnit\\Metadata\\Parser\\Parser' => $vendorDir . '/phpunit/phpunit/src/Metadata/Parser/Parser.php',
    'PHPUnit\\Metadata\\Parser\\ParserChain' => $vendorDir . '/phpunit/phpunit/src/Metadata/Parser/ParserChain.php',
    'PHPUnit\\Metadata\\Parser\\Registry' => $vendorDir . '/phpunit/phpunit/src/Metadata/Parser/Registry.php',
    'PHPUnit\\Metadata\\PostCondition' => $vendorDir . '/phpunit/phpunit/src/Metadata/PostCondition.php',
    'PHPUnit\\Metadata\\PreCondition' => $vendorDir . '/phpunit/phpunit/src/Metadata/PreCondition.php',
    'PHPUnit\\Metadata\\PreserveGlobalState' => $vendorDir . '/phpunit/phpunit/src/Metadata/PreserveGlobalState.php',
    'PHPUnit\\Metadata\\ReflectionException' => $vendorDir . '/phpunit/phpunit/src/Metadata/Exception/ReflectionException.php',
    'PHPUnit\\Metadata\\RequiresFunction' => $vendorDir . '/phpunit/phpunit/src/Metadata/RequiresFunction.php',
    'PHPUnit\\Metadata\\RequiresMethod' => $vendorDir . '/phpunit/phpunit/src/Metadata/RequiresMethod.php',
    'PHPUnit\\Metadata\\RequiresOperatingSystem' => $vendorDir . '/phpunit/phpunit/src/Metadata/RequiresOperatingSystem.php',
    'PHPUnit\\Metadata\\RequiresOperatingSystemFamily' => $vendorDir . '/phpunit/phpunit/src/Metadata/RequiresOperatingSystemFamily.php',
    'PHPUnit\\Metadata\\RequiresPhp' => $vendorDir . '/phpunit/phpunit/src/Metadata/RequiresPhp.php',
    'PHPUnit\\Metadata\\RequiresPhpExtension' => $vendorDir . '/phpunit/phpunit/src/Metadata/RequiresPhpExtension.php',
    'PHPUnit\\Metadata\\RequiresPhpunit' => $vendorDir . '/phpunit/phpunit/src/Metadata/RequiresPhpunit.php',
    'PHPUnit\\Metadata\\RequiresPhpunitExtension' => $vendorDir . '/phpunit/phpunit/src/Metadata/RequiresPhpunitExtension.php',
    'PHPUnit\\Metadata\\RequiresSetting' => $vendorDir . '/phpunit/phpunit/src/Metadata/RequiresSetting.php',
    'PHPUnit\\Metadata\\RunClassInSeparateProcess' => $vendorDir . '/phpunit/phpunit/src/Metadata/RunClassInSeparateProcess.php',
    'PHPUnit\\Metadata\\RunInSeparateProcess' => $vendorDir . '/phpunit/phpunit/src/Metadata/RunInSeparateProcess.php',
    'PHPUnit\\Metadata\\RunTestsInSeparateProcesses' => $vendorDir . '/phpunit/phpunit/src/Metadata/RunTestsInSeparateProcesses.php',
    'PHPUnit\\Metadata\\Test' => $vendorDir . '/phpunit/phpunit/src/Metadata/Test.php',
    'PHPUnit\\Metadata\\TestDox' => $vendorDir . '/phpunit/phpunit/src/Metadata/TestDox.php',
    'PHPUnit\\Metadata\\TestWith' => $vendorDir . '/phpunit/phpunit/src/Metadata/TestWith.php',
    'PHPUnit\\Metadata\\Uses' => $vendorDir . '/phpunit/phpunit/src/Metadata/Uses.php',
    'PHPUnit\\Metadata\\UsesClass' => $vendorDir . '/phpunit/phpunit/src/Metadata/UsesClass.php',
    'PHPUnit\\Metadata\\UsesDefaultClass' => $vendorDir . '/phpunit/phpunit/src/Metadata/UsesDefaultClass.php',
    'PHPUnit\\Metadata\\UsesFunction' => $vendorDir . '/phpunit/phpunit/src/Metadata/UsesFunction.php',
    'PHPUnit\\Metadata\\UsesMethod' => $vendorDir . '/phpunit/phpunit/src/Metadata/UsesMethod.php',
    'PHPUnit\\Metadata\\UsesTrait' => $vendorDir . '/phpunit/phpunit/src/Metadata/UsesTrait.php',
    'PHPUnit\\Metadata\\Version\\ComparisonRequirement' => $vendorDir . '/phpunit/phpunit/src/Metadata/Version/ComparisonRequirement.php',
    'PHPUnit\\Metadata\\Version\\ConstraintRequirement' => $vendorDir . '/phpunit/phpunit/src/Metadata/Version/ConstraintRequirement.php',
    'PHPUnit\\Metadata\\Version\\Requirement' => $vendorDir . '/phpunit/phpunit/src/Metadata/Version/Requirement.php',
    'PHPUnit\\Metadata\\WithoutErrorHandler' => $vendorDir . '/phpunit/phpunit/src/Metadata/WithoutErrorHandler.php',
    'PHPUnit\\Runner\\Baseline\\Baseline' => $vendorDir . '/phpunit/phpunit/src/Runner/Baseline/Baseline.php',
    'PHPUnit\\Runner\\Baseline\\CannotLoadBaselineException' => $vendorDir . '/phpunit/phpunit/src/Runner/Baseline/Exception/CannotLoadBaselineException.php',
    'PHPUnit\\Runner\\Baseline\\FileDoesNotHaveLineException' => $vendorDir . '/phpunit/phpunit/src/Runner/Baseline/Exception/FileDoesNotHaveLineException.php',
    'PHPUnit\\Runner\\Baseline\\Generator' => $vendorDir . '/phpunit/phpunit/src/Runner/Baseline/Generator.php',
    'PHPUnit\\Runner\\Baseline\\Issue' => $vendorDir . '/phpunit/phpunit/src/Runner/Baseline/Issue.php',
    'PHPUnit\\Runner\\Baseline\\Reader' => $vendorDir . '/phpunit/phpunit/src/Runner/Baseline/Reader.php',
    'PHPUnit\\Runner\\Baseline\\RelativePathCalculator' => $vendorDir . '/phpunit/phpunit/src/Runner/Baseline/RelativePathCalculator.php',
    'PHPUnit\\Runner\\Baseline\\Subscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/Baseline/Subscriber/Subscriber.php',
    'PHPUnit\\Runner\\Baseline\\TestTriggeredDeprecationSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/Baseline/Subscriber/TestTriggeredDeprecationSubscriber.php',
    'PHPUnit\\Runner\\Baseline\\TestTriggeredNoticeSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/Baseline/Subscriber/TestTriggeredNoticeSubscriber.php',
    'PHPUnit\\Runner\\Baseline\\TestTriggeredPhpDeprecationSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/Baseline/Subscriber/TestTriggeredPhpDeprecationSubscriber.php',
    'PHPUnit\\Runner\\Baseline\\TestTriggeredPhpNoticeSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/Baseline/Subscriber/TestTriggeredPhpNoticeSubscriber.php',
    'PHPUnit\\Runner\\Baseline\\TestTriggeredPhpWarningSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/Baseline/Subscriber/TestTriggeredPhpWarningSubscriber.php',
    'PHPUnit\\Runner\\Baseline\\TestTriggeredWarningSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/Baseline/Subscriber/TestTriggeredWarningSubscriber.php',
    'PHPUnit\\Runner\\Baseline\\Writer' => $vendorDir . '/phpunit/phpunit/src/Runner/Baseline/Writer.php',
    'PHPUnit\\Runner\\ClassCannotBeFoundException' => $vendorDir . '/phpunit/phpunit/src/Runner/Exception/ClassCannotBeFoundException.php',
    'PHPUnit\\Runner\\ClassDoesNotExtendTestCaseException' => $vendorDir . '/phpunit/phpunit/src/Runner/Exception/ClassDoesNotExtendTestCaseException.php',
    'PHPUnit\\Runner\\ClassIsAbstractException' => $vendorDir . '/phpunit/phpunit/src/Runner/Exception/ClassIsAbstractException.php',
    'PHPUnit\\Runner\\CodeCoverage' => $vendorDir . '/phpunit/phpunit/src/Runner/CodeCoverage.php',
    'PHPUnit\\Runner\\DeprecationCollector\\Collector' => $vendorDir . '/phpunit/phpunit/src/Runner/DeprecationCollector/Collector.php',
    'PHPUnit\\Runner\\DeprecationCollector\\Facade' => $vendorDir . '/phpunit/phpunit/src/Runner/DeprecationCollector/Facade.php',
    'PHPUnit\\Runner\\DeprecationCollector\\Subscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/DeprecationCollector/Subscriber/Subscriber.php',
    'PHPUnit\\Runner\\DeprecationCollector\\TestPreparedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/DeprecationCollector/Subscriber/TestPreparedSubscriber.php',
    'PHPUnit\\Runner\\DeprecationCollector\\TestTriggeredDeprecationSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/DeprecationCollector/Subscriber/TestTriggeredDeprecationSubscriber.php',
    'PHPUnit\\Runner\\DirectoryDoesNotExistException' => $vendorDir . '/phpunit/phpunit/src/Runner/Exception/DirectoryDoesNotExistException.php',
    'PHPUnit\\Runner\\ErrorException' => $vendorDir . '/phpunit/phpunit/src/Runner/Exception/ErrorException.php',
    'PHPUnit\\Runner\\ErrorHandler' => $vendorDir . '/phpunit/phpunit/src/Runner/ErrorHandler.php',
    'PHPUnit\\Runner\\Exception' => $vendorDir . '/phpunit/phpunit/src/Runner/Exception/Exception.php',
    'PHPUnit\\Runner\\Extension\\Extension' => $vendorDir . '/phpunit/phpunit/src/Runner/Extension/Extension.php',
    'PHPUnit\\Runner\\Extension\\ExtensionBootstrapper' => $vendorDir . '/phpunit/phpunit/src/Runner/Extension/ExtensionBootstrapper.php',
    'PHPUnit\\Runner\\Extension\\Facade' => $vendorDir . '/phpunit/phpunit/src/Runner/Extension/Facade.php',
    'PHPUnit\\Runner\\Extension\\ParameterCollection' => $vendorDir . '/phpunit/phpunit/src/Runner/Extension/ParameterCollection.php',
    'PHPUnit\\Runner\\Extension\\PharLoader' => $vendorDir . '/phpunit/phpunit/src/Runner/Extension/PharLoader.php',
    'PHPUnit\\Runner\\FileDoesNotExistException' => $vendorDir . '/phpunit/phpunit/src/Runner/Exception/FileDoesNotExistException.php',
    'PHPUnit\\Runner\\Filter\\ExcludeGroupFilterIterator' => $vendorDir . '/phpunit/phpunit/src/Runner/Filter/ExcludeGroupFilterIterator.php',
    'PHPUnit\\Runner\\Filter\\ExcludeNameFilterIterator' => $vendorDir . '/phpunit/phpunit/src/Runner/Filter/ExcludeNameFilterIterator.php',
    'PHPUnit\\Runner\\Filter\\Factory' => $vendorDir . '/phpunit/phpunit/src/Runner/Filter/Factory.php',
    'PHPUnit\\Runner\\Filter\\GroupFilterIterator' => $vendorDir . '/phpunit/phpunit/src/Runner/Filter/GroupFilterIterator.php',
    'PHPUnit\\Runner\\Filter\\IncludeGroupFilterIterator' => $vendorDir . '/phpunit/phpunit/src/Runner/Filter/IncludeGroupFilterIterator.php',
    'PHPUnit\\Runner\\Filter\\IncludeNameFilterIterator' => $vendorDir . '/phpunit/phpunit/src/Runner/Filter/IncludeNameFilterIterator.php',
    'PHPUnit\\Runner\\Filter\\NameFilterIterator' => $vendorDir . '/phpunit/phpunit/src/Runner/Filter/NameFilterIterator.php',
    'PHPUnit\\Runner\\Filter\\TestIdFilterIterator' => $vendorDir . '/phpunit/phpunit/src/Runner/Filter/TestIdFilterIterator.php',
    'PHPUnit\\Runner\\GarbageCollection\\ExecutionFinishedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/GarbageCollection/Subscriber/ExecutionFinishedSubscriber.php',
    'PHPUnit\\Runner\\GarbageCollection\\ExecutionStartedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/GarbageCollection/Subscriber/ExecutionStartedSubscriber.php',
    'PHPUnit\\Runner\\GarbageCollection\\GarbageCollectionHandler' => $vendorDir . '/phpunit/phpunit/src/Runner/GarbageCollection/GarbageCollectionHandler.php',
    'PHPUnit\\Runner\\GarbageCollection\\Subscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/GarbageCollection/Subscriber/Subscriber.php',
    'PHPUnit\\Runner\\GarbageCollection\\TestFinishedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/GarbageCollection/Subscriber/TestFinishedSubscriber.php',
    'PHPUnit\\Runner\\HookMethod' => $vendorDir . '/phpunit/phpunit/src/Runner/HookMethod/HookMethod.php',
    'PHPUnit\\Runner\\HookMethodCollection' => $vendorDir . '/phpunit/phpunit/src/Runner/HookMethod/HookMethodCollection.php',
    'PHPUnit\\Runner\\InvalidOrderException' => $vendorDir . '/phpunit/phpunit/src/Runner/Exception/InvalidOrderException.php',
    'PHPUnit\\Runner\\InvalidPhptFileException' => $vendorDir . '/phpunit/phpunit/src/Runner/Exception/InvalidPhptFileException.php',
    'PHPUnit\\Runner\\ParameterDoesNotExistException' => $vendorDir . '/phpunit/phpunit/src/Runner/Exception/ParameterDoesNotExistException.php',
    'PHPUnit\\Runner\\PhptExternalFileCannotBeLoadedException' => $vendorDir . '/phpunit/phpunit/src/Runner/Exception/PhptExternalFileCannotBeLoadedException.php',
    'PHPUnit\\Runner\\PhptTestCase' => $vendorDir . '/phpunit/phpunit/src/Runner/PHPT/PhptTestCase.php',
    'PHPUnit\\Runner\\ResultCache\\DefaultResultCache' => $vendorDir . '/phpunit/phpunit/src/Runner/ResultCache/DefaultResultCache.php',
    'PHPUnit\\Runner\\ResultCache\\NullResultCache' => $vendorDir . '/phpunit/phpunit/src/Runner/ResultCache/NullResultCache.php',
    'PHPUnit\\Runner\\ResultCache\\ResultCache' => $vendorDir . '/phpunit/phpunit/src/Runner/ResultCache/ResultCache.php',
    'PHPUnit\\Runner\\ResultCache\\ResultCacheHandler' => $vendorDir . '/phpunit/phpunit/src/Runner/ResultCache/ResultCacheHandler.php',
    'PHPUnit\\Runner\\ResultCache\\Subscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/ResultCache/Subscriber/Subscriber.php',
    'PHPUnit\\Runner\\ResultCache\\TestConsideredRiskySubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/ResultCache/Subscriber/TestConsideredRiskySubscriber.php',
    'PHPUnit\\Runner\\ResultCache\\TestErroredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/ResultCache/Subscriber/TestErroredSubscriber.php',
    'PHPUnit\\Runner\\ResultCache\\TestFailedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/ResultCache/Subscriber/TestFailedSubscriber.php',
    'PHPUnit\\Runner\\ResultCache\\TestFinishedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/ResultCache/Subscriber/TestFinishedSubscriber.php',
    'PHPUnit\\Runner\\ResultCache\\TestMarkedIncompleteSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/ResultCache/Subscriber/TestMarkedIncompleteSubscriber.php',
    'PHPUnit\\Runner\\ResultCache\\TestPreparedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/ResultCache/Subscriber/TestPreparedSubscriber.php',
    'PHPUnit\\Runner\\ResultCache\\TestSkippedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/ResultCache/Subscriber/TestSkippedSubscriber.php',
    'PHPUnit\\Runner\\ResultCache\\TestSuiteFinishedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/ResultCache/Subscriber/TestSuiteFinishedSubscriber.php',
    'PHPUnit\\Runner\\ResultCache\\TestSuiteStartedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/ResultCache/Subscriber/TestSuiteStartedSubscriber.php',
    'PHPUnit\\Runner\\TestSuiteLoader' => $vendorDir . '/phpunit/phpunit/src/Runner/TestSuiteLoader.php',
    'PHPUnit\\Runner\\TestSuiteSorter' => $vendorDir . '/phpunit/phpunit/src/Runner/TestSuiteSorter.php',
    'PHPUnit\\Runner\\UnsupportedPhptSectionException' => $vendorDir . '/phpunit/phpunit/src/Runner/Exception/UnsupportedPhptSectionException.php',
    'PHPUnit\\Runner\\Version' => $vendorDir . '/phpunit/phpunit/src/Runner/Version.php',
    'PHPUnit\\TestRunner\\IssueFilter' => $vendorDir . '/phpunit/phpunit/src/Runner/IssueFilter.php',
    'PHPUnit\\TestRunner\\TestResult\\AfterTestClassMethodErroredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Subscriber/AfterTestClassMethodErroredSubscriber.php',
    'PHPUnit\\TestRunner\\TestResult\\BeforeTestClassMethodErroredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Subscriber/BeforeTestClassMethodErroredSubscriber.php',
    'PHPUnit\\TestRunner\\TestResult\\Collector' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Collector.php',
    'PHPUnit\\TestRunner\\TestResult\\ExecutionStartedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Subscriber/ExecutionStartedSubscriber.php',
    'PHPUnit\\TestRunner\\TestResult\\Facade' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Facade.php',
    'PHPUnit\\TestRunner\\TestResult\\Issues\\Issue' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Issue.php',
    'PHPUnit\\TestRunner\\TestResult\\PassedTests' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/PassedTests.php',
    'PHPUnit\\TestRunner\\TestResult\\Subscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Subscriber/Subscriber.php',
    'PHPUnit\\TestRunner\\TestResult\\TestConsideredRiskySubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Subscriber/TestConsideredRiskySubscriber.php',
    'PHPUnit\\TestRunner\\TestResult\\TestErroredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Subscriber/TestErroredSubscriber.php',
    'PHPUnit\\TestRunner\\TestResult\\TestFailedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Subscriber/TestFailedSubscriber.php',
    'PHPUnit\\TestRunner\\TestResult\\TestFinishedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Subscriber/TestFinishedSubscriber.php',
    'PHPUnit\\TestRunner\\TestResult\\TestMarkedIncompleteSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Subscriber/TestMarkedIncompleteSubscriber.php',
    'PHPUnit\\TestRunner\\TestResult\\TestPreparedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Subscriber/TestPreparedSubscriber.php',
    'PHPUnit\\TestRunner\\TestResult\\TestResult' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/TestResult.php',
    'PHPUnit\\TestRunner\\TestResult\\TestRunnerTriggeredDeprecationSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Subscriber/TestRunnerTriggeredDeprecationSubscriber.php',
    'PHPUnit\\TestRunner\\TestResult\\TestRunnerTriggeredWarningSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Subscriber/TestRunnerTriggeredWarningSubscriber.php',
    'PHPUnit\\TestRunner\\TestResult\\TestSkippedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Subscriber/TestSkippedSubscriber.php',
    'PHPUnit\\TestRunner\\TestResult\\TestSuiteFinishedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Subscriber/TestSuiteFinishedSubscriber.php',
    'PHPUnit\\TestRunner\\TestResult\\TestSuiteSkippedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Subscriber/TestSuiteSkippedSubscriber.php',
    'PHPUnit\\TestRunner\\TestResult\\TestSuiteStartedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Subscriber/TestSuiteStartedSubscriber.php',
    'PHPUnit\\TestRunner\\TestResult\\TestTriggeredDeprecationSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Subscriber/TestTriggeredDeprecationSubscriber.php',
    'PHPUnit\\TestRunner\\TestResult\\TestTriggeredErrorSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Subscriber/TestTriggeredErrorSubscriber.php',
    'PHPUnit\\TestRunner\\TestResult\\TestTriggeredNoticeSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Subscriber/TestTriggeredNoticeSubscriber.php',
    'PHPUnit\\TestRunner\\TestResult\\TestTriggeredPhpDeprecationSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Subscriber/TestTriggeredPhpDeprecationSubscriber.php',
    'PHPUnit\\TestRunner\\TestResult\\TestTriggeredPhpNoticeSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Subscriber/TestTriggeredPhpNoticeSubscriber.php',
    'PHPUnit\\TestRunner\\TestResult\\TestTriggeredPhpWarningSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Subscriber/TestTriggeredPhpWarningSubscriber.php',
    'PHPUnit\\TestRunner\\TestResult\\TestTriggeredPhpunitDeprecationSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Subscriber/TestTriggeredPhpunitDeprecationSubscriber.php',
    'PHPUnit\\TestRunner\\TestResult\\TestTriggeredPhpunitErrorSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Subscriber/TestTriggeredPhpunitErrorSubscriber.php',
    'PHPUnit\\TestRunner\\TestResult\\TestTriggeredPhpunitWarningSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Subscriber/TestTriggeredPhpunitWarningSubscriber.php',
    'PHPUnit\\TestRunner\\TestResult\\TestTriggeredWarningSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Subscriber/TestTriggeredWarningSubscriber.php',
    'PHPUnit\\TextUI\\Application' => $vendorDir . '/phpunit/phpunit/src/TextUI/Application.php',
    'PHPUnit\\TextUI\\CannotOpenSocketException' => $vendorDir . '/phpunit/phpunit/src/TextUI/Exception/CannotOpenSocketException.php',
    'PHPUnit\\TextUI\\CliArguments\\Builder' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Cli/Builder.php',
    'PHPUnit\\TextUI\\CliArguments\\Configuration' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Cli/Configuration.php',
    'PHPUnit\\TextUI\\CliArguments\\Exception' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Cli/Exception.php',
    'PHPUnit\\TextUI\\CliArguments\\XmlConfigurationFileFinder' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Cli/XmlConfigurationFileFinder.php',
    'PHPUnit\\TextUI\\Command\\AtLeastVersionCommand' => $vendorDir . '/phpunit/phpunit/src/TextUI/Command/Commands/AtLeastVersionCommand.php',
    'PHPUnit\\TextUI\\Command\\Command' => $vendorDir . '/phpunit/phpunit/src/TextUI/Command/Command.php',
    'PHPUnit\\TextUI\\Command\\GenerateConfigurationCommand' => $vendorDir . '/phpunit/phpunit/src/TextUI/Command/Commands/GenerateConfigurationCommand.php',
    'PHPUnit\\TextUI\\Command\\ListGroupsCommand' => $vendorDir . '/phpunit/phpunit/src/TextUI/Command/Commands/ListGroupsCommand.php',
    'PHPUnit\\TextUI\\Command\\ListTestFilesCommand' => $vendorDir . '/phpunit/phpunit/src/TextUI/Command/Commands/ListTestFilesCommand.php',
    'PHPUnit\\TextUI\\Command\\ListTestSuitesCommand' => $vendorDir . '/phpunit/phpunit/src/TextUI/Command/Commands/ListTestSuitesCommand.php',
    'PHPUnit\\TextUI\\Command\\ListTestsAsTextCommand' => $vendorDir . '/phpunit/phpunit/src/TextUI/Command/Commands/ListTestsAsTextCommand.php',
    'PHPUnit\\TextUI\\Command\\ListTestsAsXmlCommand' => $vendorDir . '/phpunit/phpunit/src/TextUI/Command/Commands/ListTestsAsXmlCommand.php',
    'PHPUnit\\TextUI\\Command\\MigrateConfigurationCommand' => $vendorDir . '/phpunit/phpunit/src/TextUI/Command/Commands/MigrateConfigurationCommand.php',
    'PHPUnit\\TextUI\\Command\\Result' => $vendorDir . '/phpunit/phpunit/src/TextUI/Command/Result.php',
    'PHPUnit\\TextUI\\Command\\ShowHelpCommand' => $vendorDir . '/phpunit/phpunit/src/TextUI/Command/Commands/ShowHelpCommand.php',
    'PHPUnit\\TextUI\\Command\\ShowVersionCommand' => $vendorDir . '/phpunit/phpunit/src/TextUI/Command/Commands/ShowVersionCommand.php',
    'PHPUnit\\TextUI\\Command\\VersionCheckCommand' => $vendorDir . '/phpunit/phpunit/src/TextUI/Command/Commands/VersionCheckCommand.php',
    'PHPUnit\\TextUI\\Command\\WarmCodeCoverageCacheCommand' => $vendorDir . '/phpunit/phpunit/src/TextUI/Command/Commands/WarmCodeCoverageCacheCommand.php',
    'PHPUnit\\TextUI\\Configuration\\Builder' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Builder.php',
    'PHPUnit\\TextUI\\Configuration\\CodeCoverageFilterRegistry' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/CodeCoverageFilterRegistry.php',
    'PHPUnit\\TextUI\\Configuration\\CodeCoverageReportNotConfiguredException' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Exception/CodeCoverageReportNotConfiguredException.php',
    'PHPUnit\\TextUI\\Configuration\\Configuration' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Configuration.php',
    'PHPUnit\\TextUI\\Configuration\\ConfigurationCannotBeBuiltException' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Exception/ConfigurationCannotBeBuiltException.php',
    'PHPUnit\\TextUI\\Configuration\\Constant' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/Constant.php',
    'PHPUnit\\TextUI\\Configuration\\ConstantCollection' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/ConstantCollection.php',
    'PHPUnit\\TextUI\\Configuration\\ConstantCollectionIterator' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/ConstantCollectionIterator.php',
    'PHPUnit\\TextUI\\Configuration\\Directory' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/Directory.php',
    'PHPUnit\\TextUI\\Configuration\\DirectoryCollection' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/DirectoryCollection.php',
    'PHPUnit\\TextUI\\Configuration\\DirectoryCollectionIterator' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/DirectoryCollectionIterator.php',
    'PHPUnit\\TextUI\\Configuration\\Exception' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Exception/Exception.php',
    'PHPUnit\\TextUI\\Configuration\\ExtensionBootstrap' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/ExtensionBootstrap.php',
    'PHPUnit\\TextUI\\Configuration\\ExtensionBootstrapCollection' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/ExtensionBootstrapCollection.php',
    'PHPUnit\\TextUI\\Configuration\\ExtensionBootstrapCollectionIterator' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/ExtensionBootstrapCollectionIterator.php',
    'PHPUnit\\TextUI\\Configuration\\File' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/File.php',
    'PHPUnit\\TextUI\\Configuration\\FileCollection' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/FileCollection.php',
    'PHPUnit\\TextUI\\Configuration\\FileCollectionIterator' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/FileCollectionIterator.php',
    'PHPUnit\\TextUI\\Configuration\\FilterDirectory' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/FilterDirectory.php',
    'PHPUnit\\TextUI\\Configuration\\FilterDirectoryCollection' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/FilterDirectoryCollection.php',
    'PHPUnit\\TextUI\\Configuration\\FilterDirectoryCollectionIterator' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/FilterDirectoryCollectionIterator.php',
    'PHPUnit\\TextUI\\Configuration\\FilterNotConfiguredException' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Exception/FilterNotConfiguredException.php',
    'PHPUnit\\TextUI\\Configuration\\Group' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/Group.php',
    'PHPUnit\\TextUI\\Configuration\\GroupCollection' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/GroupCollection.php',
    'PHPUnit\\TextUI\\Configuration\\GroupCollectionIterator' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/GroupCollectionIterator.php',
    'PHPUnit\\TextUI\\Configuration\\IniSetting' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/IniSetting.php',
    'PHPUnit\\TextUI\\Configuration\\IniSettingCollection' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/IniSettingCollection.php',
    'PHPUnit\\TextUI\\Configuration\\IniSettingCollectionIterator' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/IniSettingCollectionIterator.php',
    'PHPUnit\\TextUI\\Configuration\\LoggingNotConfiguredException' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Exception/LoggingNotConfiguredException.php',
    'PHPUnit\\TextUI\\Configuration\\Merger' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Merger.php',
    'PHPUnit\\TextUI\\Configuration\\NoBaselineException' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Exception/NoBaselineException.php',
    'PHPUnit\\TextUI\\Configuration\\NoBootstrapException' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Exception/NoBootstrapException.php',
    'PHPUnit\\TextUI\\Configuration\\NoCacheDirectoryException' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Exception/NoCacheDirectoryException.php',
    'PHPUnit\\TextUI\\Configuration\\NoConfigurationFileException' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Exception/NoConfigurationFileException.php',
    'PHPUnit\\TextUI\\Configuration\\NoCoverageCacheDirectoryException' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Exception/NoCoverageCacheDirectoryException.php',
    'PHPUnit\\TextUI\\Configuration\\NoCustomCssFileException' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Exception/NoCustomCssFileException.php',
    'PHPUnit\\TextUI\\Configuration\\NoDefaultTestSuiteException' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Exception/NoDefaultTestSuiteException.php',
    'PHPUnit\\TextUI\\Configuration\\NoPharExtensionDirectoryException' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Exception/NoPharExtensionDirectoryException.php',
    'PHPUnit\\TextUI\\Configuration\\Php' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/Php.php',
    'PHPUnit\\TextUI\\Configuration\\PhpHandler' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/PhpHandler.php',
    'PHPUnit\\TextUI\\Configuration\\Registry' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Registry.php',
    'PHPUnit\\TextUI\\Configuration\\Source' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/Source.php',
    'PHPUnit\\TextUI\\Configuration\\SourceFilter' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/SourceFilter.php',
    'PHPUnit\\TextUI\\Configuration\\SourceMapper' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/SourceMapper.php',
    'PHPUnit\\TextUI\\Configuration\\SpecificDeprecationToStopOnNotConfiguredException' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Exception/SpecificDeprecationToStopOnNotConfiguredException.php',
    'PHPUnit\\TextUI\\Configuration\\TestDirectory' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/TestDirectory.php',
    'PHPUnit\\TextUI\\Configuration\\TestDirectoryCollection' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/TestDirectoryCollection.php',
    'PHPUnit\\TextUI\\Configuration\\TestDirectoryCollectionIterator' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/TestDirectoryCollectionIterator.php',
    'PHPUnit\\TextUI\\Configuration\\TestFile' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/TestFile.php',
    'PHPUnit\\TextUI\\Configuration\\TestFileCollection' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/TestFileCollection.php',
    'PHPUnit\\TextUI\\Configuration\\TestFileCollectionIterator' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/TestFileCollectionIterator.php',
    'PHPUnit\\TextUI\\Configuration\\TestSuite' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/TestSuite.php',
    'PHPUnit\\TextUI\\Configuration\\TestSuiteBuilder' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/TestSuiteBuilder.php',
    'PHPUnit\\TextUI\\Configuration\\TestSuiteCollection' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/TestSuiteCollection.php',
    'PHPUnit\\TextUI\\Configuration\\TestSuiteCollectionIterator' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/TestSuiteCollectionIterator.php',
    'PHPUnit\\TextUI\\Configuration\\Variable' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/Variable.php',
    'PHPUnit\\TextUI\\Configuration\\VariableCollection' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/VariableCollection.php',
    'PHPUnit\\TextUI\\Configuration\\VariableCollectionIterator' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/VariableCollectionIterator.php',
    'PHPUnit\\TextUI\\Exception' => $vendorDir . '/phpunit/phpunit/src/TextUI/Exception/Exception.php',
    'PHPUnit\\TextUI\\Help' => $vendorDir . '/phpunit/phpunit/src/TextUI/Help.php',
    'PHPUnit\\TextUI\\InvalidSocketException' => $vendorDir . '/phpunit/phpunit/src/TextUI/Exception/InvalidSocketException.php',
    'PHPUnit\\TextUI\\Output\\DefaultPrinter' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/Printer/DefaultPrinter.php',
    'PHPUnit\\TextUI\\Output\\Default\\ProgressPrinter\\BeforeTestClassMethodErroredSubscriber' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/Default/ProgressPrinter/Subscriber/BeforeTestClassMethodErroredSubscriber.php',
    'PHPUnit\\TextUI\\Output\\Default\\ProgressPrinter\\ProgressPrinter' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/Default/ProgressPrinter/ProgressPrinter.php',
    'PHPUnit\\TextUI\\Output\\Default\\ProgressPrinter\\Subscriber' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/Default/ProgressPrinter/Subscriber/Subscriber.php',
    'PHPUnit\\TextUI\\Output\\Default\\ProgressPrinter\\TestConsideredRiskySubscriber' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/Default/ProgressPrinter/Subscriber/TestConsideredRiskySubscriber.php',
    'PHPUnit\\TextUI\\Output\\Default\\ProgressPrinter\\TestErroredSubscriber' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/Default/ProgressPrinter/Subscriber/TestErroredSubscriber.php',
    'PHPUnit\\TextUI\\Output\\Default\\ProgressPrinter\\TestFailedSubscriber' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/Default/ProgressPrinter/Subscriber/TestFailedSubscriber.php',
    'PHPUnit\\TextUI\\Output\\Default\\ProgressPrinter\\TestFinishedSubscriber' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/Default/ProgressPrinter/Subscriber/TestFinishedSubscriber.php',
    'PHPUnit\\TextUI\\Output\\Default\\ProgressPrinter\\TestMarkedIncompleteSubscriber' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/Default/ProgressPrinter/Subscriber/TestMarkedIncompleteSubscriber.php',
    'PHPUnit\\TextUI\\Output\\Default\\ProgressPrinter\\TestPreparedSubscriber' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/Default/ProgressPrinter/Subscriber/TestPreparedSubscriber.php',
    'PHPUnit\\TextUI\\Output\\Default\\ProgressPrinter\\TestRunnerExecutionStartedSubscriber' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/Default/ProgressPrinter/Subscriber/TestRunnerExecutionStartedSubscriber.php',
    'PHPUnit\\TextUI\\Output\\Default\\ProgressPrinter\\TestSkippedSubscriber' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/Default/ProgressPrinter/Subscriber/TestSkippedSubscriber.php',
    'PHPUnit\\TextUI\\Output\\Default\\ProgressPrinter\\TestTriggeredDeprecationSubscriber' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/Default/ProgressPrinter/Subscriber/TestTriggeredDeprecationSubscriber.php',
    'PHPUnit\\TextUI\\Output\\Default\\ProgressPrinter\\TestTriggeredErrorSubscriber' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/Default/ProgressPrinter/Subscriber/TestTriggeredErrorSubscriber.php',
    'PHPUnit\\TextUI\\Output\\Default\\ProgressPrinter\\TestTriggeredNoticeSubscriber' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/Default/ProgressPrinter/Subscriber/TestTriggeredNoticeSubscriber.php',
    'PHPUnit\\TextUI\\Output\\Default\\ProgressPrinter\\TestTriggeredPhpDeprecationSubscriber' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/Default/ProgressPrinter/Subscriber/TestTriggeredPhpDeprecationSubscriber.php',
    'PHPUnit\\TextUI\\Output\\Default\\ProgressPrinter\\TestTriggeredPhpNoticeSubscriber' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/Default/ProgressPrinter/Subscriber/TestTriggeredPhpNoticeSubscriber.php',
    'PHPUnit\\TextUI\\Output\\Default\\ProgressPrinter\\TestTriggeredPhpWarningSubscriber' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/Default/ProgressPrinter/Subscriber/TestTriggeredPhpWarningSubscriber.php',
    'PHPUnit\\TextUI\\Output\\Default\\ProgressPrinter\\TestTriggeredPhpunitDeprecationSubscriber' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/Default/ProgressPrinter/Subscriber/TestTriggeredPhpunitDeprecationSubscriber.php',
    'PHPUnit\\TextUI\\Output\\Default\\ProgressPrinter\\TestTriggeredPhpunitWarningSubscriber' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/Default/ProgressPrinter/Subscriber/TestTriggeredPhpunitWarningSubscriber.php',
    'PHPUnit\\TextUI\\Output\\Default\\ProgressPrinter\\TestTriggeredWarningSubscriber' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/Default/ProgressPrinter/Subscriber/TestTriggeredWarningSubscriber.php',
    'PHPUnit\\TextUI\\Output\\Default\\ResultPrinter' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/Default/ResultPrinter.php',
    'PHPUnit\\TextUI\\Output\\Default\\UnexpectedOutputPrinter' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/Default/UnexpectedOutputPrinter.php',
    'PHPUnit\\TextUI\\Output\\Facade' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/Facade.php',
    'PHPUnit\\TextUI\\Output\\NullPrinter' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/Printer/NullPrinter.php',
    'PHPUnit\\TextUI\\Output\\Printer' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/Printer/Printer.php',
    'PHPUnit\\TextUI\\Output\\SummaryPrinter' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/SummaryPrinter.php',
    'PHPUnit\\TextUI\\Output\\TestDox\\ResultPrinter' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/TestDox/ResultPrinter.php',
    'PHPUnit\\TextUI\\RuntimeException' => $vendorDir . '/phpunit/phpunit/src/TextUI/Exception/RuntimeException.php',
    'PHPUnit\\TextUI\\ShellExitCodeCalculator' => $vendorDir . '/phpunit/phpunit/src/TextUI/ShellExitCodeCalculator.php',
    'PHPUnit\\TextUI\\TestDirectoryNotFoundException' => $vendorDir . '/phpunit/phpunit/src/TextUI/Exception/TestDirectoryNotFoundException.php',
    'PHPUnit\\TextUI\\TestFileNotFoundException' => $vendorDir . '/phpunit/phpunit/src/TextUI/Exception/TestFileNotFoundException.php',
    'PHPUnit\\TextUI\\TestRunner' => $vendorDir . '/phpunit/phpunit/src/TextUI/TestRunner.php',
    'PHPUnit\\TextUI\\TestSuiteFilterProcessor' => $vendorDir . '/phpunit/phpunit/src/TextUI/TestSuiteFilterProcessor.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\CannotFindSchemaException' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Exception/CannotFindSchemaException.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\CodeCoverage\\CodeCoverage' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/CodeCoverage/CodeCoverage.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\CodeCoverage\\Report\\Clover' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/CodeCoverage/Report/Clover.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\CodeCoverage\\Report\\Cobertura' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/CodeCoverage/Report/Cobertura.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\CodeCoverage\\Report\\Crap4j' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/CodeCoverage/Report/Crap4j.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\CodeCoverage\\Report\\Html' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/CodeCoverage/Report/Html.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\CodeCoverage\\Report\\Php' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/CodeCoverage/Report/Php.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\CodeCoverage\\Report\\Text' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/CodeCoverage/Report/Text.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\CodeCoverage\\Report\\Xml' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/CodeCoverage/Report/Xml.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\Configuration' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Configuration.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\ConvertLogTypes' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/ConvertLogTypes.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\CoverageCloverToReport' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/CoverageCloverToReport.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\CoverageCrap4jToReport' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/CoverageCrap4jToReport.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\CoverageHtmlToReport' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/CoverageHtmlToReport.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\CoveragePhpToReport' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/CoveragePhpToReport.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\CoverageTextToReport' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/CoverageTextToReport.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\CoverageXmlToReport' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/CoverageXmlToReport.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\DefaultConfiguration' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/DefaultConfiguration.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\Exception' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Exception.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\FailedSchemaDetectionResult' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/SchemaDetector/FailedSchemaDetectionResult.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\Generator' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Generator.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\Groups' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Groups.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\IntroduceCacheDirectoryAttribute' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/IntroduceCacheDirectoryAttribute.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\IntroduceCoverageElement' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/IntroduceCoverageElement.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\LoadedFromFileConfiguration' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/LoadedFromFileConfiguration.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\Loader' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Loader.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\LogToReportMigration' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/LogToReportMigration.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\Logging\\Junit' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Logging/Junit.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\Logging\\Logging' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Logging/Logging.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\Logging\\TeamCity' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Logging/TeamCity.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\Logging\\TestDox\\Html' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Logging/TestDox/Html.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\Logging\\TestDox\\Text' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Logging/TestDox/Text.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\Migration' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/Migration.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\MigrationBuilder' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/MigrationBuilder.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\MigrationException' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/MigrationException.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\Migrator' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrator.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\MoveAttributesFromFilterWhitelistToCoverage' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/MoveAttributesFromFilterWhitelistToCoverage.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\MoveAttributesFromRootToCoverage' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/MoveAttributesFromRootToCoverage.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\MoveCoverageDirectoriesToSource' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/MoveCoverageDirectoriesToSource.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\MoveWhitelistExcludesToCoverage' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/MoveWhitelistExcludesToCoverage.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\MoveWhitelistIncludesToCoverage' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/MoveWhitelistIncludesToCoverage.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\PHPUnit' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/PHPUnit.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\RemoveBeStrictAboutResourceUsageDuringSmallTestsAttribute' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/RemoveBeStrictAboutResourceUsageDuringSmallTestsAttribute.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\RemoveBeStrictAboutTodoAnnotatedTestsAttribute' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/RemoveBeStrictAboutTodoAnnotatedTestsAttribute.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\RemoveCacheResultFileAttribute' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/RemoveCacheResultFileAttribute.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\RemoveCacheTokensAttribute' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/RemoveCacheTokensAttribute.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\RemoveConversionToExceptionsAttributes' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/RemoveConversionToExceptionsAttributes.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\RemoveCoverageElementCacheDirectoryAttribute' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/RemoveCoverageElementCacheDirectoryAttribute.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\RemoveCoverageElementProcessUncoveredFilesAttribute' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/RemoveCoverageElementProcessUncoveredFilesAttribute.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\RemoveEmptyFilter' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/RemoveEmptyFilter.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\RemoveListeners' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/RemoveListeners.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\RemoveLogTypes' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/RemoveLogTypes.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\RemoveLoggingElements' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/RemoveLoggingElements.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\RemoveNoInteractionAttribute' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/RemoveNoInteractionAttribute.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\RemovePrinterAttributes' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/RemovePrinterAttributes.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\RemoveRegisterMockObjectsFromTestArgumentsRecursivelyAttribute' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/RemoveRegisterMockObjectsFromTestArgumentsRecursivelyAttribute.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\RemoveTestDoxGroupsElement' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/RemoveTestDoxGroupsElement.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\RemoveTestSuiteLoaderAttributes' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/RemoveTestSuiteLoaderAttributes.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\RemoveVerboseAttribute' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/RemoveVerboseAttribute.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\RenameBackupStaticAttributesAttribute' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/RenameBackupStaticAttributesAttribute.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\RenameBeStrictAboutCoversAnnotationAttribute' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/RenameBeStrictAboutCoversAnnotationAttribute.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\RenameForceCoversAnnotationAttribute' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/RenameForceCoversAnnotationAttribute.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\ReplaceRestrictDeprecationsWithIgnoreDeprecations' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/ReplaceRestrictDeprecationsWithIgnoreDeprecations.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\SchemaDetectionResult' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/SchemaDetector/SchemaDetectionResult.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\SchemaDetector' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/SchemaDetector/SchemaDetector.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\SchemaFinder' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/SchemaFinder.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\SnapshotNodeList' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/SnapshotNodeList.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\SuccessfulSchemaDetectionResult' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/SchemaDetector/SuccessfulSchemaDetectionResult.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\TestSuiteMapper' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/TestSuiteMapper.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\UpdateSchemaLocation' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/UpdateSchemaLocation.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\ValidationResult' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Validator/ValidationResult.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\Validator' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Validator/Validator.php',
    'PHPUnit\\Util\\Cloner' => $vendorDir . '/phpunit/phpunit/src/Util/Cloner.php',
    'PHPUnit\\Util\\Color' => $vendorDir . '/phpunit/phpunit/src/Util/Color.php',
    'PHPUnit\\Util\\Exception' => $vendorDir . '/phpunit/phpunit/src/Util/Exception/Exception.php',
    'PHPUnit\\Util\\ExcludeList' => $vendorDir . '/phpunit/phpunit/src/Util/ExcludeList.php',
    'PHPUnit\\Util\\Exporter' => $vendorDir . '/phpunit/phpunit/src/Util/Exporter.php',
    'PHPUnit\\Util\\Filesystem' => $vendorDir . '/phpunit/phpunit/src/Util/Filesystem.php',
    'PHPUnit\\Util\\Filter' => $vendorDir . '/phpunit/phpunit/src/Util/Filter.php',
    'PHPUnit\\Util\\GlobalState' => $vendorDir . '/phpunit/phpunit/src/Util/GlobalState.php',
    'PHPUnit\\Util\\Http\\Downloader' => $vendorDir . '/phpunit/phpunit/src/Util/Http/Downloader.php',
    'PHPUnit\\Util\\Http\\PhpDownloader' => $vendorDir . '/phpunit/phpunit/src/Util/Http/PhpDownloader.php',
    'PHPUnit\\Util\\InvalidDirectoryException' => $vendorDir . '/phpunit/phpunit/src/Util/Exception/InvalidDirectoryException.php',
    'PHPUnit\\Util\\InvalidJsonException' => $vendorDir . '/phpunit/phpunit/src/Util/Exception/InvalidJsonException.php',
    'PHPUnit\\Util\\InvalidVersionOperatorException' => $vendorDir . '/phpunit/phpunit/src/Util/Exception/InvalidVersionOperatorException.php',
    'PHPUnit\\Util\\Json' => $vendorDir . '/phpunit/phpunit/src/Util/Json.php',
    'PHPUnit\\Util\\PHP\\DefaultJobRunner' => $vendorDir . '/phpunit/phpunit/src/Util/PHP/DefaultJobRunner.php',
    'PHPUnit\\Util\\PHP\\Job' => $vendorDir . '/phpunit/phpunit/src/Util/PHP/Job.php',
    'PHPUnit\\Util\\PHP\\JobRunner' => $vendorDir . '/phpunit/phpunit/src/Util/PHP/JobRunner.php',
    'PHPUnit\\Util\\PHP\\JobRunnerRegistry' => $vendorDir . '/phpunit/phpunit/src/Util/PHP/JobRunnerRegistry.php',
    'PHPUnit\\Util\\PHP\\PhpProcessException' => $vendorDir . '/phpunit/phpunit/src/Util/Exception/PhpProcessException.php',
    'PHPUnit\\Util\\PHP\\Result' => $vendorDir . '/phpunit/phpunit/src/Util/PHP/Result.php',
    'PHPUnit\\Util\\Reflection' => $vendorDir . '/phpunit/phpunit/src/Util/Reflection.php',
    'PHPUnit\\Util\\Test' => $vendorDir . '/phpunit/phpunit/src/Util/Test.php',
    'PHPUnit\\Util\\ThrowableToStringMapper' => $vendorDir . '/phpunit/phpunit/src/Util/ThrowableToStringMapper.php',
    'PHPUnit\\Util\\VersionComparisonOperator' => $vendorDir . '/phpunit/phpunit/src/Util/VersionComparisonOperator.php',
    'PHPUnit\\Util\\Xml' => $vendorDir . '/phpunit/phpunit/src/Util/Xml/Xml.php',
    'PHPUnit\\Util\\Xml\\Loader' => $vendorDir . '/phpunit/phpunit/src/Util/Xml/Loader.php',
    'PHPUnit\\Util\\Xml\\XmlException' => $vendorDir . '/phpunit/phpunit/src/Util/Exception/XmlException.php',
    'PharIo\\Manifest\\Application' => $vendorDir . '/phar-io/manifest/src/values/Application.php',
    'PharIo\\Manifest\\ApplicationName' => $vendorDir . '/phar-io/manifest/src/values/ApplicationName.php',
    'PharIo\\Manifest\\Author' => $vendorDir . '/phar-io/manifest/src/values/Author.php',
    'PharIo\\Manifest\\AuthorCollection' => $vendorDir . '/phar-io/manifest/src/values/AuthorCollection.php',
    'PharIo\\Manifest\\AuthorCollectionIterator' => $vendorDir . '/phar-io/manifest/src/values/AuthorCollectionIterator.php',
    'PharIo\\Manifest\\AuthorElement' => $vendorDir . '/phar-io/manifest/src/xml/AuthorElement.php',
    'PharIo\\Manifest\\AuthorElementCollection' => $vendorDir . '/phar-io/manifest/src/xml/AuthorElementCollection.php',
    'PharIo\\Manifest\\BundledComponent' => $vendorDir . '/phar-io/manifest/src/values/BundledComponent.php',
    'PharIo\\Manifest\\BundledComponentCollection' => $vendorDir . '/phar-io/manifest/src/values/BundledComponentCollection.php',
    'PharIo\\Manifest\\BundledComponentCollectionIterator' => $vendorDir . '/phar-io/manifest/src/values/BundledComponentCollectionIterator.php',
    'PharIo\\Manifest\\BundlesElement' => $vendorDir . '/phar-io/manifest/src/xml/BundlesElement.php',
    'PharIo\\Manifest\\ComponentElement' => $vendorDir . '/phar-io/manifest/src/xml/ComponentElement.php',
    'PharIo\\Manifest\\ComponentElementCollection' => $vendorDir . '/phar-io/manifest/src/xml/ComponentElementCollection.php',
    'PharIo\\Manifest\\ContainsElement' => $vendorDir . '/phar-io/manifest/src/xml/ContainsElement.php',
    'PharIo\\Manifest\\CopyrightElement' => $vendorDir . '/phar-io/manifest/src/xml/CopyrightElement.php',
    'PharIo\\Manifest\\CopyrightInformation' => $vendorDir . '/phar-io/manifest/src/values/CopyrightInformation.php',
    'PharIo\\Manifest\\ElementCollection' => $vendorDir . '/phar-io/manifest/src/xml/ElementCollection.php',
    'PharIo\\Manifest\\ElementCollectionException' => $vendorDir . '/phar-io/manifest/src/exceptions/ElementCollectionException.php',
    'PharIo\\Manifest\\Email' => $vendorDir . '/phar-io/manifest/src/values/Email.php',
    'PharIo\\Manifest\\Exception' => $vendorDir . '/phar-io/manifest/src/exceptions/Exception.php',
    'PharIo\\Manifest\\ExtElement' => $vendorDir . '/phar-io/manifest/src/xml/ExtElement.php',
    'PharIo\\Manifest\\ExtElementCollection' => $vendorDir . '/phar-io/manifest/src/xml/ExtElementCollection.php',
    'PharIo\\Manifest\\Extension' => $vendorDir . '/phar-io/manifest/src/values/Extension.php',
    'PharIo\\Manifest\\ExtensionElement' => $vendorDir . '/phar-io/manifest/src/xml/ExtensionElement.php',
    'PharIo\\Manifest\\InvalidApplicationNameException' => $vendorDir . '/phar-io/manifest/src/exceptions/InvalidApplicationNameException.php',
    'PharIo\\Manifest\\InvalidEmailException' => $vendorDir . '/phar-io/manifest/src/exceptions/InvalidEmailException.php',
    'PharIo\\Manifest\\InvalidUrlException' => $vendorDir . '/phar-io/manifest/src/exceptions/InvalidUrlException.php',
    'PharIo\\Manifest\\Library' => $vendorDir . '/phar-io/manifest/src/values/Library.php',
    'PharIo\\Manifest\\License' => $vendorDir . '/phar-io/manifest/src/values/License.php',
    'PharIo\\Manifest\\LicenseElement' => $vendorDir . '/phar-io/manifest/src/xml/LicenseElement.php',
    'PharIo\\Manifest\\Manifest' => $vendorDir . '/phar-io/manifest/src/values/Manifest.php',
    'PharIo\\Manifest\\ManifestDocument' => $vendorDir . '/phar-io/manifest/src/xml/ManifestDocument.php',
    'PharIo\\Manifest\\ManifestDocumentException' => $vendorDir . '/phar-io/manifest/src/exceptions/ManifestDocumentException.php',
    'PharIo\\Manifest\\ManifestDocumentLoadingException' => $vendorDir . '/phar-io/manifest/src/exceptions/ManifestDocumentLoadingException.php',
    'PharIo\\Manifest\\ManifestDocumentMapper' => $vendorDir . '/phar-io/manifest/src/ManifestDocumentMapper.php',
    'PharIo\\Manifest\\ManifestDocumentMapperException' => $vendorDir . '/phar-io/manifest/src/exceptions/ManifestDocumentMapperException.php',
    'PharIo\\Manifest\\ManifestElement' => $vendorDir . '/phar-io/manifest/src/xml/ManifestElement.php',
    'PharIo\\Manifest\\ManifestElementException' => $vendorDir . '/phar-io/manifest/src/exceptions/ManifestElementException.php',
    'PharIo\\Manifest\\ManifestLoader' => $vendorDir . '/phar-io/manifest/src/ManifestLoader.php',
    'PharIo\\Manifest\\ManifestLoaderException' => $vendorDir . '/phar-io/manifest/src/exceptions/ManifestLoaderException.php',
    'PharIo\\Manifest\\ManifestSerializer' => $vendorDir . '/phar-io/manifest/src/ManifestSerializer.php',
    'PharIo\\Manifest\\NoEmailAddressException' => $vendorDir . '/phar-io/manifest/src/exceptions/NoEmailAddressException.php',
    'PharIo\\Manifest\\PhpElement' => $vendorDir . '/phar-io/manifest/src/xml/PhpElement.php',
    'PharIo\\Manifest\\PhpExtensionRequirement' => $vendorDir . '/phar-io/manifest/src/values/PhpExtensionRequirement.php',
    'PharIo\\Manifest\\PhpVersionRequirement' => $vendorDir . '/phar-io/manifest/src/values/PhpVersionRequirement.php',
    'PharIo\\Manifest\\Requirement' => $vendorDir . '/phar-io/manifest/src/values/Requirement.php',
    'PharIo\\Manifest\\RequirementCollection' => $vendorDir . '/phar-io/manifest/src/values/RequirementCollection.php',
    'PharIo\\Manifest\\RequirementCollectionIterator' => $vendorDir . '/phar-io/manifest/src/values/RequirementCollectionIterator.php',
    'PharIo\\Manifest\\RequiresElement' => $vendorDir . '/phar-io/manifest/src/xml/RequiresElement.php',
    'PharIo\\Manifest\\Type' => $vendorDir . '/phar-io/manifest/src/values/Type.php',
    'PharIo\\Manifest\\Url' => $vendorDir . '/phar-io/manifest/src/values/Url.php',
    'PharIo\\Version\\AbstractVersionConstraint' => $vendorDir . '/phar-io/version/src/constraints/AbstractVersionConstraint.php',
    'PharIo\\Version\\AndVersionConstraintGroup' => $vendorDir . '/phar-io/version/src/constraints/AndVersionConstraintGroup.php',
    'PharIo\\Version\\AnyVersionConstraint' => $vendorDir . '/phar-io/version/src/constraints/AnyVersionConstraint.php',
    'PharIo\\Version\\BuildMetaData' => $vendorDir . '/phar-io/version/src/BuildMetaData.php',
    'PharIo\\Version\\ExactVersionConstraint' => $vendorDir . '/phar-io/version/src/constraints/ExactVersionConstraint.php',
    'PharIo\\Version\\Exception' => $vendorDir . '/phar-io/version/src/exceptions/Exception.php',
    'PharIo\\Version\\GreaterThanOrEqualToVersionConstraint' => $vendorDir . '/phar-io/version/src/constraints/GreaterThanOrEqualToVersionConstraint.php',
    'PharIo\\Version\\InvalidPreReleaseSuffixException' => $vendorDir . '/phar-io/version/src/exceptions/InvalidPreReleaseSuffixException.php',
    'PharIo\\Version\\InvalidVersionException' => $vendorDir . '/phar-io/version/src/exceptions/InvalidVersionException.php',
    'PharIo\\Version\\NoBuildMetaDataException' => $vendorDir . '/phar-io/version/src/exceptions/NoBuildMetaDataException.php',
    'PharIo\\Version\\NoPreReleaseSuffixException' => $vendorDir . '/phar-io/version/src/exceptions/NoPreReleaseSuffixException.php',
    'PharIo\\Version\\OrVersionConstraintGroup' => $vendorDir . '/phar-io/version/src/constraints/OrVersionConstraintGroup.php',
    'PharIo\\Version\\PreReleaseSuffix' => $vendorDir . '/phar-io/version/src/PreReleaseSuffix.php',
    'PharIo\\Version\\SpecificMajorAndMinorVersionConstraint' => $vendorDir . '/phar-io/version/src/constraints/SpecificMajorAndMinorVersionConstraint.php',
    'PharIo\\Version\\SpecificMajorVersionConstraint' => $vendorDir . '/phar-io/version/src/constraints/SpecificMajorVersionConstraint.php',
    'PharIo\\Version\\UnsupportedVersionConstraintException' => $vendorDir . '/phar-io/version/src/exceptions/UnsupportedVersionConstraintException.php',
    'PharIo\\Version\\Version' => $vendorDir . '/phar-io/version/src/Version.php',
    'PharIo\\Version\\VersionConstraint' => $vendorDir . '/phar-io/version/src/constraints/VersionConstraint.php',
    'PharIo\\Version\\VersionConstraintParser' => $vendorDir . '/phar-io/version/src/VersionConstraintParser.php',
    'PharIo\\Version\\VersionConstraintValue' => $vendorDir . '/phar-io/version/src/VersionConstraintValue.php',
    'PharIo\\Version\\VersionNumber' => $vendorDir . '/phar-io/version/src/VersionNumber.php',
    'PhpCsFixer\\AbstractDoctrineAnnotationFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/AbstractDoctrineAnnotationFixer.php',
    'PhpCsFixer\\AbstractFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/AbstractFixer.php',
    'PhpCsFixer\\AbstractFopenFlagFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/AbstractFopenFlagFixer.php',
    'PhpCsFixer\\AbstractFunctionReferenceFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/AbstractFunctionReferenceFixer.php',
    'PhpCsFixer\\AbstractNoUselessElseFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/AbstractNoUselessElseFixer.php',
    'PhpCsFixer\\AbstractPhpdocToTypeDeclarationFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/AbstractPhpdocToTypeDeclarationFixer.php',
    'PhpCsFixer\\AbstractPhpdocTypesFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/AbstractPhpdocTypesFixer.php',
    'PhpCsFixer\\AbstractProxyFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/AbstractProxyFixer.php',
    'PhpCsFixer\\Cache\\Cache' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Cache/Cache.php',
    'PhpCsFixer\\Cache\\CacheInterface' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Cache/CacheInterface.php',
    'PhpCsFixer\\Cache\\CacheManagerInterface' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Cache/CacheManagerInterface.php',
    'PhpCsFixer\\Cache\\Directory' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Cache/Directory.php',
    'PhpCsFixer\\Cache\\DirectoryInterface' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Cache/DirectoryInterface.php',
    'PhpCsFixer\\Cache\\FileCacheManager' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Cache/FileCacheManager.php',
    'PhpCsFixer\\Cache\\FileHandler' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Cache/FileHandler.php',
    'PhpCsFixer\\Cache\\FileHandlerInterface' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Cache/FileHandlerInterface.php',
    'PhpCsFixer\\Cache\\NullCacheManager' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Cache/NullCacheManager.php',
    'PhpCsFixer\\Cache\\Signature' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Cache/Signature.php',
    'PhpCsFixer\\Cache\\SignatureInterface' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Cache/SignatureInterface.php',
    'PhpCsFixer\\Config' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Config.php',
    'PhpCsFixer\\ConfigInterface' => $vendorDir . '/friendsofphp/php-cs-fixer/src/ConfigInterface.php',
    'PhpCsFixer\\ConfigurationException\\InvalidConfigurationException' => $vendorDir . '/friendsofphp/php-cs-fixer/src/ConfigurationException/InvalidConfigurationException.php',
    'PhpCsFixer\\ConfigurationException\\InvalidFixerConfigurationException' => $vendorDir . '/friendsofphp/php-cs-fixer/src/ConfigurationException/InvalidFixerConfigurationException.php',
    'PhpCsFixer\\ConfigurationException\\InvalidForEnvFixerConfigurationException' => $vendorDir . '/friendsofphp/php-cs-fixer/src/ConfigurationException/InvalidForEnvFixerConfigurationException.php',
    'PhpCsFixer\\ConfigurationException\\RequiredFixerConfigurationException' => $vendorDir . '/friendsofphp/php-cs-fixer/src/ConfigurationException/RequiredFixerConfigurationException.php',
    'PhpCsFixer\\Console\\Application' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Console/Application.php',
    'PhpCsFixer\\Console\\Command\\CheckCommand' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Console/Command/CheckCommand.php',
    'PhpCsFixer\\Console\\Command\\DescribeCommand' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Console/Command/DescribeCommand.php',
    'PhpCsFixer\\Console\\Command\\DescribeNameNotFoundException' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Console/Command/DescribeNameNotFoundException.php',
    'PhpCsFixer\\Console\\Command\\DocumentationCommand' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Console/Command/DocumentationCommand.php',
    'PhpCsFixer\\Console\\Command\\FixCommand' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Console/Command/FixCommand.php',
    'PhpCsFixer\\Console\\Command\\FixCommandExitStatusCalculator' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Console/Command/FixCommandExitStatusCalculator.php',
    'PhpCsFixer\\Console\\Command\\HelpCommand' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Console/Command/HelpCommand.php',
    'PhpCsFixer\\Console\\Command\\ListFilesCommand' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Console/Command/ListFilesCommand.php',
    'PhpCsFixer\\Console\\Command\\ListSetsCommand' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Console/Command/ListSetsCommand.php',
    'PhpCsFixer\\Console\\Command\\SelfUpdateCommand' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Console/Command/SelfUpdateCommand.php',
    'PhpCsFixer\\Console\\Command\\WorkerCommand' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Console/Command/WorkerCommand.php',
    'PhpCsFixer\\Console\\ConfigurationResolver' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Console/ConfigurationResolver.php',
    'PhpCsFixer\\Console\\Output\\ErrorOutput' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Console/Output/ErrorOutput.php',
    'PhpCsFixer\\Console\\Output\\OutputContext' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Console/Output/OutputContext.php',
    'PhpCsFixer\\Console\\Output\\Progress\\DotsOutput' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Console/Output/Progress/DotsOutput.php',
    'PhpCsFixer\\Console\\Output\\Progress\\NullOutput' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Console/Output/Progress/NullOutput.php',
    'PhpCsFixer\\Console\\Output\\Progress\\PercentageBarOutput' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Console/Output/Progress/PercentageBarOutput.php',
    'PhpCsFixer\\Console\\Output\\Progress\\ProgressOutputFactory' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Console/Output/Progress/ProgressOutputFactory.php',
    'PhpCsFixer\\Console\\Output\\Progress\\ProgressOutputInterface' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Console/Output/Progress/ProgressOutputInterface.php',
    'PhpCsFixer\\Console\\Output\\Progress\\ProgressOutputType' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Console/Output/Progress/ProgressOutputType.php',
    'PhpCsFixer\\Console\\Report\\FixReport\\CheckstyleReporter' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Console/Report/FixReport/CheckstyleReporter.php',
    'PhpCsFixer\\Console\\Report\\FixReport\\GitlabReporter' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Console/Report/FixReport/GitlabReporter.php',
    'PhpCsFixer\\Console\\Report\\FixReport\\JsonReporter' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Console/Report/FixReport/JsonReporter.php',
    'PhpCsFixer\\Console\\Report\\FixReport\\JunitReporter' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Console/Report/FixReport/JunitReporter.php',
    'PhpCsFixer\\Console\\Report\\FixReport\\ReportSummary' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Console/Report/FixReport/ReportSummary.php',
    'PhpCsFixer\\Console\\Report\\FixReport\\ReporterFactory' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Console/Report/FixReport/ReporterFactory.php',
    'PhpCsFixer\\Console\\Report\\FixReport\\ReporterInterface' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Console/Report/FixReport/ReporterInterface.php',
    'PhpCsFixer\\Console\\Report\\FixReport\\TextReporter' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Console/Report/FixReport/TextReporter.php',
    'PhpCsFixer\\Console\\Report\\FixReport\\XmlReporter' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Console/Report/FixReport/XmlReporter.php',
    'PhpCsFixer\\Console\\Report\\ListSetsReport\\JsonReporter' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Console/Report/ListSetsReport/JsonReporter.php',
    'PhpCsFixer\\Console\\Report\\ListSetsReport\\ReportSummary' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Console/Report/ListSetsReport/ReportSummary.php',
    'PhpCsFixer\\Console\\Report\\ListSetsReport\\ReporterFactory' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Console/Report/ListSetsReport/ReporterFactory.php',
    'PhpCsFixer\\Console\\Report\\ListSetsReport\\ReporterInterface' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Console/Report/ListSetsReport/ReporterInterface.php',
    'PhpCsFixer\\Console\\Report\\ListSetsReport\\TextReporter' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Console/Report/ListSetsReport/TextReporter.php',
    'PhpCsFixer\\Console\\SelfUpdate\\GithubClient' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Console/SelfUpdate/GithubClient.php',
    'PhpCsFixer\\Console\\SelfUpdate\\GithubClientInterface' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Console/SelfUpdate/GithubClientInterface.php',
    'PhpCsFixer\\Console\\SelfUpdate\\NewVersionChecker' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Console/SelfUpdate/NewVersionChecker.php',
    'PhpCsFixer\\Console\\SelfUpdate\\NewVersionCheckerInterface' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Console/SelfUpdate/NewVersionCheckerInterface.php',
    'PhpCsFixer\\Console\\WarningsDetector' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Console/WarningsDetector.php',
    'PhpCsFixer\\Differ\\DiffConsoleFormatter' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Differ/DiffConsoleFormatter.php',
    'PhpCsFixer\\Differ\\DifferInterface' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Differ/DifferInterface.php',
    'PhpCsFixer\\Differ\\FullDiffer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Differ/FullDiffer.php',
    'PhpCsFixer\\Differ\\NullDiffer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Differ/NullDiffer.php',
    'PhpCsFixer\\Differ\\UnifiedDiffer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Differ/UnifiedDiffer.php',
    'PhpCsFixer\\DocBlock\\Annotation' => $vendorDir . '/friendsofphp/php-cs-fixer/src/DocBlock/Annotation.php',
    'PhpCsFixer\\DocBlock\\DocBlock' => $vendorDir . '/friendsofphp/php-cs-fixer/src/DocBlock/DocBlock.php',
    'PhpCsFixer\\DocBlock\\Line' => $vendorDir . '/friendsofphp/php-cs-fixer/src/DocBlock/Line.php',
    'PhpCsFixer\\DocBlock\\ShortDescription' => $vendorDir . '/friendsofphp/php-cs-fixer/src/DocBlock/ShortDescription.php',
    'PhpCsFixer\\DocBlock\\Tag' => $vendorDir . '/friendsofphp/php-cs-fixer/src/DocBlock/Tag.php',
    'PhpCsFixer\\DocBlock\\TagComparator' => $vendorDir . '/friendsofphp/php-cs-fixer/src/DocBlock/TagComparator.php',
    'PhpCsFixer\\DocBlock\\TypeExpression' => $vendorDir . '/friendsofphp/php-cs-fixer/src/DocBlock/TypeExpression.php',
    'PhpCsFixer\\Doctrine\\Annotation\\DocLexer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Doctrine/Annotation/DocLexer.php',
    'PhpCsFixer\\Doctrine\\Annotation\\Token' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Doctrine/Annotation/Token.php',
    'PhpCsFixer\\Doctrine\\Annotation\\Tokens' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Doctrine/Annotation/Tokens.php',
    'PhpCsFixer\\Documentation\\DocumentationLocator' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Documentation/DocumentationLocator.php',
    'PhpCsFixer\\Documentation\\FixerDocumentGenerator' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Documentation/FixerDocumentGenerator.php',
    'PhpCsFixer\\Documentation\\RstUtils' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Documentation/RstUtils.php',
    'PhpCsFixer\\Documentation\\RuleSetDocumentationGenerator' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Documentation/RuleSetDocumentationGenerator.php',
    'PhpCsFixer\\Error\\Error' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Error/Error.php',
    'PhpCsFixer\\Error\\ErrorsManager' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Error/ErrorsManager.php',
    'PhpCsFixer\\Error\\SourceExceptionFactory' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Error/SourceExceptionFactory.php',
    'PhpCsFixer\\ExecutorWithoutErrorHandler' => $vendorDir . '/friendsofphp/php-cs-fixer/src/ExecutorWithoutErrorHandler.php',
    'PhpCsFixer\\ExecutorWithoutErrorHandlerException' => $vendorDir . '/friendsofphp/php-cs-fixer/src/ExecutorWithoutErrorHandlerException.php',
    'PhpCsFixer\\FileReader' => $vendorDir . '/friendsofphp/php-cs-fixer/src/FileReader.php',
    'PhpCsFixer\\FileRemoval' => $vendorDir . '/friendsofphp/php-cs-fixer/src/FileRemoval.php',
    'PhpCsFixer\\Finder' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Finder.php',
    'PhpCsFixer\\FixerConfiguration\\AliasedFixerOption' => $vendorDir . '/friendsofphp/php-cs-fixer/src/FixerConfiguration/AliasedFixerOption.php',
    'PhpCsFixer\\FixerConfiguration\\AliasedFixerOptionBuilder' => $vendorDir . '/friendsofphp/php-cs-fixer/src/FixerConfiguration/AliasedFixerOptionBuilder.php',
    'PhpCsFixer\\FixerConfiguration\\AllowedValueSubset' => $vendorDir . '/friendsofphp/php-cs-fixer/src/FixerConfiguration/AllowedValueSubset.php',
    'PhpCsFixer\\FixerConfiguration\\DeprecatedFixerOption' => $vendorDir . '/friendsofphp/php-cs-fixer/src/FixerConfiguration/DeprecatedFixerOption.php',
    'PhpCsFixer\\FixerConfiguration\\DeprecatedFixerOptionInterface' => $vendorDir . '/friendsofphp/php-cs-fixer/src/FixerConfiguration/DeprecatedFixerOptionInterface.php',
    'PhpCsFixer\\FixerConfiguration\\FixerConfigurationResolver' => $vendorDir . '/friendsofphp/php-cs-fixer/src/FixerConfiguration/FixerConfigurationResolver.php',
    'PhpCsFixer\\FixerConfiguration\\FixerConfigurationResolverInterface' => $vendorDir . '/friendsofphp/php-cs-fixer/src/FixerConfiguration/FixerConfigurationResolverInterface.php',
    'PhpCsFixer\\FixerConfiguration\\FixerOption' => $vendorDir . '/friendsofphp/php-cs-fixer/src/FixerConfiguration/FixerOption.php',
    'PhpCsFixer\\FixerConfiguration\\FixerOptionBuilder' => $vendorDir . '/friendsofphp/php-cs-fixer/src/FixerConfiguration/FixerOptionBuilder.php',
    'PhpCsFixer\\FixerConfiguration\\FixerOptionInterface' => $vendorDir . '/friendsofphp/php-cs-fixer/src/FixerConfiguration/FixerOptionInterface.php',
    'PhpCsFixer\\FixerConfiguration\\FixerOptionSorter' => $vendorDir . '/friendsofphp/php-cs-fixer/src/FixerConfiguration/FixerOptionSorter.php',
    'PhpCsFixer\\FixerConfiguration\\InvalidOptionsForEnvException' => $vendorDir . '/friendsofphp/php-cs-fixer/src/FixerConfiguration/InvalidOptionsForEnvException.php',
    'PhpCsFixer\\FixerDefinition\\CodeSample' => $vendorDir . '/friendsofphp/php-cs-fixer/src/FixerDefinition/CodeSample.php',
    'PhpCsFixer\\FixerDefinition\\CodeSampleInterface' => $vendorDir . '/friendsofphp/php-cs-fixer/src/FixerDefinition/CodeSampleInterface.php',
    'PhpCsFixer\\FixerDefinition\\FileSpecificCodeSample' => $vendorDir . '/friendsofphp/php-cs-fixer/src/FixerDefinition/FileSpecificCodeSample.php',
    'PhpCsFixer\\FixerDefinition\\FileSpecificCodeSampleInterface' => $vendorDir . '/friendsofphp/php-cs-fixer/src/FixerDefinition/FileSpecificCodeSampleInterface.php',
    'PhpCsFixer\\FixerDefinition\\FixerDefinition' => $vendorDir . '/friendsofphp/php-cs-fixer/src/FixerDefinition/FixerDefinition.php',
    'PhpCsFixer\\FixerDefinition\\FixerDefinitionInterface' => $vendorDir . '/friendsofphp/php-cs-fixer/src/FixerDefinition/FixerDefinitionInterface.php',
    'PhpCsFixer\\FixerDefinition\\VersionSpecificCodeSample' => $vendorDir . '/friendsofphp/php-cs-fixer/src/FixerDefinition/VersionSpecificCodeSample.php',
    'PhpCsFixer\\FixerDefinition\\VersionSpecificCodeSampleInterface' => $vendorDir . '/friendsofphp/php-cs-fixer/src/FixerDefinition/VersionSpecificCodeSampleInterface.php',
    'PhpCsFixer\\FixerDefinition\\VersionSpecification' => $vendorDir . '/friendsofphp/php-cs-fixer/src/FixerDefinition/VersionSpecification.php',
    'PhpCsFixer\\FixerDefinition\\VersionSpecificationInterface' => $vendorDir . '/friendsofphp/php-cs-fixer/src/FixerDefinition/VersionSpecificationInterface.php',
    'PhpCsFixer\\FixerFactory' => $vendorDir . '/friendsofphp/php-cs-fixer/src/FixerFactory.php',
    'PhpCsFixer\\FixerNameValidator' => $vendorDir . '/friendsofphp/php-cs-fixer/src/FixerNameValidator.php',
    'PhpCsFixer\\Fixer\\AbstractIncrementOperatorFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/AbstractIncrementOperatorFixer.php',
    'PhpCsFixer\\Fixer\\AbstractPhpUnitFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/AbstractPhpUnitFixer.php',
    'PhpCsFixer\\Fixer\\AbstractShortOperatorFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/AbstractShortOperatorFixer.php',
    'PhpCsFixer\\Fixer\\Alias\\ArrayPushFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Alias/ArrayPushFixer.php',
    'PhpCsFixer\\Fixer\\Alias\\BacktickToShellExecFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Alias/BacktickToShellExecFixer.php',
    'PhpCsFixer\\Fixer\\Alias\\EregToPregFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Alias/EregToPregFixer.php',
    'PhpCsFixer\\Fixer\\Alias\\MbStrFunctionsFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Alias/MbStrFunctionsFixer.php',
    'PhpCsFixer\\Fixer\\Alias\\ModernizeStrposFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Alias/ModernizeStrposFixer.php',
    'PhpCsFixer\\Fixer\\Alias\\NoAliasFunctionsFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Alias/NoAliasFunctionsFixer.php',
    'PhpCsFixer\\Fixer\\Alias\\NoAliasLanguageConstructCallFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Alias/NoAliasLanguageConstructCallFixer.php',
    'PhpCsFixer\\Fixer\\Alias\\NoMixedEchoPrintFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Alias/NoMixedEchoPrintFixer.php',
    'PhpCsFixer\\Fixer\\Alias\\PowToExponentiationFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Alias/PowToExponentiationFixer.php',
    'PhpCsFixer\\Fixer\\Alias\\RandomApiMigrationFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Alias/RandomApiMigrationFixer.php',
    'PhpCsFixer\\Fixer\\Alias\\SetTypeToCastFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Alias/SetTypeToCastFixer.php',
    'PhpCsFixer\\Fixer\\ArrayNotation\\ArraySyntaxFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ArrayNotation/ArraySyntaxFixer.php',
    'PhpCsFixer\\Fixer\\ArrayNotation\\NoMultilineWhitespaceAroundDoubleArrowFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ArrayNotation/NoMultilineWhitespaceAroundDoubleArrowFixer.php',
    'PhpCsFixer\\Fixer\\ArrayNotation\\NoTrailingCommaInSinglelineArrayFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ArrayNotation/NoTrailingCommaInSinglelineArrayFixer.php',
    'PhpCsFixer\\Fixer\\ArrayNotation\\NoWhitespaceBeforeCommaInArrayFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ArrayNotation/NoWhitespaceBeforeCommaInArrayFixer.php',
    'PhpCsFixer\\Fixer\\ArrayNotation\\NormalizeIndexBraceFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ArrayNotation/NormalizeIndexBraceFixer.php',
    'PhpCsFixer\\Fixer\\ArrayNotation\\ReturnToYieldFromFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ArrayNotation/ReturnToYieldFromFixer.php',
    'PhpCsFixer\\Fixer\\ArrayNotation\\TrimArraySpacesFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ArrayNotation/TrimArraySpacesFixer.php',
    'PhpCsFixer\\Fixer\\ArrayNotation\\WhitespaceAfterCommaInArrayFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ArrayNotation/WhitespaceAfterCommaInArrayFixer.php',
    'PhpCsFixer\\Fixer\\ArrayNotation\\YieldFromArrayToYieldsFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ArrayNotation/YieldFromArrayToYieldsFixer.php',
    'PhpCsFixer\\Fixer\\AttributeNotation\\AttributeEmptyParenthesesFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/AttributeNotation/AttributeEmptyParenthesesFixer.php',
    'PhpCsFixer\\Fixer\\AttributeNotation\\GeneralAttributeRemoveFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/AttributeNotation/GeneralAttributeRemoveFixer.php',
    'PhpCsFixer\\Fixer\\AttributeNotation\\OrderedAttributesFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/AttributeNotation/OrderedAttributesFixer.php',
    'PhpCsFixer\\Fixer\\Basic\\BracesFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Basic/BracesFixer.php',
    'PhpCsFixer\\Fixer\\Basic\\BracesPositionFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Basic/BracesPositionFixer.php',
    'PhpCsFixer\\Fixer\\Basic\\CurlyBracesPositionFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Basic/CurlyBracesPositionFixer.php',
    'PhpCsFixer\\Fixer\\Basic\\EncodingFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Basic/EncodingFixer.php',
    'PhpCsFixer\\Fixer\\Basic\\NoMultipleStatementsPerLineFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Basic/NoMultipleStatementsPerLineFixer.php',
    'PhpCsFixer\\Fixer\\Basic\\NoTrailingCommaInSinglelineFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Basic/NoTrailingCommaInSinglelineFixer.php',
    'PhpCsFixer\\Fixer\\Basic\\NonPrintableCharacterFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Basic/NonPrintableCharacterFixer.php',
    'PhpCsFixer\\Fixer\\Basic\\NumericLiteralSeparatorFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Basic/NumericLiteralSeparatorFixer.php',
    'PhpCsFixer\\Fixer\\Basic\\OctalNotationFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Basic/OctalNotationFixer.php',
    'PhpCsFixer\\Fixer\\Basic\\PsrAutoloadingFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Basic/PsrAutoloadingFixer.php',
    'PhpCsFixer\\Fixer\\Basic\\SingleLineEmptyBodyFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Basic/SingleLineEmptyBodyFixer.php',
    'PhpCsFixer\\Fixer\\Casing\\ClassReferenceNameCasingFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Casing/ClassReferenceNameCasingFixer.php',
    'PhpCsFixer\\Fixer\\Casing\\ConstantCaseFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Casing/ConstantCaseFixer.php',
    'PhpCsFixer\\Fixer\\Casing\\IntegerLiteralCaseFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Casing/IntegerLiteralCaseFixer.php',
    'PhpCsFixer\\Fixer\\Casing\\LowercaseKeywordsFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Casing/LowercaseKeywordsFixer.php',
    'PhpCsFixer\\Fixer\\Casing\\LowercaseStaticReferenceFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Casing/LowercaseStaticReferenceFixer.php',
    'PhpCsFixer\\Fixer\\Casing\\MagicConstantCasingFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Casing/MagicConstantCasingFixer.php',
    'PhpCsFixer\\Fixer\\Casing\\MagicMethodCasingFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Casing/MagicMethodCasingFixer.php',
    'PhpCsFixer\\Fixer\\Casing\\NativeFunctionCasingFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Casing/NativeFunctionCasingFixer.php',
    'PhpCsFixer\\Fixer\\Casing\\NativeFunctionTypeDeclarationCasingFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Casing/NativeFunctionTypeDeclarationCasingFixer.php',
    'PhpCsFixer\\Fixer\\Casing\\NativeTypeDeclarationCasingFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Casing/NativeTypeDeclarationCasingFixer.php',
    'PhpCsFixer\\Fixer\\CastNotation\\CastSpacesFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/CastNotation/CastSpacesFixer.php',
    'PhpCsFixer\\Fixer\\CastNotation\\LowercaseCastFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/CastNotation/LowercaseCastFixer.php',
    'PhpCsFixer\\Fixer\\CastNotation\\ModernizeTypesCastingFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/CastNotation/ModernizeTypesCastingFixer.php',
    'PhpCsFixer\\Fixer\\CastNotation\\NoShortBoolCastFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/CastNotation/NoShortBoolCastFixer.php',
    'PhpCsFixer\\Fixer\\CastNotation\\NoUnsetCastFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/CastNotation/NoUnsetCastFixer.php',
    'PhpCsFixer\\Fixer\\CastNotation\\ShortScalarCastFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/CastNotation/ShortScalarCastFixer.php',
    'PhpCsFixer\\Fixer\\ClassNotation\\ClassAttributesSeparationFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ClassNotation/ClassAttributesSeparationFixer.php',
    'PhpCsFixer\\Fixer\\ClassNotation\\ClassDefinitionFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ClassNotation/ClassDefinitionFixer.php',
    'PhpCsFixer\\Fixer\\ClassNotation\\FinalClassFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ClassNotation/FinalClassFixer.php',
    'PhpCsFixer\\Fixer\\ClassNotation\\FinalInternalClassFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ClassNotation/FinalInternalClassFixer.php',
    'PhpCsFixer\\Fixer\\ClassNotation\\FinalPublicMethodForAbstractClassFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ClassNotation/FinalPublicMethodForAbstractClassFixer.php',
    'PhpCsFixer\\Fixer\\ClassNotation\\NoBlankLinesAfterClassOpeningFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ClassNotation/NoBlankLinesAfterClassOpeningFixer.php',
    'PhpCsFixer\\Fixer\\ClassNotation\\NoNullPropertyInitializationFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ClassNotation/NoNullPropertyInitializationFixer.php',
    'PhpCsFixer\\Fixer\\ClassNotation\\NoPhp4ConstructorFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ClassNotation/NoPhp4ConstructorFixer.php',
    'PhpCsFixer\\Fixer\\ClassNotation\\NoUnneededFinalMethodFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ClassNotation/NoUnneededFinalMethodFixer.php',
    'PhpCsFixer\\Fixer\\ClassNotation\\OrderedClassElementsFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ClassNotation/OrderedClassElementsFixer.php',
    'PhpCsFixer\\Fixer\\ClassNotation\\OrderedInterfacesFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ClassNotation/OrderedInterfacesFixer.php',
    'PhpCsFixer\\Fixer\\ClassNotation\\OrderedTraitsFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ClassNotation/OrderedTraitsFixer.php',
    'PhpCsFixer\\Fixer\\ClassNotation\\OrderedTypesFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ClassNotation/OrderedTypesFixer.php',
    'PhpCsFixer\\Fixer\\ClassNotation\\PhpdocReadonlyClassCommentToKeywordFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ClassNotation/PhpdocReadonlyClassCommentToKeywordFixer.php',
    'PhpCsFixer\\Fixer\\ClassNotation\\ProtectedToPrivateFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ClassNotation/ProtectedToPrivateFixer.php',
    'PhpCsFixer\\Fixer\\ClassNotation\\SelfAccessorFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ClassNotation/SelfAccessorFixer.php',
    'PhpCsFixer\\Fixer\\ClassNotation\\SelfStaticAccessorFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ClassNotation/SelfStaticAccessorFixer.php',
    'PhpCsFixer\\Fixer\\ClassNotation\\SingleClassElementPerStatementFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ClassNotation/SingleClassElementPerStatementFixer.php',
    'PhpCsFixer\\Fixer\\ClassNotation\\SingleTraitInsertPerStatementFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ClassNotation/SingleTraitInsertPerStatementFixer.php',
    'PhpCsFixer\\Fixer\\ClassNotation\\VisibilityRequiredFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ClassNotation/VisibilityRequiredFixer.php',
    'PhpCsFixer\\Fixer\\ClassUsage\\DateTimeImmutableFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ClassUsage/DateTimeImmutableFixer.php',
    'PhpCsFixer\\Fixer\\Comment\\CommentToPhpdocFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Comment/CommentToPhpdocFixer.php',
    'PhpCsFixer\\Fixer\\Comment\\HeaderCommentFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Comment/HeaderCommentFixer.php',
    'PhpCsFixer\\Fixer\\Comment\\MultilineCommentOpeningClosingFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Comment/MultilineCommentOpeningClosingFixer.php',
    'PhpCsFixer\\Fixer\\Comment\\NoEmptyCommentFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Comment/NoEmptyCommentFixer.php',
    'PhpCsFixer\\Fixer\\Comment\\NoTrailingWhitespaceInCommentFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Comment/NoTrailingWhitespaceInCommentFixer.php',
    'PhpCsFixer\\Fixer\\Comment\\SingleLineCommentSpacingFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Comment/SingleLineCommentSpacingFixer.php',
    'PhpCsFixer\\Fixer\\Comment\\SingleLineCommentStyleFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Comment/SingleLineCommentStyleFixer.php',
    'PhpCsFixer\\Fixer\\ConfigurableFixerInterface' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ConfigurableFixerInterface.php',
    'PhpCsFixer\\Fixer\\ConfigurableFixerTrait' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ConfigurableFixerTrait.php',
    'PhpCsFixer\\Fixer\\ConstantNotation\\NativeConstantInvocationFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ConstantNotation/NativeConstantInvocationFixer.php',
    'PhpCsFixer\\Fixer\\ControlStructure\\ControlStructureBracesFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ControlStructure/ControlStructureBracesFixer.php',
    'PhpCsFixer\\Fixer\\ControlStructure\\ControlStructureContinuationPositionFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ControlStructure/ControlStructureContinuationPositionFixer.php',
    'PhpCsFixer\\Fixer\\ControlStructure\\ElseifFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ControlStructure/ElseifFixer.php',
    'PhpCsFixer\\Fixer\\ControlStructure\\EmptyLoopBodyFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ControlStructure/EmptyLoopBodyFixer.php',
    'PhpCsFixer\\Fixer\\ControlStructure\\EmptyLoopConditionFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ControlStructure/EmptyLoopConditionFixer.php',
    'PhpCsFixer\\Fixer\\ControlStructure\\IncludeFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ControlStructure/IncludeFixer.php',
    'PhpCsFixer\\Fixer\\ControlStructure\\NoAlternativeSyntaxFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ControlStructure/NoAlternativeSyntaxFixer.php',
    'PhpCsFixer\\Fixer\\ControlStructure\\NoBreakCommentFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ControlStructure/NoBreakCommentFixer.php',
    'PhpCsFixer\\Fixer\\ControlStructure\\NoSuperfluousElseifFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ControlStructure/NoSuperfluousElseifFixer.php',
    'PhpCsFixer\\Fixer\\ControlStructure\\NoTrailingCommaInListCallFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ControlStructure/NoTrailingCommaInListCallFixer.php',
    'PhpCsFixer\\Fixer\\ControlStructure\\NoUnneededBracesFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ControlStructure/NoUnneededBracesFixer.php',
    'PhpCsFixer\\Fixer\\ControlStructure\\NoUnneededControlParenthesesFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ControlStructure/NoUnneededControlParenthesesFixer.php',
    'PhpCsFixer\\Fixer\\ControlStructure\\NoUnneededCurlyBracesFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ControlStructure/NoUnneededCurlyBracesFixer.php',
    'PhpCsFixer\\Fixer\\ControlStructure\\NoUselessElseFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ControlStructure/NoUselessElseFixer.php',
    'PhpCsFixer\\Fixer\\ControlStructure\\SimplifiedIfReturnFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ControlStructure/SimplifiedIfReturnFixer.php',
    'PhpCsFixer\\Fixer\\ControlStructure\\SwitchCaseSemicolonToColonFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ControlStructure/SwitchCaseSemicolonToColonFixer.php',
    'PhpCsFixer\\Fixer\\ControlStructure\\SwitchCaseSpaceFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ControlStructure/SwitchCaseSpaceFixer.php',
    'PhpCsFixer\\Fixer\\ControlStructure\\SwitchContinueToBreakFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ControlStructure/SwitchContinueToBreakFixer.php',
    'PhpCsFixer\\Fixer\\ControlStructure\\TrailingCommaInMultilineFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ControlStructure/TrailingCommaInMultilineFixer.php',
    'PhpCsFixer\\Fixer\\ControlStructure\\YodaStyleFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ControlStructure/YodaStyleFixer.php',
    'PhpCsFixer\\Fixer\\DeprecatedFixerInterface' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/DeprecatedFixerInterface.php',
    'PhpCsFixer\\Fixer\\DoctrineAnnotation\\DoctrineAnnotationArrayAssignmentFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/DoctrineAnnotation/DoctrineAnnotationArrayAssignmentFixer.php',
    'PhpCsFixer\\Fixer\\DoctrineAnnotation\\DoctrineAnnotationBracesFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/DoctrineAnnotation/DoctrineAnnotationBracesFixer.php',
    'PhpCsFixer\\Fixer\\DoctrineAnnotation\\DoctrineAnnotationIndentationFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/DoctrineAnnotation/DoctrineAnnotationIndentationFixer.php',
    'PhpCsFixer\\Fixer\\DoctrineAnnotation\\DoctrineAnnotationSpacesFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/DoctrineAnnotation/DoctrineAnnotationSpacesFixer.php',
    'PhpCsFixer\\Fixer\\ExperimentalFixerInterface' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ExperimentalFixerInterface.php',
    'PhpCsFixer\\Fixer\\FixerInterface' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/FixerInterface.php',
    'PhpCsFixer\\Fixer\\FunctionNotation\\CombineNestedDirnameFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/FunctionNotation/CombineNestedDirnameFixer.php',
    'PhpCsFixer\\Fixer\\FunctionNotation\\DateTimeCreateFromFormatCallFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/FunctionNotation/DateTimeCreateFromFormatCallFixer.php',
    'PhpCsFixer\\Fixer\\FunctionNotation\\FopenFlagOrderFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/FunctionNotation/FopenFlagOrderFixer.php',
    'PhpCsFixer\\Fixer\\FunctionNotation\\FopenFlagsFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/FunctionNotation/FopenFlagsFixer.php',
    'PhpCsFixer\\Fixer\\FunctionNotation\\FunctionDeclarationFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/FunctionNotation/FunctionDeclarationFixer.php',
    'PhpCsFixer\\Fixer\\FunctionNotation\\FunctionTypehintSpaceFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/FunctionNotation/FunctionTypehintSpaceFixer.php',
    'PhpCsFixer\\Fixer\\FunctionNotation\\ImplodeCallFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/FunctionNotation/ImplodeCallFixer.php',
    'PhpCsFixer\\Fixer\\FunctionNotation\\LambdaNotUsedImportFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/FunctionNotation/LambdaNotUsedImportFixer.php',
    'PhpCsFixer\\Fixer\\FunctionNotation\\MethodArgumentSpaceFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/FunctionNotation/MethodArgumentSpaceFixer.php',
    'PhpCsFixer\\Fixer\\FunctionNotation\\NativeFunctionInvocationFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/FunctionNotation/NativeFunctionInvocationFixer.php',
    'PhpCsFixer\\Fixer\\FunctionNotation\\NoSpacesAfterFunctionNameFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/FunctionNotation/NoSpacesAfterFunctionNameFixer.php',
    'PhpCsFixer\\Fixer\\FunctionNotation\\NoTrailingCommaInSinglelineFunctionCallFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/FunctionNotation/NoTrailingCommaInSinglelineFunctionCallFixer.php',
    'PhpCsFixer\\Fixer\\FunctionNotation\\NoUnreachableDefaultArgumentValueFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/FunctionNotation/NoUnreachableDefaultArgumentValueFixer.php',
    'PhpCsFixer\\Fixer\\FunctionNotation\\NoUselessSprintfFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/FunctionNotation/NoUselessSprintfFixer.php',
    'PhpCsFixer\\Fixer\\FunctionNotation\\NullableTypeDeclarationForDefaultNullValueFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/FunctionNotation/NullableTypeDeclarationForDefaultNullValueFixer.php',
    'PhpCsFixer\\Fixer\\FunctionNotation\\PhpdocToParamTypeFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/FunctionNotation/PhpdocToParamTypeFixer.php',
    'PhpCsFixer\\Fixer\\FunctionNotation\\PhpdocToPropertyTypeFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/FunctionNotation/PhpdocToPropertyTypeFixer.php',
    'PhpCsFixer\\Fixer\\FunctionNotation\\PhpdocToReturnTypeFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/FunctionNotation/PhpdocToReturnTypeFixer.php',
    'PhpCsFixer\\Fixer\\FunctionNotation\\RegularCallableCallFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/FunctionNotation/RegularCallableCallFixer.php',
    'PhpCsFixer\\Fixer\\FunctionNotation\\ReturnTypeDeclarationFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/FunctionNotation/ReturnTypeDeclarationFixer.php',
    'PhpCsFixer\\Fixer\\FunctionNotation\\SingleLineThrowFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/FunctionNotation/SingleLineThrowFixer.php',
    'PhpCsFixer\\Fixer\\FunctionNotation\\StaticLambdaFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/FunctionNotation/StaticLambdaFixer.php',
    'PhpCsFixer\\Fixer\\FunctionNotation\\UseArrowFunctionsFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/FunctionNotation/UseArrowFunctionsFixer.php',
    'PhpCsFixer\\Fixer\\FunctionNotation\\VoidReturnFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/FunctionNotation/VoidReturnFixer.php',
    'PhpCsFixer\\Fixer\\Import\\FullyQualifiedStrictTypesFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Import/FullyQualifiedStrictTypesFixer.php',
    'PhpCsFixer\\Fixer\\Import\\GlobalNamespaceImportFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Import/GlobalNamespaceImportFixer.php',
    'PhpCsFixer\\Fixer\\Import\\GroupImportFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Import/GroupImportFixer.php',
    'PhpCsFixer\\Fixer\\Import\\NoLeadingImportSlashFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Import/NoLeadingImportSlashFixer.php',
    'PhpCsFixer\\Fixer\\Import\\NoUnneededImportAliasFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Import/NoUnneededImportAliasFixer.php',
    'PhpCsFixer\\Fixer\\Import\\NoUnusedImportsFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Import/NoUnusedImportsFixer.php',
    'PhpCsFixer\\Fixer\\Import\\OrderedImportsFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Import/OrderedImportsFixer.php',
    'PhpCsFixer\\Fixer\\Import\\SingleImportPerStatementFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Import/SingleImportPerStatementFixer.php',
    'PhpCsFixer\\Fixer\\Import\\SingleLineAfterImportsFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Import/SingleLineAfterImportsFixer.php',
    'PhpCsFixer\\Fixer\\Indentation' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Indentation.php',
    'PhpCsFixer\\Fixer\\InternalFixerInterface' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/InternalFixerInterface.php',
    'PhpCsFixer\\Fixer\\LanguageConstruct\\ClassKeywordFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/LanguageConstruct/ClassKeywordFixer.php',
    'PhpCsFixer\\Fixer\\LanguageConstruct\\ClassKeywordRemoveFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/LanguageConstruct/ClassKeywordRemoveFixer.php',
    'PhpCsFixer\\Fixer\\LanguageConstruct\\CombineConsecutiveIssetsFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/LanguageConstruct/CombineConsecutiveIssetsFixer.php',
    'PhpCsFixer\\Fixer\\LanguageConstruct\\CombineConsecutiveUnsetsFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/LanguageConstruct/CombineConsecutiveUnsetsFixer.php',
    'PhpCsFixer\\Fixer\\LanguageConstruct\\DeclareEqualNormalizeFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/LanguageConstruct/DeclareEqualNormalizeFixer.php',
    'PhpCsFixer\\Fixer\\LanguageConstruct\\DeclareParenthesesFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/LanguageConstruct/DeclareParenthesesFixer.php',
    'PhpCsFixer\\Fixer\\LanguageConstruct\\DirConstantFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/LanguageConstruct/DirConstantFixer.php',
    'PhpCsFixer\\Fixer\\LanguageConstruct\\ErrorSuppressionFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/LanguageConstruct/ErrorSuppressionFixer.php',
    'PhpCsFixer\\Fixer\\LanguageConstruct\\ExplicitIndirectVariableFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/LanguageConstruct/ExplicitIndirectVariableFixer.php',
    'PhpCsFixer\\Fixer\\LanguageConstruct\\FunctionToConstantFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/LanguageConstruct/FunctionToConstantFixer.php',
    'PhpCsFixer\\Fixer\\LanguageConstruct\\GetClassToClassKeywordFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/LanguageConstruct/GetClassToClassKeywordFixer.php',
    'PhpCsFixer\\Fixer\\LanguageConstruct\\IsNullFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/LanguageConstruct/IsNullFixer.php',
    'PhpCsFixer\\Fixer\\LanguageConstruct\\NoUnsetOnPropertyFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/LanguageConstruct/NoUnsetOnPropertyFixer.php',
    'PhpCsFixer\\Fixer\\LanguageConstruct\\NullableTypeDeclarationFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/LanguageConstruct/NullableTypeDeclarationFixer.php',
    'PhpCsFixer\\Fixer\\LanguageConstruct\\SingleSpaceAfterConstructFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/LanguageConstruct/SingleSpaceAfterConstructFixer.php',
    'PhpCsFixer\\Fixer\\LanguageConstruct\\SingleSpaceAroundConstructFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/LanguageConstruct/SingleSpaceAroundConstructFixer.php',
    'PhpCsFixer\\Fixer\\ListNotation\\ListSyntaxFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ListNotation/ListSyntaxFixer.php',
    'PhpCsFixer\\Fixer\\NamespaceNotation\\BlankLineAfterNamespaceFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/NamespaceNotation/BlankLineAfterNamespaceFixer.php',
    'PhpCsFixer\\Fixer\\NamespaceNotation\\BlankLinesBeforeNamespaceFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/NamespaceNotation/BlankLinesBeforeNamespaceFixer.php',
    'PhpCsFixer\\Fixer\\NamespaceNotation\\CleanNamespaceFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/NamespaceNotation/CleanNamespaceFixer.php',
    'PhpCsFixer\\Fixer\\NamespaceNotation\\NoBlankLinesBeforeNamespaceFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/NamespaceNotation/NoBlankLinesBeforeNamespaceFixer.php',
    'PhpCsFixer\\Fixer\\NamespaceNotation\\NoLeadingNamespaceWhitespaceFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/NamespaceNotation/NoLeadingNamespaceWhitespaceFixer.php',
    'PhpCsFixer\\Fixer\\NamespaceNotation\\SingleBlankLineBeforeNamespaceFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/NamespaceNotation/SingleBlankLineBeforeNamespaceFixer.php',
    'PhpCsFixer\\Fixer\\Naming\\NoHomoglyphNamesFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Naming/NoHomoglyphNamesFixer.php',
    'PhpCsFixer\\Fixer\\Operator\\AssignNullCoalescingToCoalesceEqualFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Operator/AssignNullCoalescingToCoalesceEqualFixer.php',
    'PhpCsFixer\\Fixer\\Operator\\BinaryOperatorSpacesFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Operator/BinaryOperatorSpacesFixer.php',
    'PhpCsFixer\\Fixer\\Operator\\ConcatSpaceFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Operator/ConcatSpaceFixer.php',
    'PhpCsFixer\\Fixer\\Operator\\IncrementStyleFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Operator/IncrementStyleFixer.php',
    'PhpCsFixer\\Fixer\\Operator\\LogicalOperatorsFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Operator/LogicalOperatorsFixer.php',
    'PhpCsFixer\\Fixer\\Operator\\LongToShorthandOperatorFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Operator/LongToShorthandOperatorFixer.php',
    'PhpCsFixer\\Fixer\\Operator\\NewWithBracesFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Operator/NewWithBracesFixer.php',
    'PhpCsFixer\\Fixer\\Operator\\NewWithParenthesesFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Operator/NewWithParenthesesFixer.php',
    'PhpCsFixer\\Fixer\\Operator\\NoSpaceAroundDoubleColonFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Operator/NoSpaceAroundDoubleColonFixer.php',
    'PhpCsFixer\\Fixer\\Operator\\NoUselessConcatOperatorFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Operator/NoUselessConcatOperatorFixer.php',
    'PhpCsFixer\\Fixer\\Operator\\NoUselessNullsafeOperatorFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Operator/NoUselessNullsafeOperatorFixer.php',
    'PhpCsFixer\\Fixer\\Operator\\NotOperatorWithSpaceFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Operator/NotOperatorWithSpaceFixer.php',
    'PhpCsFixer\\Fixer\\Operator\\NotOperatorWithSuccessorSpaceFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Operator/NotOperatorWithSuccessorSpaceFixer.php',
    'PhpCsFixer\\Fixer\\Operator\\ObjectOperatorWithoutWhitespaceFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Operator/ObjectOperatorWithoutWhitespaceFixer.php',
    'PhpCsFixer\\Fixer\\Operator\\OperatorLinebreakFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Operator/OperatorLinebreakFixer.php',
    'PhpCsFixer\\Fixer\\Operator\\StandardizeIncrementFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Operator/StandardizeIncrementFixer.php',
    'PhpCsFixer\\Fixer\\Operator\\StandardizeNotEqualsFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Operator/StandardizeNotEqualsFixer.php',
    'PhpCsFixer\\Fixer\\Operator\\TernaryOperatorSpacesFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Operator/TernaryOperatorSpacesFixer.php',
    'PhpCsFixer\\Fixer\\Operator\\TernaryToElvisOperatorFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Operator/TernaryToElvisOperatorFixer.php',
    'PhpCsFixer\\Fixer\\Operator\\TernaryToNullCoalescingFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Operator/TernaryToNullCoalescingFixer.php',
    'PhpCsFixer\\Fixer\\Operator\\UnaryOperatorSpacesFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Operator/UnaryOperatorSpacesFixer.php',
    'PhpCsFixer\\Fixer\\PhpTag\\BlankLineAfterOpeningTagFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/PhpTag/BlankLineAfterOpeningTagFixer.php',
    'PhpCsFixer\\Fixer\\PhpTag\\EchoTagSyntaxFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/PhpTag/EchoTagSyntaxFixer.php',
    'PhpCsFixer\\Fixer\\PhpTag\\FullOpeningTagFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/PhpTag/FullOpeningTagFixer.php',
    'PhpCsFixer\\Fixer\\PhpTag\\LinebreakAfterOpeningTagFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/PhpTag/LinebreakAfterOpeningTagFixer.php',
    'PhpCsFixer\\Fixer\\PhpTag\\NoClosingTagFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/PhpTag/NoClosingTagFixer.php',
    'PhpCsFixer\\Fixer\\PhpUnit\\PhpUnitAssertNewNamesFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/PhpUnit/PhpUnitAssertNewNamesFixer.php',
    'PhpCsFixer\\Fixer\\PhpUnit\\PhpUnitAttributesFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/PhpUnit/PhpUnitAttributesFixer.php',
    'PhpCsFixer\\Fixer\\PhpUnit\\PhpUnitConstructFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/PhpUnit/PhpUnitConstructFixer.php',
    'PhpCsFixer\\Fixer\\PhpUnit\\PhpUnitDataProviderMethodOrderFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/PhpUnit/PhpUnitDataProviderMethodOrderFixer.php',
    'PhpCsFixer\\Fixer\\PhpUnit\\PhpUnitDataProviderNameFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/PhpUnit/PhpUnitDataProviderNameFixer.php',
    'PhpCsFixer\\Fixer\\PhpUnit\\PhpUnitDataProviderReturnTypeFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/PhpUnit/PhpUnitDataProviderReturnTypeFixer.php',
    'PhpCsFixer\\Fixer\\PhpUnit\\PhpUnitDataProviderStaticFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/PhpUnit/PhpUnitDataProviderStaticFixer.php',
    'PhpCsFixer\\Fixer\\PhpUnit\\PhpUnitDedicateAssertFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/PhpUnit/PhpUnitDedicateAssertFixer.php',
    'PhpCsFixer\\Fixer\\PhpUnit\\PhpUnitDedicateAssertInternalTypeFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/PhpUnit/PhpUnitDedicateAssertInternalTypeFixer.php',
    'PhpCsFixer\\Fixer\\PhpUnit\\PhpUnitExpectationFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/PhpUnit/PhpUnitExpectationFixer.php',
    'PhpCsFixer\\Fixer\\PhpUnit\\PhpUnitFqcnAnnotationFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/PhpUnit/PhpUnitFqcnAnnotationFixer.php',
    'PhpCsFixer\\Fixer\\PhpUnit\\PhpUnitInternalClassFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/PhpUnit/PhpUnitInternalClassFixer.php',
    'PhpCsFixer\\Fixer\\PhpUnit\\PhpUnitMethodCasingFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/PhpUnit/PhpUnitMethodCasingFixer.php',
    'PhpCsFixer\\Fixer\\PhpUnit\\PhpUnitMockFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/PhpUnit/PhpUnitMockFixer.php',
    'PhpCsFixer\\Fixer\\PhpUnit\\PhpUnitMockShortWillReturnFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/PhpUnit/PhpUnitMockShortWillReturnFixer.php',
    'PhpCsFixer\\Fixer\\PhpUnit\\PhpUnitNamespacedFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/PhpUnit/PhpUnitNamespacedFixer.php',
    'PhpCsFixer\\Fixer\\PhpUnit\\PhpUnitNoExpectationAnnotationFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/PhpUnit/PhpUnitNoExpectationAnnotationFixer.php',
    'PhpCsFixer\\Fixer\\PhpUnit\\PhpUnitSetUpTearDownVisibilityFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/PhpUnit/PhpUnitSetUpTearDownVisibilityFixer.php',
    'PhpCsFixer\\Fixer\\PhpUnit\\PhpUnitSizeClassFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/PhpUnit/PhpUnitSizeClassFixer.php',
    'PhpCsFixer\\Fixer\\PhpUnit\\PhpUnitStrictFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/PhpUnit/PhpUnitStrictFixer.php',
    'PhpCsFixer\\Fixer\\PhpUnit\\PhpUnitTargetVersion' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/PhpUnit/PhpUnitTargetVersion.php',
    'PhpCsFixer\\Fixer\\PhpUnit\\PhpUnitTestAnnotationFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/PhpUnit/PhpUnitTestAnnotationFixer.php',
    'PhpCsFixer\\Fixer\\PhpUnit\\PhpUnitTestCaseStaticMethodCallsFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/PhpUnit/PhpUnitTestCaseStaticMethodCallsFixer.php',
    'PhpCsFixer\\Fixer\\PhpUnit\\PhpUnitTestClassRequiresCoversFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/PhpUnit/PhpUnitTestClassRequiresCoversFixer.php',
    'PhpCsFixer\\Fixer\\Phpdoc\\AlignMultilineCommentFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Phpdoc/AlignMultilineCommentFixer.php',
    'PhpCsFixer\\Fixer\\Phpdoc\\GeneralPhpdocAnnotationRemoveFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Phpdoc/GeneralPhpdocAnnotationRemoveFixer.php',
    'PhpCsFixer\\Fixer\\Phpdoc\\GeneralPhpdocTagRenameFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Phpdoc/GeneralPhpdocTagRenameFixer.php',
    'PhpCsFixer\\Fixer\\Phpdoc\\NoBlankLinesAfterPhpdocFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Phpdoc/NoBlankLinesAfterPhpdocFixer.php',
    'PhpCsFixer\\Fixer\\Phpdoc\\NoEmptyPhpdocFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Phpdoc/NoEmptyPhpdocFixer.php',
    'PhpCsFixer\\Fixer\\Phpdoc\\NoSuperfluousPhpdocTagsFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Phpdoc/NoSuperfluousPhpdocTagsFixer.php',
    'PhpCsFixer\\Fixer\\Phpdoc\\PhpdocAddMissingParamAnnotationFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Phpdoc/PhpdocAddMissingParamAnnotationFixer.php',
    'PhpCsFixer\\Fixer\\Phpdoc\\PhpdocAlignFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Phpdoc/PhpdocAlignFixer.php',
    'PhpCsFixer\\Fixer\\Phpdoc\\PhpdocAnnotationWithoutDotFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Phpdoc/PhpdocAnnotationWithoutDotFixer.php',
    'PhpCsFixer\\Fixer\\Phpdoc\\PhpdocArrayTypeFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Phpdoc/PhpdocArrayTypeFixer.php',
    'PhpCsFixer\\Fixer\\Phpdoc\\PhpdocIndentFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Phpdoc/PhpdocIndentFixer.php',
    'PhpCsFixer\\Fixer\\Phpdoc\\PhpdocInlineTagNormalizerFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Phpdoc/PhpdocInlineTagNormalizerFixer.php',
    'PhpCsFixer\\Fixer\\Phpdoc\\PhpdocLineSpanFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Phpdoc/PhpdocLineSpanFixer.php',
    'PhpCsFixer\\Fixer\\Phpdoc\\PhpdocListTypeFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Phpdoc/PhpdocListTypeFixer.php',
    'PhpCsFixer\\Fixer\\Phpdoc\\PhpdocNoAccessFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Phpdoc/PhpdocNoAccessFixer.php',
    'PhpCsFixer\\Fixer\\Phpdoc\\PhpdocNoAliasTagFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Phpdoc/PhpdocNoAliasTagFixer.php',
    'PhpCsFixer\\Fixer\\Phpdoc\\PhpdocNoEmptyReturnFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Phpdoc/PhpdocNoEmptyReturnFixer.php',
    'PhpCsFixer\\Fixer\\Phpdoc\\PhpdocNoPackageFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Phpdoc/PhpdocNoPackageFixer.php',
    'PhpCsFixer\\Fixer\\Phpdoc\\PhpdocNoUselessInheritdocFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Phpdoc/PhpdocNoUselessInheritdocFixer.php',
    'PhpCsFixer\\Fixer\\Phpdoc\\PhpdocOrderByValueFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Phpdoc/PhpdocOrderByValueFixer.php',
    'PhpCsFixer\\Fixer\\Phpdoc\\PhpdocOrderFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Phpdoc/PhpdocOrderFixer.php',
    'PhpCsFixer\\Fixer\\Phpdoc\\PhpdocParamOrderFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Phpdoc/PhpdocParamOrderFixer.php',
    'PhpCsFixer\\Fixer\\Phpdoc\\PhpdocReturnSelfReferenceFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Phpdoc/PhpdocReturnSelfReferenceFixer.php',
    'PhpCsFixer\\Fixer\\Phpdoc\\PhpdocScalarFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Phpdoc/PhpdocScalarFixer.php',
    'PhpCsFixer\\Fixer\\Phpdoc\\PhpdocSeparationFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Phpdoc/PhpdocSeparationFixer.php',
    'PhpCsFixer\\Fixer\\Phpdoc\\PhpdocSingleLineVarSpacingFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Phpdoc/PhpdocSingleLineVarSpacingFixer.php',
    'PhpCsFixer\\Fixer\\Phpdoc\\PhpdocSummaryFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Phpdoc/PhpdocSummaryFixer.php',
    'PhpCsFixer\\Fixer\\Phpdoc\\PhpdocTagCasingFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Phpdoc/PhpdocTagCasingFixer.php',
    'PhpCsFixer\\Fixer\\Phpdoc\\PhpdocTagTypeFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Phpdoc/PhpdocTagTypeFixer.php',
    'PhpCsFixer\\Fixer\\Phpdoc\\PhpdocToCommentFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Phpdoc/PhpdocToCommentFixer.php',
    'PhpCsFixer\\Fixer\\Phpdoc\\PhpdocTrimConsecutiveBlankLineSeparationFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Phpdoc/PhpdocTrimConsecutiveBlankLineSeparationFixer.php',
    'PhpCsFixer\\Fixer\\Phpdoc\\PhpdocTrimFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Phpdoc/PhpdocTrimFixer.php',
    'PhpCsFixer\\Fixer\\Phpdoc\\PhpdocTypesFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Phpdoc/PhpdocTypesFixer.php',
    'PhpCsFixer\\Fixer\\Phpdoc\\PhpdocTypesOrderFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Phpdoc/PhpdocTypesOrderFixer.php',
    'PhpCsFixer\\Fixer\\Phpdoc\\PhpdocVarAnnotationCorrectOrderFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Phpdoc/PhpdocVarAnnotationCorrectOrderFixer.php',
    'PhpCsFixer\\Fixer\\Phpdoc\\PhpdocVarWithoutNameFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Phpdoc/PhpdocVarWithoutNameFixer.php',
    'PhpCsFixer\\Fixer\\ReturnNotation\\NoUselessReturnFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ReturnNotation/NoUselessReturnFixer.php',
    'PhpCsFixer\\Fixer\\ReturnNotation\\ReturnAssignmentFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ReturnNotation/ReturnAssignmentFixer.php',
    'PhpCsFixer\\Fixer\\ReturnNotation\\SimplifiedNullReturnFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/ReturnNotation/SimplifiedNullReturnFixer.php',
    'PhpCsFixer\\Fixer\\Semicolon\\MultilineWhitespaceBeforeSemicolonsFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Semicolon/MultilineWhitespaceBeforeSemicolonsFixer.php',
    'PhpCsFixer\\Fixer\\Semicolon\\NoEmptyStatementFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Semicolon/NoEmptyStatementFixer.php',
    'PhpCsFixer\\Fixer\\Semicolon\\NoSinglelineWhitespaceBeforeSemicolonsFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Semicolon/NoSinglelineWhitespaceBeforeSemicolonsFixer.php',
    'PhpCsFixer\\Fixer\\Semicolon\\SemicolonAfterInstructionFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Semicolon/SemicolonAfterInstructionFixer.php',
    'PhpCsFixer\\Fixer\\Semicolon\\SpaceAfterSemicolonFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Semicolon/SpaceAfterSemicolonFixer.php',
    'PhpCsFixer\\Fixer\\Strict\\DeclareStrictTypesFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Strict/DeclareStrictTypesFixer.php',
    'PhpCsFixer\\Fixer\\Strict\\StrictComparisonFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Strict/StrictComparisonFixer.php',
    'PhpCsFixer\\Fixer\\Strict\\StrictParamFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Strict/StrictParamFixer.php',
    'PhpCsFixer\\Fixer\\StringNotation\\EscapeImplicitBackslashesFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/StringNotation/EscapeImplicitBackslashesFixer.php',
    'PhpCsFixer\\Fixer\\StringNotation\\ExplicitStringVariableFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/StringNotation/ExplicitStringVariableFixer.php',
    'PhpCsFixer\\Fixer\\StringNotation\\HeredocClosingMarkerFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/StringNotation/HeredocClosingMarkerFixer.php',
    'PhpCsFixer\\Fixer\\StringNotation\\HeredocToNowdocFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/StringNotation/HeredocToNowdocFixer.php',
    'PhpCsFixer\\Fixer\\StringNotation\\MultilineStringToHeredocFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/StringNotation/MultilineStringToHeredocFixer.php',
    'PhpCsFixer\\Fixer\\StringNotation\\NoBinaryStringFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/StringNotation/NoBinaryStringFixer.php',
    'PhpCsFixer\\Fixer\\StringNotation\\NoTrailingWhitespaceInStringFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/StringNotation/NoTrailingWhitespaceInStringFixer.php',
    'PhpCsFixer\\Fixer\\StringNotation\\SimpleToComplexStringVariableFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/StringNotation/SimpleToComplexStringVariableFixer.php',
    'PhpCsFixer\\Fixer\\StringNotation\\SingleQuoteFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/StringNotation/SingleQuoteFixer.php',
    'PhpCsFixer\\Fixer\\StringNotation\\StringImplicitBackslashesFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/StringNotation/StringImplicitBackslashesFixer.php',
    'PhpCsFixer\\Fixer\\StringNotation\\StringLengthToEmptyFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/StringNotation/StringLengthToEmptyFixer.php',
    'PhpCsFixer\\Fixer\\StringNotation\\StringLineEndingFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/StringNotation/StringLineEndingFixer.php',
    'PhpCsFixer\\Fixer\\Whitespace\\ArrayIndentationFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Whitespace/ArrayIndentationFixer.php',
    'PhpCsFixer\\Fixer\\Whitespace\\BlankLineBeforeStatementFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Whitespace/BlankLineBeforeStatementFixer.php',
    'PhpCsFixer\\Fixer\\Whitespace\\BlankLineBetweenImportGroupsFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Whitespace/BlankLineBetweenImportGroupsFixer.php',
    'PhpCsFixer\\Fixer\\Whitespace\\CompactNullableTypeDeclarationFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Whitespace/CompactNullableTypeDeclarationFixer.php',
    'PhpCsFixer\\Fixer\\Whitespace\\CompactNullableTypehintFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Whitespace/CompactNullableTypehintFixer.php',
    'PhpCsFixer\\Fixer\\Whitespace\\HeredocIndentationFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Whitespace/HeredocIndentationFixer.php',
    'PhpCsFixer\\Fixer\\Whitespace\\IndentationTypeFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Whitespace/IndentationTypeFixer.php',
    'PhpCsFixer\\Fixer\\Whitespace\\LineEndingFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Whitespace/LineEndingFixer.php',
    'PhpCsFixer\\Fixer\\Whitespace\\MethodChainingIndentationFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Whitespace/MethodChainingIndentationFixer.php',
    'PhpCsFixer\\Fixer\\Whitespace\\NoExtraBlankLinesFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Whitespace/NoExtraBlankLinesFixer.php',
    'PhpCsFixer\\Fixer\\Whitespace\\NoSpacesAroundOffsetFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Whitespace/NoSpacesAroundOffsetFixer.php',
    'PhpCsFixer\\Fixer\\Whitespace\\NoSpacesInsideParenthesisFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Whitespace/NoSpacesInsideParenthesisFixer.php',
    'PhpCsFixer\\Fixer\\Whitespace\\NoTrailingWhitespaceFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Whitespace/NoTrailingWhitespaceFixer.php',
    'PhpCsFixer\\Fixer\\Whitespace\\NoWhitespaceInBlankLineFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Whitespace/NoWhitespaceInBlankLineFixer.php',
    'PhpCsFixer\\Fixer\\Whitespace\\SingleBlankLineAtEofFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Whitespace/SingleBlankLineAtEofFixer.php',
    'PhpCsFixer\\Fixer\\Whitespace\\SpacesInsideParenthesesFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Whitespace/SpacesInsideParenthesesFixer.php',
    'PhpCsFixer\\Fixer\\Whitespace\\StatementIndentationFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Whitespace/StatementIndentationFixer.php',
    'PhpCsFixer\\Fixer\\Whitespace\\TypeDeclarationSpacesFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Whitespace/TypeDeclarationSpacesFixer.php',
    'PhpCsFixer\\Fixer\\Whitespace\\TypesSpacesFixer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/Whitespace/TypesSpacesFixer.php',
    'PhpCsFixer\\Fixer\\WhitespacesAwareFixerInterface' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Fixer/WhitespacesAwareFixerInterface.php',
    'PhpCsFixer\\Hasher' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Hasher.php',
    'PhpCsFixer\\Indicator\\PhpUnitTestCaseIndicator' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Indicator/PhpUnitTestCaseIndicator.php',
    'PhpCsFixer\\Linter\\CachingLinter' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Linter/CachingLinter.php',
    'PhpCsFixer\\Linter\\Linter' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Linter/Linter.php',
    'PhpCsFixer\\Linter\\LinterInterface' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Linter/LinterInterface.php',
    'PhpCsFixer\\Linter\\LintingException' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Linter/LintingException.php',
    'PhpCsFixer\\Linter\\LintingResultInterface' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Linter/LintingResultInterface.php',
    'PhpCsFixer\\Linter\\ProcessLinter' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Linter/ProcessLinter.php',
    'PhpCsFixer\\Linter\\ProcessLinterProcessBuilder' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Linter/ProcessLinterProcessBuilder.php',
    'PhpCsFixer\\Linter\\ProcessLintingResult' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Linter/ProcessLintingResult.php',
    'PhpCsFixer\\Linter\\TokenizerLinter' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Linter/TokenizerLinter.php',
    'PhpCsFixer\\Linter\\TokenizerLintingResult' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Linter/TokenizerLintingResult.php',
    'PhpCsFixer\\Linter\\UnavailableLinterException' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Linter/UnavailableLinterException.php',
    'PhpCsFixer\\ParallelAwareConfigInterface' => $vendorDir . '/friendsofphp/php-cs-fixer/src/ParallelAwareConfigInterface.php',
    'PhpCsFixer\\PharChecker' => $vendorDir . '/friendsofphp/php-cs-fixer/src/PharChecker.php',
    'PhpCsFixer\\PharCheckerInterface' => $vendorDir . '/friendsofphp/php-cs-fixer/src/PharCheckerInterface.php',
    'PhpCsFixer\\Preg' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Preg.php',
    'PhpCsFixer\\PregException' => $vendorDir . '/friendsofphp/php-cs-fixer/src/PregException.php',
    'PhpCsFixer\\RuleSet\\AbstractMigrationSetDescription' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/AbstractMigrationSetDescription.php',
    'PhpCsFixer\\RuleSet\\AbstractRuleSetDescription' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/AbstractRuleSetDescription.php',
    'PhpCsFixer\\RuleSet\\DeprecatedRuleSetDescriptionInterface' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/DeprecatedRuleSetDescriptionInterface.php',
    'PhpCsFixer\\RuleSet\\RuleSet' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/RuleSet.php',
    'PhpCsFixer\\RuleSet\\RuleSetDescriptionInterface' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/RuleSetDescriptionInterface.php',
    'PhpCsFixer\\RuleSet\\RuleSetInterface' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/RuleSetInterface.php',
    'PhpCsFixer\\RuleSet\\RuleSets' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/RuleSets.php',
    'PhpCsFixer\\RuleSet\\Sets\\DoctrineAnnotationSet' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/Sets/DoctrineAnnotationSet.php',
    'PhpCsFixer\\RuleSet\\Sets\\PERCS1x0RiskySet' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/Sets/PERCS1x0RiskySet.php',
    'PhpCsFixer\\RuleSet\\Sets\\PERCS1x0Set' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/Sets/PERCS1x0Set.php',
    'PhpCsFixer\\RuleSet\\Sets\\PERCS2x0RiskySet' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/Sets/PERCS2x0RiskySet.php',
    'PhpCsFixer\\RuleSet\\Sets\\PERCS2x0Set' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/Sets/PERCS2x0Set.php',
    'PhpCsFixer\\RuleSet\\Sets\\PERCSRiskySet' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/Sets/PERCSRiskySet.php',
    'PhpCsFixer\\RuleSet\\Sets\\PERCSSet' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/Sets/PERCSSet.php',
    'PhpCsFixer\\RuleSet\\Sets\\PERRiskySet' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/Sets/PERRiskySet.php',
    'PhpCsFixer\\RuleSet\\Sets\\PERSet' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/Sets/PERSet.php',
    'PhpCsFixer\\RuleSet\\Sets\\PHP54MigrationSet' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/Sets/PHP54MigrationSet.php',
    'PhpCsFixer\\RuleSet\\Sets\\PHP56MigrationRiskySet' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/Sets/PHP56MigrationRiskySet.php',
    'PhpCsFixer\\RuleSet\\Sets\\PHP70MigrationRiskySet' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/Sets/PHP70MigrationRiskySet.php',
    'PhpCsFixer\\RuleSet\\Sets\\PHP70MigrationSet' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/Sets/PHP70MigrationSet.php',
    'PhpCsFixer\\RuleSet\\Sets\\PHP71MigrationRiskySet' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/Sets/PHP71MigrationRiskySet.php',
    'PhpCsFixer\\RuleSet\\Sets\\PHP71MigrationSet' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/Sets/PHP71MigrationSet.php',
    'PhpCsFixer\\RuleSet\\Sets\\PHP73MigrationSet' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/Sets/PHP73MigrationSet.php',
    'PhpCsFixer\\RuleSet\\Sets\\PHP74MigrationRiskySet' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/Sets/PHP74MigrationRiskySet.php',
    'PhpCsFixer\\RuleSet\\Sets\\PHP74MigrationSet' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/Sets/PHP74MigrationSet.php',
    'PhpCsFixer\\RuleSet\\Sets\\PHP80MigrationRiskySet' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/Sets/PHP80MigrationRiskySet.php',
    'PhpCsFixer\\RuleSet\\Sets\\PHP80MigrationSet' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/Sets/PHP80MigrationSet.php',
    'PhpCsFixer\\RuleSet\\Sets\\PHP81MigrationSet' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/Sets/PHP81MigrationSet.php',
    'PhpCsFixer\\RuleSet\\Sets\\PHP82MigrationRiskySet' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/Sets/PHP82MigrationRiskySet.php',
    'PhpCsFixer\\RuleSet\\Sets\\PHP82MigrationSet' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/Sets/PHP82MigrationSet.php',
    'PhpCsFixer\\RuleSet\\Sets\\PHP83MigrationSet' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/Sets/PHP83MigrationSet.php',
    'PhpCsFixer\\RuleSet\\Sets\\PHP84MigrationSet' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/Sets/PHP84MigrationSet.php',
    'PhpCsFixer\\RuleSet\\Sets\\PHPUnit100MigrationRiskySet' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/Sets/PHPUnit100MigrationRiskySet.php',
    'PhpCsFixer\\RuleSet\\Sets\\PHPUnit30MigrationRiskySet' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/Sets/PHPUnit30MigrationRiskySet.php',
    'PhpCsFixer\\RuleSet\\Sets\\PHPUnit32MigrationRiskySet' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/Sets/PHPUnit32MigrationRiskySet.php',
    'PhpCsFixer\\RuleSet\\Sets\\PHPUnit35MigrationRiskySet' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/Sets/PHPUnit35MigrationRiskySet.php',
    'PhpCsFixer\\RuleSet\\Sets\\PHPUnit43MigrationRiskySet' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/Sets/PHPUnit43MigrationRiskySet.php',
    'PhpCsFixer\\RuleSet\\Sets\\PHPUnit48MigrationRiskySet' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/Sets/PHPUnit48MigrationRiskySet.php',
    'PhpCsFixer\\RuleSet\\Sets\\PHPUnit50MigrationRiskySet' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/Sets/PHPUnit50MigrationRiskySet.php',
    'PhpCsFixer\\RuleSet\\Sets\\PHPUnit52MigrationRiskySet' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/Sets/PHPUnit52MigrationRiskySet.php',
    'PhpCsFixer\\RuleSet\\Sets\\PHPUnit54MigrationRiskySet' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/Sets/PHPUnit54MigrationRiskySet.php',
    'PhpCsFixer\\RuleSet\\Sets\\PHPUnit55MigrationRiskySet' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/Sets/PHPUnit55MigrationRiskySet.php',
    'PhpCsFixer\\RuleSet\\Sets\\PHPUnit56MigrationRiskySet' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/Sets/PHPUnit56MigrationRiskySet.php',
    'PhpCsFixer\\RuleSet\\Sets\\PHPUnit57MigrationRiskySet' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/Sets/PHPUnit57MigrationRiskySet.php',
    'PhpCsFixer\\RuleSet\\Sets\\PHPUnit60MigrationRiskySet' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/Sets/PHPUnit60MigrationRiskySet.php',
    'PhpCsFixer\\RuleSet\\Sets\\PHPUnit75MigrationRiskySet' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/Sets/PHPUnit75MigrationRiskySet.php',
    'PhpCsFixer\\RuleSet\\Sets\\PHPUnit84MigrationRiskySet' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/Sets/PHPUnit84MigrationRiskySet.php',
    'PhpCsFixer\\RuleSet\\Sets\\PHPUnit91MigrationRiskySet' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/Sets/PHPUnit91MigrationRiskySet.php',
    'PhpCsFixer\\RuleSet\\Sets\\PSR12RiskySet' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/Sets/PSR12RiskySet.php',
    'PhpCsFixer\\RuleSet\\Sets\\PSR12Set' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/Sets/PSR12Set.php',
    'PhpCsFixer\\RuleSet\\Sets\\PSR1Set' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/Sets/PSR1Set.php',
    'PhpCsFixer\\RuleSet\\Sets\\PSR2Set' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/Sets/PSR2Set.php',
    'PhpCsFixer\\RuleSet\\Sets\\PhpCsFixerRiskySet' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/Sets/PhpCsFixerRiskySet.php',
    'PhpCsFixer\\RuleSet\\Sets\\PhpCsFixerSet' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/Sets/PhpCsFixerSet.php',
    'PhpCsFixer\\RuleSet\\Sets\\SymfonyRiskySet' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/Sets/SymfonyRiskySet.php',
    'PhpCsFixer\\RuleSet\\Sets\\SymfonySet' => $vendorDir . '/friendsofphp/php-cs-fixer/src/RuleSet/Sets/SymfonySet.php',
    'PhpCsFixer\\Runner\\Event\\AnalysisStarted' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Runner/Event/AnalysisStarted.php',
    'PhpCsFixer\\Runner\\Event\\FileProcessed' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Runner/Event/FileProcessed.php',
    'PhpCsFixer\\Runner\\FileCachingLintingFileIterator' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Runner/FileCachingLintingFileIterator.php',
    'PhpCsFixer\\Runner\\FileFilterIterator' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Runner/FileFilterIterator.php',
    'PhpCsFixer\\Runner\\LintingFileIterator' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Runner/LintingFileIterator.php',
    'PhpCsFixer\\Runner\\LintingResultAwareFileIteratorInterface' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Runner/LintingResultAwareFileIteratorInterface.php',
    'PhpCsFixer\\Runner\\Parallel\\ParallelAction' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Runner/Parallel/ParallelAction.php',
    'PhpCsFixer\\Runner\\Parallel\\ParallelConfig' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Runner/Parallel/ParallelConfig.php',
    'PhpCsFixer\\Runner\\Parallel\\ParallelConfigFactory' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Runner/Parallel/ParallelConfigFactory.php',
    'PhpCsFixer\\Runner\\Parallel\\ParallelisationException' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Runner/Parallel/ParallelisationException.php',
    'PhpCsFixer\\Runner\\Parallel\\Process' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Runner/Parallel/Process.php',
    'PhpCsFixer\\Runner\\Parallel\\ProcessFactory' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Runner/Parallel/ProcessFactory.php',
    'PhpCsFixer\\Runner\\Parallel\\ProcessIdentifier' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Runner/Parallel/ProcessIdentifier.php',
    'PhpCsFixer\\Runner\\Parallel\\ProcessPool' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Runner/Parallel/ProcessPool.php',
    'PhpCsFixer\\Runner\\Parallel\\WorkerException' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Runner/Parallel/WorkerException.php',
    'PhpCsFixer\\Runner\\Runner' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Runner/Runner.php',
    'PhpCsFixer\\Runner\\RunnerConfig' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Runner/RunnerConfig.php',
    'PhpCsFixer\\StdinFileInfo' => $vendorDir . '/friendsofphp/php-cs-fixer/src/StdinFileInfo.php',
    'PhpCsFixer\\Tokenizer\\AbstractTransformer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/AbstractTransformer.php',
    'PhpCsFixer\\Tokenizer\\AbstractTypeTransformer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/AbstractTypeTransformer.php',
    'PhpCsFixer\\Tokenizer\\Analyzer\\AlternativeSyntaxAnalyzer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/Analyzer/AlternativeSyntaxAnalyzer.php',
    'PhpCsFixer\\Tokenizer\\Analyzer\\Analysis\\AbstractControlCaseStructuresAnalysis' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/Analyzer/Analysis/AbstractControlCaseStructuresAnalysis.php',
    'PhpCsFixer\\Tokenizer\\Analyzer\\Analysis\\ArgumentAnalysis' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/Analyzer/Analysis/ArgumentAnalysis.php',
    'PhpCsFixer\\Tokenizer\\Analyzer\\Analysis\\AttributeAnalysis' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/Analyzer/Analysis/AttributeAnalysis.php',
    'PhpCsFixer\\Tokenizer\\Analyzer\\Analysis\\CaseAnalysis' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/Analyzer/Analysis/CaseAnalysis.php',
    'PhpCsFixer\\Tokenizer\\Analyzer\\Analysis\\DataProviderAnalysis' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/Analyzer/Analysis/DataProviderAnalysis.php',
    'PhpCsFixer\\Tokenizer\\Analyzer\\Analysis\\DefaultAnalysis' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/Analyzer/Analysis/DefaultAnalysis.php',
    'PhpCsFixer\\Tokenizer\\Analyzer\\Analysis\\EnumAnalysis' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/Analyzer/Analysis/EnumAnalysis.php',
    'PhpCsFixer\\Tokenizer\\Analyzer\\Analysis\\MatchAnalysis' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/Analyzer/Analysis/MatchAnalysis.php',
    'PhpCsFixer\\Tokenizer\\Analyzer\\Analysis\\NamespaceAnalysis' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/Analyzer/Analysis/NamespaceAnalysis.php',
    'PhpCsFixer\\Tokenizer\\Analyzer\\Analysis\\NamespaceUseAnalysis' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/Analyzer/Analysis/NamespaceUseAnalysis.php',
    'PhpCsFixer\\Tokenizer\\Analyzer\\Analysis\\StartEndTokenAwareAnalysis' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/Analyzer/Analysis/StartEndTokenAwareAnalysis.php',
    'PhpCsFixer\\Tokenizer\\Analyzer\\Analysis\\SwitchAnalysis' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/Analyzer/Analysis/SwitchAnalysis.php',
    'PhpCsFixer\\Tokenizer\\Analyzer\\Analysis\\TypeAnalysis' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/Analyzer/Analysis/TypeAnalysis.php',
    'PhpCsFixer\\Tokenizer\\Analyzer\\ArgumentsAnalyzer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/Analyzer/ArgumentsAnalyzer.php',
    'PhpCsFixer\\Tokenizer\\Analyzer\\AttributeAnalyzer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/Analyzer/AttributeAnalyzer.php',
    'PhpCsFixer\\Tokenizer\\Analyzer\\BlocksAnalyzer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/Analyzer/BlocksAnalyzer.php',
    'PhpCsFixer\\Tokenizer\\Analyzer\\ClassyAnalyzer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/Analyzer/ClassyAnalyzer.php',
    'PhpCsFixer\\Tokenizer\\Analyzer\\CommentsAnalyzer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/Analyzer/CommentsAnalyzer.php',
    'PhpCsFixer\\Tokenizer\\Analyzer\\ControlCaseStructuresAnalyzer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/Analyzer/ControlCaseStructuresAnalyzer.php',
    'PhpCsFixer\\Tokenizer\\Analyzer\\DataProviderAnalyzer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/Analyzer/DataProviderAnalyzer.php',
    'PhpCsFixer\\Tokenizer\\Analyzer\\FullyQualifiedNameAnalyzer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/Analyzer/FullyQualifiedNameAnalyzer.php',
    'PhpCsFixer\\Tokenizer\\Analyzer\\FunctionsAnalyzer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/Analyzer/FunctionsAnalyzer.php',
    'PhpCsFixer\\Tokenizer\\Analyzer\\GotoLabelAnalyzer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/Analyzer/GotoLabelAnalyzer.php',
    'PhpCsFixer\\Tokenizer\\Analyzer\\NamespaceUsesAnalyzer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/Analyzer/NamespaceUsesAnalyzer.php',
    'PhpCsFixer\\Tokenizer\\Analyzer\\NamespacesAnalyzer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/Analyzer/NamespacesAnalyzer.php',
    'PhpCsFixer\\Tokenizer\\Analyzer\\RangeAnalyzer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/Analyzer/RangeAnalyzer.php',
    'PhpCsFixer\\Tokenizer\\Analyzer\\ReferenceAnalyzer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/Analyzer/ReferenceAnalyzer.php',
    'PhpCsFixer\\Tokenizer\\Analyzer\\SwitchAnalyzer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/Analyzer/SwitchAnalyzer.php',
    'PhpCsFixer\\Tokenizer\\Analyzer\\WhitespacesAnalyzer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/Analyzer/WhitespacesAnalyzer.php',
    'PhpCsFixer\\Tokenizer\\CT' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/CT.php',
    'PhpCsFixer\\Tokenizer\\Processor\\ImportProcessor' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/Processor/ImportProcessor.php',
    'PhpCsFixer\\Tokenizer\\Token' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/Token.php',
    'PhpCsFixer\\Tokenizer\\Tokens' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/Tokens.php',
    'PhpCsFixer\\Tokenizer\\TokensAnalyzer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/TokensAnalyzer.php',
    'PhpCsFixer\\Tokenizer\\TransformerInterface' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/TransformerInterface.php',
    'PhpCsFixer\\Tokenizer\\Transformer\\ArrayTypehintTransformer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/Transformer/ArrayTypehintTransformer.php',
    'PhpCsFixer\\Tokenizer\\Transformer\\AttributeTransformer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/Transformer/AttributeTransformer.php',
    'PhpCsFixer\\Tokenizer\\Transformer\\BraceClassInstantiationTransformer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/Transformer/BraceClassInstantiationTransformer.php',
    'PhpCsFixer\\Tokenizer\\Transformer\\BraceTransformer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/Transformer/BraceTransformer.php',
    'PhpCsFixer\\Tokenizer\\Transformer\\ClassConstantTransformer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/Transformer/ClassConstantTransformer.php',
    'PhpCsFixer\\Tokenizer\\Transformer\\ConstructorPromotionTransformer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/Transformer/ConstructorPromotionTransformer.php',
    'PhpCsFixer\\Tokenizer\\Transformer\\DisjunctiveNormalFormTypeParenthesisTransformer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/Transformer/DisjunctiveNormalFormTypeParenthesisTransformer.php',
    'PhpCsFixer\\Tokenizer\\Transformer\\FirstClassCallableTransformer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/Transformer/FirstClassCallableTransformer.php',
    'PhpCsFixer\\Tokenizer\\Transformer\\ImportTransformer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/Transformer/ImportTransformer.php',
    'PhpCsFixer\\Tokenizer\\Transformer\\NameQualifiedTransformer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/Transformer/NameQualifiedTransformer.php',
    'PhpCsFixer\\Tokenizer\\Transformer\\NamedArgumentTransformer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/Transformer/NamedArgumentTransformer.php',
    'PhpCsFixer\\Tokenizer\\Transformer\\NamespaceOperatorTransformer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/Transformer/NamespaceOperatorTransformer.php',
    'PhpCsFixer\\Tokenizer\\Transformer\\NullableTypeTransformer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/Transformer/NullableTypeTransformer.php',
    'PhpCsFixer\\Tokenizer\\Transformer\\ReturnRefTransformer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/Transformer/ReturnRefTransformer.php',
    'PhpCsFixer\\Tokenizer\\Transformer\\SquareBraceTransformer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/Transformer/SquareBraceTransformer.php',
    'PhpCsFixer\\Tokenizer\\Transformer\\TypeAlternationTransformer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/Transformer/TypeAlternationTransformer.php',
    'PhpCsFixer\\Tokenizer\\Transformer\\TypeColonTransformer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/Transformer/TypeColonTransformer.php',
    'PhpCsFixer\\Tokenizer\\Transformer\\TypeIntersectionTransformer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/Transformer/TypeIntersectionTransformer.php',
    'PhpCsFixer\\Tokenizer\\Transformer\\UseTransformer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/Transformer/UseTransformer.php',
    'PhpCsFixer\\Tokenizer\\Transformer\\WhitespacyCommentTransformer' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/Transformer/WhitespacyCommentTransformer.php',
    'PhpCsFixer\\Tokenizer\\Transformers' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Tokenizer/Transformers.php',
    'PhpCsFixer\\ToolInfo' => $vendorDir . '/friendsofphp/php-cs-fixer/src/ToolInfo.php',
    'PhpCsFixer\\ToolInfoInterface' => $vendorDir . '/friendsofphp/php-cs-fixer/src/ToolInfoInterface.php',
    'PhpCsFixer\\Utils' => $vendorDir . '/friendsofphp/php-cs-fixer/src/Utils.php',
    'PhpCsFixer\\WhitespacesFixerConfig' => $vendorDir . '/friendsofphp/php-cs-fixer/src/WhitespacesFixerConfig.php',
    'PhpCsFixer\\WordMatcher' => $vendorDir . '/friendsofphp/php-cs-fixer/src/WordMatcher.php',
    'PhpOption\\LazyOption' => $vendorDir . '/phpoption/phpoption/src/PhpOption/LazyOption.php',
    'PhpOption\\None' => $vendorDir . '/phpoption/phpoption/src/PhpOption/None.php',
    'PhpOption\\Option' => $vendorDir . '/phpoption/phpoption/src/PhpOption/Option.php',
    'PhpOption\\Some' => $vendorDir . '/phpoption/phpoption/src/PhpOption/Some.php',
    'PhpParser\\Builder' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Builder.php',
    'PhpParser\\BuilderFactory' => $vendorDir . '/nikic/php-parser/lib/PhpParser/BuilderFactory.php',
    'PhpParser\\BuilderHelpers' => $vendorDir . '/nikic/php-parser/lib/PhpParser/BuilderHelpers.php',
    'PhpParser\\Builder\\ClassConst' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Builder/ClassConst.php',
    'PhpParser\\Builder\\Class_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Builder/Class_.php',
    'PhpParser\\Builder\\Declaration' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Builder/Declaration.php',
    'PhpParser\\Builder\\EnumCase' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Builder/EnumCase.php',
    'PhpParser\\Builder\\Enum_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Builder/Enum_.php',
    'PhpParser\\Builder\\FunctionLike' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Builder/FunctionLike.php',
    'PhpParser\\Builder\\Function_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Builder/Function_.php',
    'PhpParser\\Builder\\Interface_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Builder/Interface_.php',
    'PhpParser\\Builder\\Method' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Builder/Method.php',
    'PhpParser\\Builder\\Namespace_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Builder/Namespace_.php',
    'PhpParser\\Builder\\Param' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Builder/Param.php',
    'PhpParser\\Builder\\Property' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Builder/Property.php',
    'PhpParser\\Builder\\TraitUse' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Builder/TraitUse.php',
    'PhpParser\\Builder\\TraitUseAdaptation' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Builder/TraitUseAdaptation.php',
    'PhpParser\\Builder\\Trait_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Builder/Trait_.php',
    'PhpParser\\Builder\\Use_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Builder/Use_.php',
    'PhpParser\\Comment' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Comment.php',
    'PhpParser\\Comment\\Doc' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Comment/Doc.php',
    'PhpParser\\ConstExprEvaluationException' => $vendorDir . '/nikic/php-parser/lib/PhpParser/ConstExprEvaluationException.php',
    'PhpParser\\ConstExprEvaluator' => $vendorDir . '/nikic/php-parser/lib/PhpParser/ConstExprEvaluator.php',
    'PhpParser\\Error' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Error.php',
    'PhpParser\\ErrorHandler' => $vendorDir . '/nikic/php-parser/lib/PhpParser/ErrorHandler.php',
    'PhpParser\\ErrorHandler\\Collecting' => $vendorDir . '/nikic/php-parser/lib/PhpParser/ErrorHandler/Collecting.php',
    'PhpParser\\ErrorHandler\\Throwing' => $vendorDir . '/nikic/php-parser/lib/PhpParser/ErrorHandler/Throwing.php',
    'PhpParser\\Internal\\DiffElem' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Internal/DiffElem.php',
    'PhpParser\\Internal\\Differ' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Internal/Differ.php',
    'PhpParser\\Internal\\PrintableNewAnonClassNode' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Internal/PrintableNewAnonClassNode.php',
    'PhpParser\\Internal\\TokenPolyfill' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Internal/TokenPolyfill.php',
    'PhpParser\\Internal\\TokenStream' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Internal/TokenStream.php',
    'PhpParser\\JsonDecoder' => $vendorDir . '/nikic/php-parser/lib/PhpParser/JsonDecoder.php',
    'PhpParser\\Lexer' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Lexer.php',
    'PhpParser\\Lexer\\Emulative' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Lexer/Emulative.php',
    'PhpParser\\Lexer\\TokenEmulator\\AsymmetricVisibilityTokenEmulator' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Lexer/TokenEmulator/AsymmetricVisibilityTokenEmulator.php',
    'PhpParser\\Lexer\\TokenEmulator\\AttributeEmulator' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Lexer/TokenEmulator/AttributeEmulator.php',
    'PhpParser\\Lexer\\TokenEmulator\\EnumTokenEmulator' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Lexer/TokenEmulator/EnumTokenEmulator.php',
    'PhpParser\\Lexer\\TokenEmulator\\ExplicitOctalEmulator' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Lexer/TokenEmulator/ExplicitOctalEmulator.php',
    'PhpParser\\Lexer\\TokenEmulator\\KeywordEmulator' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Lexer/TokenEmulator/KeywordEmulator.php',
    'PhpParser\\Lexer\\TokenEmulator\\MatchTokenEmulator' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Lexer/TokenEmulator/MatchTokenEmulator.php',
    'PhpParser\\Lexer\\TokenEmulator\\NullsafeTokenEmulator' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Lexer/TokenEmulator/NullsafeTokenEmulator.php',
    'PhpParser\\Lexer\\TokenEmulator\\PropertyTokenEmulator' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Lexer/TokenEmulator/PropertyTokenEmulator.php',
    'PhpParser\\Lexer\\TokenEmulator\\ReadonlyFunctionTokenEmulator' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Lexer/TokenEmulator/ReadonlyFunctionTokenEmulator.php',
    'PhpParser\\Lexer\\TokenEmulator\\ReadonlyTokenEmulator' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Lexer/TokenEmulator/ReadonlyTokenEmulator.php',
    'PhpParser\\Lexer\\TokenEmulator\\ReverseEmulator' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Lexer/TokenEmulator/ReverseEmulator.php',
    'PhpParser\\Lexer\\TokenEmulator\\TokenEmulator' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Lexer/TokenEmulator/TokenEmulator.php',
    'PhpParser\\Modifiers' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Modifiers.php',
    'PhpParser\\NameContext' => $vendorDir . '/nikic/php-parser/lib/PhpParser/NameContext.php',
    'PhpParser\\Node' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node.php',
    'PhpParser\\NodeAbstract' => $vendorDir . '/nikic/php-parser/lib/PhpParser/NodeAbstract.php',
    'PhpParser\\NodeDumper' => $vendorDir . '/nikic/php-parser/lib/PhpParser/NodeDumper.php',
    'PhpParser\\NodeFinder' => $vendorDir . '/nikic/php-parser/lib/PhpParser/NodeFinder.php',
    'PhpParser\\NodeTraverser' => $vendorDir . '/nikic/php-parser/lib/PhpParser/NodeTraverser.php',
    'PhpParser\\NodeTraverserInterface' => $vendorDir . '/nikic/php-parser/lib/PhpParser/NodeTraverserInterface.php',
    'PhpParser\\NodeVisitor' => $vendorDir . '/nikic/php-parser/lib/PhpParser/NodeVisitor.php',
    'PhpParser\\NodeVisitorAbstract' => $vendorDir . '/nikic/php-parser/lib/PhpParser/NodeVisitorAbstract.php',
    'PhpParser\\NodeVisitor\\CloningVisitor' => $vendorDir . '/nikic/php-parser/lib/PhpParser/NodeVisitor/CloningVisitor.php',
    'PhpParser\\NodeVisitor\\CommentAnnotatingVisitor' => $vendorDir . '/nikic/php-parser/lib/PhpParser/NodeVisitor/CommentAnnotatingVisitor.php',
    'PhpParser\\NodeVisitor\\FindingVisitor' => $vendorDir . '/nikic/php-parser/lib/PhpParser/NodeVisitor/FindingVisitor.php',
    'PhpParser\\NodeVisitor\\FirstFindingVisitor' => $vendorDir . '/nikic/php-parser/lib/PhpParser/NodeVisitor/FirstFindingVisitor.php',
    'PhpParser\\NodeVisitor\\NameResolver' => $vendorDir . '/nikic/php-parser/lib/PhpParser/NodeVisitor/NameResolver.php',
    'PhpParser\\NodeVisitor\\NodeConnectingVisitor' => $vendorDir . '/nikic/php-parser/lib/PhpParser/NodeVisitor/NodeConnectingVisitor.php',
    'PhpParser\\NodeVisitor\\ParentConnectingVisitor' => $vendorDir . '/nikic/php-parser/lib/PhpParser/NodeVisitor/ParentConnectingVisitor.php',
    'PhpParser\\Node\\Arg' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Arg.php',
    'PhpParser\\Node\\ArrayItem' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/ArrayItem.php',
    'PhpParser\\Node\\Attribute' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Attribute.php',
    'PhpParser\\Node\\AttributeGroup' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/AttributeGroup.php',
    'PhpParser\\Node\\ClosureUse' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/ClosureUse.php',
    'PhpParser\\Node\\ComplexType' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/ComplexType.php',
    'PhpParser\\Node\\Const_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Const_.php',
    'PhpParser\\Node\\DeclareItem' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/DeclareItem.php',
    'PhpParser\\Node\\Expr' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr.php',
    'PhpParser\\Node\\Expr\\ArrayDimFetch' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/ArrayDimFetch.php',
    'PhpParser\\Node\\Expr\\ArrayItem' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/ArrayItem.php',
    'PhpParser\\Node\\Expr\\Array_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Array_.php',
    'PhpParser\\Node\\Expr\\ArrowFunction' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/ArrowFunction.php',
    'PhpParser\\Node\\Expr\\Assign' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Assign.php',
    'PhpParser\\Node\\Expr\\AssignOp' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/AssignOp.php',
    'PhpParser\\Node\\Expr\\AssignOp\\BitwiseAnd' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/AssignOp/BitwiseAnd.php',
    'PhpParser\\Node\\Expr\\AssignOp\\BitwiseOr' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/AssignOp/BitwiseOr.php',
    'PhpParser\\Node\\Expr\\AssignOp\\BitwiseXor' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/AssignOp/BitwiseXor.php',
    'PhpParser\\Node\\Expr\\AssignOp\\Coalesce' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/AssignOp/Coalesce.php',
    'PhpParser\\Node\\Expr\\AssignOp\\Concat' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/AssignOp/Concat.php',
    'PhpParser\\Node\\Expr\\AssignOp\\Div' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/AssignOp/Div.php',
    'PhpParser\\Node\\Expr\\AssignOp\\Minus' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/AssignOp/Minus.php',
    'PhpParser\\Node\\Expr\\AssignOp\\Mod' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/AssignOp/Mod.php',
    'PhpParser\\Node\\Expr\\AssignOp\\Mul' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/AssignOp/Mul.php',
    'PhpParser\\Node\\Expr\\AssignOp\\Plus' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/AssignOp/Plus.php',
    'PhpParser\\Node\\Expr\\AssignOp\\Pow' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/AssignOp/Pow.php',
    'PhpParser\\Node\\Expr\\AssignOp\\ShiftLeft' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/AssignOp/ShiftLeft.php',
    'PhpParser\\Node\\Expr\\AssignOp\\ShiftRight' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/AssignOp/ShiftRight.php',
    'PhpParser\\Node\\Expr\\AssignRef' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/AssignRef.php',
    'PhpParser\\Node\\Expr\\BinaryOp' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\BitwiseAnd' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/BitwiseAnd.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\BitwiseOr' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/BitwiseOr.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\BitwiseXor' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/BitwiseXor.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\BooleanAnd' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/BooleanAnd.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\BooleanOr' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/BooleanOr.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\Coalesce' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/Coalesce.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\Concat' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/Concat.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\Div' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/Div.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\Equal' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/Equal.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\Greater' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/Greater.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\GreaterOrEqual' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/GreaterOrEqual.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\Identical' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/Identical.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\LogicalAnd' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/LogicalAnd.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\LogicalOr' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/LogicalOr.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\LogicalXor' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/LogicalXor.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\Minus' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/Minus.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\Mod' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/Mod.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\Mul' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/Mul.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\NotEqual' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/NotEqual.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\NotIdentical' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/NotIdentical.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\Plus' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/Plus.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\Pow' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/Pow.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\ShiftLeft' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/ShiftLeft.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\ShiftRight' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/ShiftRight.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\Smaller' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/Smaller.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\SmallerOrEqual' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/SmallerOrEqual.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\Spaceship' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/Spaceship.php',
    'PhpParser\\Node\\Expr\\BitwiseNot' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BitwiseNot.php',
    'PhpParser\\Node\\Expr\\BooleanNot' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BooleanNot.php',
    'PhpParser\\Node\\Expr\\CallLike' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/CallLike.php',
    'PhpParser\\Node\\Expr\\Cast' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Cast.php',
    'PhpParser\\Node\\Expr\\Cast\\Array_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Cast/Array_.php',
    'PhpParser\\Node\\Expr\\Cast\\Bool_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Cast/Bool_.php',
    'PhpParser\\Node\\Expr\\Cast\\Double' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Cast/Double.php',
    'PhpParser\\Node\\Expr\\Cast\\Int_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Cast/Int_.php',
    'PhpParser\\Node\\Expr\\Cast\\Object_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Cast/Object_.php',
    'PhpParser\\Node\\Expr\\Cast\\String_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Cast/String_.php',
    'PhpParser\\Node\\Expr\\Cast\\Unset_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Cast/Unset_.php',
    'PhpParser\\Node\\Expr\\ClassConstFetch' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/ClassConstFetch.php',
    'PhpParser\\Node\\Expr\\Clone_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Clone_.php',
    'PhpParser\\Node\\Expr\\Closure' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Closure.php',
    'PhpParser\\Node\\Expr\\ClosureUse' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/ClosureUse.php',
    'PhpParser\\Node\\Expr\\ConstFetch' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/ConstFetch.php',
    'PhpParser\\Node\\Expr\\Empty_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Empty_.php',
    'PhpParser\\Node\\Expr\\Error' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Error.php',
    'PhpParser\\Node\\Expr\\ErrorSuppress' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/ErrorSuppress.php',
    'PhpParser\\Node\\Expr\\Eval_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Eval_.php',
    'PhpParser\\Node\\Expr\\Exit_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Exit_.php',
    'PhpParser\\Node\\Expr\\FuncCall' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/FuncCall.php',
    'PhpParser\\Node\\Expr\\Include_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Include_.php',
    'PhpParser\\Node\\Expr\\Instanceof_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Instanceof_.php',
    'PhpParser\\Node\\Expr\\Isset_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Isset_.php',
    'PhpParser\\Node\\Expr\\List_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/List_.php',
    'PhpParser\\Node\\Expr\\Match_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Match_.php',
    'PhpParser\\Node\\Expr\\MethodCall' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/MethodCall.php',
    'PhpParser\\Node\\Expr\\New_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/New_.php',
    'PhpParser\\Node\\Expr\\NullsafeMethodCall' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/NullsafeMethodCall.php',
    'PhpParser\\Node\\Expr\\NullsafePropertyFetch' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/NullsafePropertyFetch.php',
    'PhpParser\\Node\\Expr\\PostDec' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/PostDec.php',
    'PhpParser\\Node\\Expr\\PostInc' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/PostInc.php',
    'PhpParser\\Node\\Expr\\PreDec' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/PreDec.php',
    'PhpParser\\Node\\Expr\\PreInc' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/PreInc.php',
    'PhpParser\\Node\\Expr\\Print_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Print_.php',
    'PhpParser\\Node\\Expr\\PropertyFetch' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/PropertyFetch.php',
    'PhpParser\\Node\\Expr\\ShellExec' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/ShellExec.php',
    'PhpParser\\Node\\Expr\\StaticCall' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/StaticCall.php',
    'PhpParser\\Node\\Expr\\StaticPropertyFetch' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/StaticPropertyFetch.php',
    'PhpParser\\Node\\Expr\\Ternary' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Ternary.php',
    'PhpParser\\Node\\Expr\\Throw_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Throw_.php',
    'PhpParser\\Node\\Expr\\UnaryMinus' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/UnaryMinus.php',
    'PhpParser\\Node\\Expr\\UnaryPlus' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/UnaryPlus.php',
    'PhpParser\\Node\\Expr\\Variable' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Variable.php',
    'PhpParser\\Node\\Expr\\YieldFrom' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/YieldFrom.php',
    'PhpParser\\Node\\Expr\\Yield_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Yield_.php',
    'PhpParser\\Node\\FunctionLike' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/FunctionLike.php',
    'PhpParser\\Node\\Identifier' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Identifier.php',
    'PhpParser\\Node\\InterpolatedStringPart' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/InterpolatedStringPart.php',
    'PhpParser\\Node\\IntersectionType' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/IntersectionType.php',
    'PhpParser\\Node\\MatchArm' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/MatchArm.php',
    'PhpParser\\Node\\Name' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Name.php',
    'PhpParser\\Node\\Name\\FullyQualified' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Name/FullyQualified.php',
    'PhpParser\\Node\\Name\\Relative' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Name/Relative.php',
    'PhpParser\\Node\\NullableType' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/NullableType.php',
    'PhpParser\\Node\\Param' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Param.php',
    'PhpParser\\Node\\PropertyHook' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/PropertyHook.php',
    'PhpParser\\Node\\PropertyItem' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/PropertyItem.php',
    'PhpParser\\Node\\Scalar' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar.php',
    'PhpParser\\Node\\Scalar\\DNumber' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar/DNumber.php',
    'PhpParser\\Node\\Scalar\\Encapsed' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar/Encapsed.php',
    'PhpParser\\Node\\Scalar\\EncapsedStringPart' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar/EncapsedStringPart.php',
    'PhpParser\\Node\\Scalar\\Float_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar/Float_.php',
    'PhpParser\\Node\\Scalar\\Int_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar/Int_.php',
    'PhpParser\\Node\\Scalar\\InterpolatedString' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar/InterpolatedString.php',
    'PhpParser\\Node\\Scalar\\LNumber' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar/LNumber.php',
    'PhpParser\\Node\\Scalar\\MagicConst' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar/MagicConst.php',
    'PhpParser\\Node\\Scalar\\MagicConst\\Class_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar/MagicConst/Class_.php',
    'PhpParser\\Node\\Scalar\\MagicConst\\Dir' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar/MagicConst/Dir.php',
    'PhpParser\\Node\\Scalar\\MagicConst\\File' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar/MagicConst/File.php',
    'PhpParser\\Node\\Scalar\\MagicConst\\Function_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar/MagicConst/Function_.php',
    'PhpParser\\Node\\Scalar\\MagicConst\\Line' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar/MagicConst/Line.php',
    'PhpParser\\Node\\Scalar\\MagicConst\\Method' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar/MagicConst/Method.php',
    'PhpParser\\Node\\Scalar\\MagicConst\\Namespace_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar/MagicConst/Namespace_.php',
    'PhpParser\\Node\\Scalar\\MagicConst\\Property' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar/MagicConst/Property.php',
    'PhpParser\\Node\\Scalar\\MagicConst\\Trait_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar/MagicConst/Trait_.php',
    'PhpParser\\Node\\Scalar\\String_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar/String_.php',
    'PhpParser\\Node\\StaticVar' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/StaticVar.php',
    'PhpParser\\Node\\Stmt' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt.php',
    'PhpParser\\Node\\Stmt\\Block' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Block.php',
    'PhpParser\\Node\\Stmt\\Break_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Break_.php',
    'PhpParser\\Node\\Stmt\\Case_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Case_.php',
    'PhpParser\\Node\\Stmt\\Catch_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Catch_.php',
    'PhpParser\\Node\\Stmt\\ClassConst' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/ClassConst.php',
    'PhpParser\\Node\\Stmt\\ClassLike' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/ClassLike.php',
    'PhpParser\\Node\\Stmt\\ClassMethod' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/ClassMethod.php',
    'PhpParser\\Node\\Stmt\\Class_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Class_.php',
    'PhpParser\\Node\\Stmt\\Const_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Const_.php',
    'PhpParser\\Node\\Stmt\\Continue_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Continue_.php',
    'PhpParser\\Node\\Stmt\\DeclareDeclare' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/DeclareDeclare.php',
    'PhpParser\\Node\\Stmt\\Declare_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Declare_.php',
    'PhpParser\\Node\\Stmt\\Do_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Do_.php',
    'PhpParser\\Node\\Stmt\\Echo_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Echo_.php',
    'PhpParser\\Node\\Stmt\\ElseIf_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/ElseIf_.php',
    'PhpParser\\Node\\Stmt\\Else_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Else_.php',
    'PhpParser\\Node\\Stmt\\EnumCase' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/EnumCase.php',
    'PhpParser\\Node\\Stmt\\Enum_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Enum_.php',
    'PhpParser\\Node\\Stmt\\Expression' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Expression.php',
    'PhpParser\\Node\\Stmt\\Finally_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Finally_.php',
    'PhpParser\\Node\\Stmt\\For_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/For_.php',
    'PhpParser\\Node\\Stmt\\Foreach_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Foreach_.php',
    'PhpParser\\Node\\Stmt\\Function_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Function_.php',
    'PhpParser\\Node\\Stmt\\Global_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Global_.php',
    'PhpParser\\Node\\Stmt\\Goto_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Goto_.php',
    'PhpParser\\Node\\Stmt\\GroupUse' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/GroupUse.php',
    'PhpParser\\Node\\Stmt\\HaltCompiler' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/HaltCompiler.php',
    'PhpParser\\Node\\Stmt\\If_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/If_.php',
    'PhpParser\\Node\\Stmt\\InlineHTML' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/InlineHTML.php',
    'PhpParser\\Node\\Stmt\\Interface_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Interface_.php',
    'PhpParser\\Node\\Stmt\\Label' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Label.php',
    'PhpParser\\Node\\Stmt\\Namespace_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Namespace_.php',
    'PhpParser\\Node\\Stmt\\Nop' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Nop.php',
    'PhpParser\\Node\\Stmt\\Property' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Property.php',
    'PhpParser\\Node\\Stmt\\PropertyProperty' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/PropertyProperty.php',
    'PhpParser\\Node\\Stmt\\Return_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Return_.php',
    'PhpParser\\Node\\Stmt\\StaticVar' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/StaticVar.php',
    'PhpParser\\Node\\Stmt\\Static_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Static_.php',
    'PhpParser\\Node\\Stmt\\Switch_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Switch_.php',
    'PhpParser\\Node\\Stmt\\TraitUse' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/TraitUse.php',
    'PhpParser\\Node\\Stmt\\TraitUseAdaptation' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/TraitUseAdaptation.php',
    'PhpParser\\Node\\Stmt\\TraitUseAdaptation\\Alias' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/TraitUseAdaptation/Alias.php',
    'PhpParser\\Node\\Stmt\\TraitUseAdaptation\\Precedence' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/TraitUseAdaptation/Precedence.php',
    'PhpParser\\Node\\Stmt\\Trait_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Trait_.php',
    'PhpParser\\Node\\Stmt\\TryCatch' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/TryCatch.php',
    'PhpParser\\Node\\Stmt\\Unset_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Unset_.php',
    'PhpParser\\Node\\Stmt\\UseUse' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/UseUse.php',
    'PhpParser\\Node\\Stmt\\Use_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Use_.php',
    'PhpParser\\Node\\Stmt\\While_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/While_.php',
    'PhpParser\\Node\\UnionType' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/UnionType.php',
    'PhpParser\\Node\\UseItem' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/UseItem.php',
    'PhpParser\\Node\\VarLikeIdentifier' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/VarLikeIdentifier.php',
    'PhpParser\\Node\\VariadicPlaceholder' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/VariadicPlaceholder.php',
    'PhpParser\\Parser' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Parser.php',
    'PhpParser\\ParserAbstract' => $vendorDir . '/nikic/php-parser/lib/PhpParser/ParserAbstract.php',
    'PhpParser\\ParserFactory' => $vendorDir . '/nikic/php-parser/lib/PhpParser/ParserFactory.php',
    'PhpParser\\Parser\\Php7' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Parser/Php7.php',
    'PhpParser\\Parser\\Php8' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Parser/Php8.php',
    'PhpParser\\PhpVersion' => $vendorDir . '/nikic/php-parser/lib/PhpParser/PhpVersion.php',
    'PhpParser\\PrettyPrinter' => $vendorDir . '/nikic/php-parser/lib/PhpParser/PrettyPrinter.php',
    'PhpParser\\PrettyPrinterAbstract' => $vendorDir . '/nikic/php-parser/lib/PhpParser/PrettyPrinterAbstract.php',
    'PhpParser\\PrettyPrinter\\Standard' => $vendorDir . '/nikic/php-parser/lib/PhpParser/PrettyPrinter/Standard.php',
    'PhpParser\\Token' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Token.php',
    'PhpToken' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/PhpToken.php',
    'Predis\\Autoloader' => $vendorDir . '/predis/predis/src/Autoloader.php',
    'Predis\\Client' => $vendorDir . '/predis/predis/src/Client.php',
    'Predis\\ClientConfiguration' => $vendorDir . '/predis/predis/src/ClientConfiguration.php',
    'Predis\\ClientContextInterface' => $vendorDir . '/predis/predis/src/ClientContextInterface.php',
    'Predis\\ClientException' => $vendorDir . '/predis/predis/src/ClientException.php',
    'Predis\\ClientInterface' => $vendorDir . '/predis/predis/src/ClientInterface.php',
    'Predis\\Cluster\\ClusterStrategy' => $vendorDir . '/predis/predis/src/Cluster/ClusterStrategy.php',
    'Predis\\Cluster\\Distributor\\DistributorInterface' => $vendorDir . '/predis/predis/src/Cluster/Distributor/DistributorInterface.php',
    'Predis\\Cluster\\Distributor\\EmptyRingException' => $vendorDir . '/predis/predis/src/Cluster/Distributor/EmptyRingException.php',
    'Predis\\Cluster\\Distributor\\HashRing' => $vendorDir . '/predis/predis/src/Cluster/Distributor/HashRing.php',
    'Predis\\Cluster\\Distributor\\KetamaRing' => $vendorDir . '/predis/predis/src/Cluster/Distributor/KetamaRing.php',
    'Predis\\Cluster\\Hash\\CRC16' => $vendorDir . '/predis/predis/src/Cluster/Hash/CRC16.php',
    'Predis\\Cluster\\Hash\\HashGeneratorInterface' => $vendorDir . '/predis/predis/src/Cluster/Hash/HashGeneratorInterface.php',
    'Predis\\Cluster\\Hash\\PhpiredisCRC16' => $vendorDir . '/predis/predis/src/Cluster/Hash/PhpiredisCRC16.php',
    'Predis\\Cluster\\NullSlotRange' => $vendorDir . '/predis/predis/src/Cluster/NullSlotRange.php',
    'Predis\\Cluster\\PredisStrategy' => $vendorDir . '/predis/predis/src/Cluster/PredisStrategy.php',
    'Predis\\Cluster\\RedisStrategy' => $vendorDir . '/predis/predis/src/Cluster/RedisStrategy.php',
    'Predis\\Cluster\\SimpleSlotMap' => $vendorDir . '/predis/predis/src/Cluster/SimpleSlotMap.php',
    'Predis\\Cluster\\SlotMap' => $vendorDir . '/predis/predis/src/Cluster/SlotMap.php',
    'Predis\\Cluster\\SlotRange' => $vendorDir . '/predis/predis/src/Cluster/SlotRange.php',
    'Predis\\Cluster\\StrategyInterface' => $vendorDir . '/predis/predis/src/Cluster/StrategyInterface.php',
    'Predis\\Collection\\Iterator\\CursorBasedIterator' => $vendorDir . '/predis/predis/src/Collection/Iterator/CursorBasedIterator.php',
    'Predis\\Collection\\Iterator\\HashKey' => $vendorDir . '/predis/predis/src/Collection/Iterator/HashKey.php',
    'Predis\\Collection\\Iterator\\Keyspace' => $vendorDir . '/predis/predis/src/Collection/Iterator/Keyspace.php',
    'Predis\\Collection\\Iterator\\ListKey' => $vendorDir . '/predis/predis/src/Collection/Iterator/ListKey.php',
    'Predis\\Collection\\Iterator\\SetKey' => $vendorDir . '/predis/predis/src/Collection/Iterator/SetKey.php',
    'Predis\\Collection\\Iterator\\SortedSetKey' => $vendorDir . '/predis/predis/src/Collection/Iterator/SortedSetKey.php',
    'Predis\\Command\\Argument\\ArrayableArgument' => $vendorDir . '/predis/predis/src/Command/Argument/ArrayableArgument.php',
    'Predis\\Command\\Argument\\Geospatial\\AbstractBy' => $vendorDir . '/predis/predis/src/Command/Argument/Geospatial/AbstractBy.php',
    'Predis\\Command\\Argument\\Geospatial\\ByBox' => $vendorDir . '/predis/predis/src/Command/Argument/Geospatial/ByBox.php',
    'Predis\\Command\\Argument\\Geospatial\\ByInterface' => $vendorDir . '/predis/predis/src/Command/Argument/Geospatial/ByInterface.php',
    'Predis\\Command\\Argument\\Geospatial\\ByRadius' => $vendorDir . '/predis/predis/src/Command/Argument/Geospatial/ByRadius.php',
    'Predis\\Command\\Argument\\Geospatial\\FromInterface' => $vendorDir . '/predis/predis/src/Command/Argument/Geospatial/FromInterface.php',
    'Predis\\Command\\Argument\\Geospatial\\FromLonLat' => $vendorDir . '/predis/predis/src/Command/Argument/Geospatial/FromLonLat.php',
    'Predis\\Command\\Argument\\Geospatial\\FromMember' => $vendorDir . '/predis/predis/src/Command/Argument/Geospatial/FromMember.php',
    'Predis\\Command\\Argument\\Search\\AggregateArguments' => $vendorDir . '/predis/predis/src/Command/Argument/Search/AggregateArguments.php',
    'Predis\\Command\\Argument\\Search\\AlterArguments' => $vendorDir . '/predis/predis/src/Command/Argument/Search/AlterArguments.php',
    'Predis\\Command\\Argument\\Search\\CommonArguments' => $vendorDir . '/predis/predis/src/Command/Argument/Search/CommonArguments.php',
    'Predis\\Command\\Argument\\Search\\CreateArguments' => $vendorDir . '/predis/predis/src/Command/Argument/Search/CreateArguments.php',
    'Predis\\Command\\Argument\\Search\\CursorArguments' => $vendorDir . '/predis/predis/src/Command/Argument/Search/CursorArguments.php',
    'Predis\\Command\\Argument\\Search\\DropArguments' => $vendorDir . '/predis/predis/src/Command/Argument/Search/DropArguments.php',
    'Predis\\Command\\Argument\\Search\\ExplainArguments' => $vendorDir . '/predis/predis/src/Command/Argument/Search/ExplainArguments.php',
    'Predis\\Command\\Argument\\Search\\ProfileArguments' => $vendorDir . '/predis/predis/src/Command/Argument/Search/ProfileArguments.php',
    'Predis\\Command\\Argument\\Search\\SchemaFields\\AbstractField' => $vendorDir . '/predis/predis/src/Command/Argument/Search/SchemaFields/AbstractField.php',
    'Predis\\Command\\Argument\\Search\\SchemaFields\\FieldInterface' => $vendorDir . '/predis/predis/src/Command/Argument/Search/SchemaFields/FieldInterface.php',
    'Predis\\Command\\Argument\\Search\\SchemaFields\\GeoField' => $vendorDir . '/predis/predis/src/Command/Argument/Search/SchemaFields/GeoField.php',
    'Predis\\Command\\Argument\\Search\\SchemaFields\\GeoShapeField' => $vendorDir . '/predis/predis/src/Command/Argument/Search/SchemaFields/GeoShapeField.php',
    'Predis\\Command\\Argument\\Search\\SchemaFields\\NumericField' => $vendorDir . '/predis/predis/src/Command/Argument/Search/SchemaFields/NumericField.php',
    'Predis\\Command\\Argument\\Search\\SchemaFields\\TagField' => $vendorDir . '/predis/predis/src/Command/Argument/Search/SchemaFields/TagField.php',
    'Predis\\Command\\Argument\\Search\\SchemaFields\\TextField' => $vendorDir . '/predis/predis/src/Command/Argument/Search/SchemaFields/TextField.php',
    'Predis\\Command\\Argument\\Search\\SchemaFields\\VectorField' => $vendorDir . '/predis/predis/src/Command/Argument/Search/SchemaFields/VectorField.php',
    'Predis\\Command\\Argument\\Search\\SearchArguments' => $vendorDir . '/predis/predis/src/Command/Argument/Search/SearchArguments.php',
    'Predis\\Command\\Argument\\Search\\SpellcheckArguments' => $vendorDir . '/predis/predis/src/Command/Argument/Search/SpellcheckArguments.php',
    'Predis\\Command\\Argument\\Search\\SugAddArguments' => $vendorDir . '/predis/predis/src/Command/Argument/Search/SugAddArguments.php',
    'Predis\\Command\\Argument\\Search\\SugGetArguments' => $vendorDir . '/predis/predis/src/Command/Argument/Search/SugGetArguments.php',
    'Predis\\Command\\Argument\\Search\\SynUpdateArguments' => $vendorDir . '/predis/predis/src/Command/Argument/Search/SynUpdateArguments.php',
    'Predis\\Command\\Argument\\Server\\LimitInterface' => $vendorDir . '/predis/predis/src/Command/Argument/Server/LimitInterface.php',
    'Predis\\Command\\Argument\\Server\\LimitOffsetCount' => $vendorDir . '/predis/predis/src/Command/Argument/Server/LimitOffsetCount.php',
    'Predis\\Command\\Argument\\Server\\To' => $vendorDir . '/predis/predis/src/Command/Argument/Server/To.php',
    'Predis\\Command\\Argument\\TimeSeries\\AddArguments' => $vendorDir . '/predis/predis/src/Command/Argument/TimeSeries/AddArguments.php',
    'Predis\\Command\\Argument\\TimeSeries\\AlterArguments' => $vendorDir . '/predis/predis/src/Command/Argument/TimeSeries/AlterArguments.php',
    'Predis\\Command\\Argument\\TimeSeries\\CommonArguments' => $vendorDir . '/predis/predis/src/Command/Argument/TimeSeries/CommonArguments.php',
    'Predis\\Command\\Argument\\TimeSeries\\CreateArguments' => $vendorDir . '/predis/predis/src/Command/Argument/TimeSeries/CreateArguments.php',
    'Predis\\Command\\Argument\\TimeSeries\\DecrByArguments' => $vendorDir . '/predis/predis/src/Command/Argument/TimeSeries/DecrByArguments.php',
    'Predis\\Command\\Argument\\TimeSeries\\GetArguments' => $vendorDir . '/predis/predis/src/Command/Argument/TimeSeries/GetArguments.php',
    'Predis\\Command\\Argument\\TimeSeries\\IncrByArguments' => $vendorDir . '/predis/predis/src/Command/Argument/TimeSeries/IncrByArguments.php',
    'Predis\\Command\\Argument\\TimeSeries\\InfoArguments' => $vendorDir . '/predis/predis/src/Command/Argument/TimeSeries/InfoArguments.php',
    'Predis\\Command\\Argument\\TimeSeries\\MGetArguments' => $vendorDir . '/predis/predis/src/Command/Argument/TimeSeries/MGetArguments.php',
    'Predis\\Command\\Argument\\TimeSeries\\MRangeArguments' => $vendorDir . '/predis/predis/src/Command/Argument/TimeSeries/MRangeArguments.php',
    'Predis\\Command\\Argument\\TimeSeries\\RangeArguments' => $vendorDir . '/predis/predis/src/Command/Argument/TimeSeries/RangeArguments.php',
    'Predis\\Command\\Command' => $vendorDir . '/predis/predis/src/Command/Command.php',
    'Predis\\Command\\CommandInterface' => $vendorDir . '/predis/predis/src/Command/CommandInterface.php',
    'Predis\\Command\\Factory' => $vendorDir . '/predis/predis/src/Command/Factory.php',
    'Predis\\Command\\FactoryInterface' => $vendorDir . '/predis/predis/src/Command/FactoryInterface.php',
    'Predis\\Command\\PrefixableCommandInterface' => $vendorDir . '/predis/predis/src/Command/PrefixableCommandInterface.php',
    'Predis\\Command\\Processor\\KeyPrefixProcessor' => $vendorDir . '/predis/predis/src/Command/Processor/KeyPrefixProcessor.php',
    'Predis\\Command\\Processor\\ProcessorChain' => $vendorDir . '/predis/predis/src/Command/Processor/ProcessorChain.php',
    'Predis\\Command\\Processor\\ProcessorInterface' => $vendorDir . '/predis/predis/src/Command/Processor/ProcessorInterface.php',
    'Predis\\Command\\RawCommand' => $vendorDir . '/predis/predis/src/Command/RawCommand.php',
    'Predis\\Command\\RawFactory' => $vendorDir . '/predis/predis/src/Command/RawFactory.php',
    'Predis\\Command\\RedisFactory' => $vendorDir . '/predis/predis/src/Command/RedisFactory.php',
    'Predis\\Command\\Redis\\ACL' => $vendorDir . '/predis/predis/src/Command/Redis/ACL.php',
    'Predis\\Command\\Redis\\APPEND' => $vendorDir . '/predis/predis/src/Command/Redis/APPEND.php',
    'Predis\\Command\\Redis\\AUTH' => $vendorDir . '/predis/predis/src/Command/Redis/AUTH.php',
    'Predis\\Command\\Redis\\AbstractCommand\\BZPOPBase' => $vendorDir . '/predis/predis/src/Command/Redis/AbstractCommand/BZPOPBase.php',
    'Predis\\Command\\Redis\\BGREWRITEAOF' => $vendorDir . '/predis/predis/src/Command/Redis/BGREWRITEAOF.php',
    'Predis\\Command\\Redis\\BGSAVE' => $vendorDir . '/predis/predis/src/Command/Redis/BGSAVE.php',
    'Predis\\Command\\Redis\\BITCOUNT' => $vendorDir . '/predis/predis/src/Command/Redis/BITCOUNT.php',
    'Predis\\Command\\Redis\\BITFIELD' => $vendorDir . '/predis/predis/src/Command/Redis/BITFIELD.php',
    'Predis\\Command\\Redis\\BITFIELD_RO' => $vendorDir . '/predis/predis/src/Command/Redis/BITFIELD_RO.php',
    'Predis\\Command\\Redis\\BITOP' => $vendorDir . '/predis/predis/src/Command/Redis/BITOP.php',
    'Predis\\Command\\Redis\\BITPOS' => $vendorDir . '/predis/predis/src/Command/Redis/BITPOS.php',
    'Predis\\Command\\Redis\\BLMOVE' => $vendorDir . '/predis/predis/src/Command/Redis/BLMOVE.php',
    'Predis\\Command\\Redis\\BLMPOP' => $vendorDir . '/predis/predis/src/Command/Redis/BLMPOP.php',
    'Predis\\Command\\Redis\\BLPOP' => $vendorDir . '/predis/predis/src/Command/Redis/BLPOP.php',
    'Predis\\Command\\Redis\\BRPOP' => $vendorDir . '/predis/predis/src/Command/Redis/BRPOP.php',
    'Predis\\Command\\Redis\\BRPOPLPUSH' => $vendorDir . '/predis/predis/src/Command/Redis/BRPOPLPUSH.php',
    'Predis\\Command\\Redis\\BZMPOP' => $vendorDir . '/predis/predis/src/Command/Redis/BZMPOP.php',
    'Predis\\Command\\Redis\\BZPOPMAX' => $vendorDir . '/predis/predis/src/Command/Redis/BZPOPMAX.php',
    'Predis\\Command\\Redis\\BZPOPMIN' => $vendorDir . '/predis/predis/src/Command/Redis/BZPOPMIN.php',
    'Predis\\Command\\Redis\\BloomFilter\\BFADD' => $vendorDir . '/predis/predis/src/Command/Redis/BloomFilter/BFADD.php',
    'Predis\\Command\\Redis\\BloomFilter\\BFEXISTS' => $vendorDir . '/predis/predis/src/Command/Redis/BloomFilter/BFEXISTS.php',
    'Predis\\Command\\Redis\\BloomFilter\\BFINFO' => $vendorDir . '/predis/predis/src/Command/Redis/BloomFilter/BFINFO.php',
    'Predis\\Command\\Redis\\BloomFilter\\BFINSERT' => $vendorDir . '/predis/predis/src/Command/Redis/BloomFilter/BFINSERT.php',
    'Predis\\Command\\Redis\\BloomFilter\\BFLOADCHUNK' => $vendorDir . '/predis/predis/src/Command/Redis/BloomFilter/BFLOADCHUNK.php',
    'Predis\\Command\\Redis\\BloomFilter\\BFMADD' => $vendorDir . '/predis/predis/src/Command/Redis/BloomFilter/BFMADD.php',
    'Predis\\Command\\Redis\\BloomFilter\\BFMEXISTS' => $vendorDir . '/predis/predis/src/Command/Redis/BloomFilter/BFMEXISTS.php',
    'Predis\\Command\\Redis\\BloomFilter\\BFRESERVE' => $vendorDir . '/predis/predis/src/Command/Redis/BloomFilter/BFRESERVE.php',
    'Predis\\Command\\Redis\\BloomFilter\\BFSCANDUMP' => $vendorDir . '/predis/predis/src/Command/Redis/BloomFilter/BFSCANDUMP.php',
    'Predis\\Command\\Redis\\CLIENT' => $vendorDir . '/predis/predis/src/Command/Redis/CLIENT.php',
    'Predis\\Command\\Redis\\CLUSTER' => $vendorDir . '/predis/predis/src/Command/Redis/CLUSTER.php',
    'Predis\\Command\\Redis\\COMMAND' => $vendorDir . '/predis/predis/src/Command/Redis/COMMAND.php',
    'Predis\\Command\\Redis\\CONFIG' => $vendorDir . '/predis/predis/src/Command/Redis/CONFIG.php',
    'Predis\\Command\\Redis\\COPY' => $vendorDir . '/predis/predis/src/Command/Redis/COPY.php',
    'Predis\\Command\\Redis\\Container\\ACL' => $vendorDir . '/predis/predis/src/Command/Redis/Container/ACL.php',
    'Predis\\Command\\Redis\\Container\\AbstractContainer' => $vendorDir . '/predis/predis/src/Command/Redis/Container/AbstractContainer.php',
    'Predis\\Command\\Redis\\Container\\CLUSTER' => $vendorDir . '/predis/predis/src/Command/Redis/Container/CLUSTER.php',
    'Predis\\Command\\Redis\\Container\\ContainerFactory' => $vendorDir . '/predis/predis/src/Command/Redis/Container/ContainerFactory.php',
    'Predis\\Command\\Redis\\Container\\ContainerInterface' => $vendorDir . '/predis/predis/src/Command/Redis/Container/ContainerInterface.php',
    'Predis\\Command\\Redis\\Container\\FunctionContainer' => $vendorDir . '/predis/predis/src/Command/Redis/Container/FunctionContainer.php',
    'Predis\\Command\\Redis\\Container\\Json\\JSONDEBUG' => $vendorDir . '/predis/predis/src/Command/Redis/Container/Json/JSONDEBUG.php',
    'Predis\\Command\\Redis\\Container\\Search\\FTCONFIG' => $vendorDir . '/predis/predis/src/Command/Redis/Container/Search/FTCONFIG.php',
    'Predis\\Command\\Redis\\Container\\Search\\FTCURSOR' => $vendorDir . '/predis/predis/src/Command/Redis/Container/Search/FTCURSOR.php',
    'Predis\\Command\\Redis\\CountMinSketch\\CMSINCRBY' => $vendorDir . '/predis/predis/src/Command/Redis/CountMinSketch/CMSINCRBY.php',
    'Predis\\Command\\Redis\\CountMinSketch\\CMSINFO' => $vendorDir . '/predis/predis/src/Command/Redis/CountMinSketch/CMSINFO.php',
    'Predis\\Command\\Redis\\CountMinSketch\\CMSINITBYDIM' => $vendorDir . '/predis/predis/src/Command/Redis/CountMinSketch/CMSINITBYDIM.php',
    'Predis\\Command\\Redis\\CountMinSketch\\CMSINITBYPROB' => $vendorDir . '/predis/predis/src/Command/Redis/CountMinSketch/CMSINITBYPROB.php',
    'Predis\\Command\\Redis\\CountMinSketch\\CMSMERGE' => $vendorDir . '/predis/predis/src/Command/Redis/CountMinSketch/CMSMERGE.php',
    'Predis\\Command\\Redis\\CountMinSketch\\CMSQUERY' => $vendorDir . '/predis/predis/src/Command/Redis/CountMinSketch/CMSQUERY.php',
    'Predis\\Command\\Redis\\CuckooFilter\\CFADD' => $vendorDir . '/predis/predis/src/Command/Redis/CuckooFilter/CFADD.php',
    'Predis\\Command\\Redis\\CuckooFilter\\CFADDNX' => $vendorDir . '/predis/predis/src/Command/Redis/CuckooFilter/CFADDNX.php',
    'Predis\\Command\\Redis\\CuckooFilter\\CFCOUNT' => $vendorDir . '/predis/predis/src/Command/Redis/CuckooFilter/CFCOUNT.php',
    'Predis\\Command\\Redis\\CuckooFilter\\CFDEL' => $vendorDir . '/predis/predis/src/Command/Redis/CuckooFilter/CFDEL.php',
    'Predis\\Command\\Redis\\CuckooFilter\\CFEXISTS' => $vendorDir . '/predis/predis/src/Command/Redis/CuckooFilter/CFEXISTS.php',
    'Predis\\Command\\Redis\\CuckooFilter\\CFINFO' => $vendorDir . '/predis/predis/src/Command/Redis/CuckooFilter/CFINFO.php',
    'Predis\\Command\\Redis\\CuckooFilter\\CFINSERT' => $vendorDir . '/predis/predis/src/Command/Redis/CuckooFilter/CFINSERT.php',
    'Predis\\Command\\Redis\\CuckooFilter\\CFINSERTNX' => $vendorDir . '/predis/predis/src/Command/Redis/CuckooFilter/CFINSERTNX.php',
    'Predis\\Command\\Redis\\CuckooFilter\\CFLOADCHUNK' => $vendorDir . '/predis/predis/src/Command/Redis/CuckooFilter/CFLOADCHUNK.php',
    'Predis\\Command\\Redis\\CuckooFilter\\CFMEXISTS' => $vendorDir . '/predis/predis/src/Command/Redis/CuckooFilter/CFMEXISTS.php',
    'Predis\\Command\\Redis\\CuckooFilter\\CFRESERVE' => $vendorDir . '/predis/predis/src/Command/Redis/CuckooFilter/CFRESERVE.php',
    'Predis\\Command\\Redis\\CuckooFilter\\CFSCANDUMP' => $vendorDir . '/predis/predis/src/Command/Redis/CuckooFilter/CFSCANDUMP.php',
    'Predis\\Command\\Redis\\DBSIZE' => $vendorDir . '/predis/predis/src/Command/Redis/DBSIZE.php',
    'Predis\\Command\\Redis\\DECR' => $vendorDir . '/predis/predis/src/Command/Redis/DECR.php',
    'Predis\\Command\\Redis\\DECRBY' => $vendorDir . '/predis/predis/src/Command/Redis/DECRBY.php',
    'Predis\\Command\\Redis\\DEL' => $vendorDir . '/predis/predis/src/Command/Redis/DEL.php',
    'Predis\\Command\\Redis\\DISCARD' => $vendorDir . '/predis/predis/src/Command/Redis/DISCARD.php',
    'Predis\\Command\\Redis\\DUMP' => $vendorDir . '/predis/predis/src/Command/Redis/DUMP.php',
    'Predis\\Command\\Redis\\ECHO_' => $vendorDir . '/predis/predis/src/Command/Redis/ECHO_.php',
    'Predis\\Command\\Redis\\EVALSHA' => $vendorDir . '/predis/predis/src/Command/Redis/EVALSHA.php',
    'Predis\\Command\\Redis\\EVALSHA_RO' => $vendorDir . '/predis/predis/src/Command/Redis/EVALSHA_RO.php',
    'Predis\\Command\\Redis\\EVAL_' => $vendorDir . '/predis/predis/src/Command/Redis/EVAL_.php',
    'Predis\\Command\\Redis\\EVAL_RO' => $vendorDir . '/predis/predis/src/Command/Redis/EVAL_RO.php',
    'Predis\\Command\\Redis\\EXEC' => $vendorDir . '/predis/predis/src/Command/Redis/EXEC.php',
    'Predis\\Command\\Redis\\EXISTS' => $vendorDir . '/predis/predis/src/Command/Redis/EXISTS.php',
    'Predis\\Command\\Redis\\EXPIRE' => $vendorDir . '/predis/predis/src/Command/Redis/EXPIRE.php',
    'Predis\\Command\\Redis\\EXPIREAT' => $vendorDir . '/predis/predis/src/Command/Redis/EXPIREAT.php',
    'Predis\\Command\\Redis\\EXPIRETIME' => $vendorDir . '/predis/predis/src/Command/Redis/EXPIRETIME.php',
    'Predis\\Command\\Redis\\FAILOVER' => $vendorDir . '/predis/predis/src/Command/Redis/FAILOVER.php',
    'Predis\\Command\\Redis\\FCALL' => $vendorDir . '/predis/predis/src/Command/Redis/FCALL.php',
    'Predis\\Command\\Redis\\FCALL_RO' => $vendorDir . '/predis/predis/src/Command/Redis/FCALL_RO.php',
    'Predis\\Command\\Redis\\FLUSHALL' => $vendorDir . '/predis/predis/src/Command/Redis/FLUSHALL.php',
    'Predis\\Command\\Redis\\FLUSHDB' => $vendorDir . '/predis/predis/src/Command/Redis/FLUSHDB.php',
    'Predis\\Command\\Redis\\FUNCTIONS' => $vendorDir . '/predis/predis/src/Command/Redis/FUNCTIONS.php',
    'Predis\\Command\\Redis\\GEOADD' => $vendorDir . '/predis/predis/src/Command/Redis/GEOADD.php',
    'Predis\\Command\\Redis\\GEODIST' => $vendorDir . '/predis/predis/src/Command/Redis/GEODIST.php',
    'Predis\\Command\\Redis\\GEOHASH' => $vendorDir . '/predis/predis/src/Command/Redis/GEOHASH.php',
    'Predis\\Command\\Redis\\GEOPOS' => $vendorDir . '/predis/predis/src/Command/Redis/GEOPOS.php',
    'Predis\\Command\\Redis\\GEORADIUS' => $vendorDir . '/predis/predis/src/Command/Redis/GEORADIUS.php',
    'Predis\\Command\\Redis\\GEORADIUSBYMEMBER' => $vendorDir . '/predis/predis/src/Command/Redis/GEORADIUSBYMEMBER.php',
    'Predis\\Command\\Redis\\GEOSEARCH' => $vendorDir . '/predis/predis/src/Command/Redis/GEOSEARCH.php',
    'Predis\\Command\\Redis\\GEOSEARCHSTORE' => $vendorDir . '/predis/predis/src/Command/Redis/GEOSEARCHSTORE.php',
    'Predis\\Command\\Redis\\GET' => $vendorDir . '/predis/predis/src/Command/Redis/GET.php',
    'Predis\\Command\\Redis\\GETBIT' => $vendorDir . '/predis/predis/src/Command/Redis/GETBIT.php',
    'Predis\\Command\\Redis\\GETDEL' => $vendorDir . '/predis/predis/src/Command/Redis/GETDEL.php',
    'Predis\\Command\\Redis\\GETEX' => $vendorDir . '/predis/predis/src/Command/Redis/GETEX.php',
    'Predis\\Command\\Redis\\GETRANGE' => $vendorDir . '/predis/predis/src/Command/Redis/GETRANGE.php',
    'Predis\\Command\\Redis\\GETSET' => $vendorDir . '/predis/predis/src/Command/Redis/GETSET.php',
    'Predis\\Command\\Redis\\HDEL' => $vendorDir . '/predis/predis/src/Command/Redis/HDEL.php',
    'Predis\\Command\\Redis\\HEXISTS' => $vendorDir . '/predis/predis/src/Command/Redis/HEXISTS.php',
    'Predis\\Command\\Redis\\HEXPIRE' => $vendorDir . '/predis/predis/src/Command/Redis/HEXPIRE.php',
    'Predis\\Command\\Redis\\HEXPIREAT' => $vendorDir . '/predis/predis/src/Command/Redis/HEXPIREAT.php',
    'Predis\\Command\\Redis\\HEXPIRETIME' => $vendorDir . '/predis/predis/src/Command/Redis/HEXPIRETIME.php',
    'Predis\\Command\\Redis\\HGET' => $vendorDir . '/predis/predis/src/Command/Redis/HGET.php',
    'Predis\\Command\\Redis\\HGETALL' => $vendorDir . '/predis/predis/src/Command/Redis/HGETALL.php',
    'Predis\\Command\\Redis\\HGETDEL' => $vendorDir . '/predis/predis/src/Command/Redis/HGETDEL.php',
    'Predis\\Command\\Redis\\HGETEX' => $vendorDir . '/predis/predis/src/Command/Redis/HGETEX.php',
    'Predis\\Command\\Redis\\HINCRBY' => $vendorDir . '/predis/predis/src/Command/Redis/HINCRBY.php',
    'Predis\\Command\\Redis\\HINCRBYFLOAT' => $vendorDir . '/predis/predis/src/Command/Redis/HINCRBYFLOAT.php',
    'Predis\\Command\\Redis\\HKEYS' => $vendorDir . '/predis/predis/src/Command/Redis/HKEYS.php',
    'Predis\\Command\\Redis\\HLEN' => $vendorDir . '/predis/predis/src/Command/Redis/HLEN.php',
    'Predis\\Command\\Redis\\HMGET' => $vendorDir . '/predis/predis/src/Command/Redis/HMGET.php',
    'Predis\\Command\\Redis\\HMSET' => $vendorDir . '/predis/predis/src/Command/Redis/HMSET.php',
    'Predis\\Command\\Redis\\HPERSIST' => $vendorDir . '/predis/predis/src/Command/Redis/HPERSIST.php',
    'Predis\\Command\\Redis\\HPEXPIRE' => $vendorDir . '/predis/predis/src/Command/Redis/HPEXPIRE.php',
    'Predis\\Command\\Redis\\HPEXPIREAT' => $vendorDir . '/predis/predis/src/Command/Redis/HPEXPIREAT.php',
    'Predis\\Command\\Redis\\HPEXPIRETIME' => $vendorDir . '/predis/predis/src/Command/Redis/HPEXPIRETIME.php',
    'Predis\\Command\\Redis\\HPTTL' => $vendorDir . '/predis/predis/src/Command/Redis/HPTTL.php',
    'Predis\\Command\\Redis\\HRANDFIELD' => $vendorDir . '/predis/predis/src/Command/Redis/HRANDFIELD.php',
    'Predis\\Command\\Redis\\HSCAN' => $vendorDir . '/predis/predis/src/Command/Redis/HSCAN.php',
    'Predis\\Command\\Redis\\HSET' => $vendorDir . '/predis/predis/src/Command/Redis/HSET.php',
    'Predis\\Command\\Redis\\HSETEX' => $vendorDir . '/predis/predis/src/Command/Redis/HSETEX.php',
    'Predis\\Command\\Redis\\HSETNX' => $vendorDir . '/predis/predis/src/Command/Redis/HSETNX.php',
    'Predis\\Command\\Redis\\HSTRLEN' => $vendorDir . '/predis/predis/src/Command/Redis/HSTRLEN.php',
    'Predis\\Command\\Redis\\HTTL' => $vendorDir . '/predis/predis/src/Command/Redis/HTTL.php',
    'Predis\\Command\\Redis\\HVALS' => $vendorDir . '/predis/predis/src/Command/Redis/HVALS.php',
    'Predis\\Command\\Redis\\INCR' => $vendorDir . '/predis/predis/src/Command/Redis/INCR.php',
    'Predis\\Command\\Redis\\INCRBY' => $vendorDir . '/predis/predis/src/Command/Redis/INCRBY.php',
    'Predis\\Command\\Redis\\INCRBYFLOAT' => $vendorDir . '/predis/predis/src/Command/Redis/INCRBYFLOAT.php',
    'Predis\\Command\\Redis\\INFO' => $vendorDir . '/predis/predis/src/Command/Redis/INFO.php',
    'Predis\\Command\\Redis\\Json\\JSONARRAPPEND' => $vendorDir . '/predis/predis/src/Command/Redis/Json/JSONARRAPPEND.php',
    'Predis\\Command\\Redis\\Json\\JSONARRINDEX' => $vendorDir . '/predis/predis/src/Command/Redis/Json/JSONARRINDEX.php',
    'Predis\\Command\\Redis\\Json\\JSONARRINSERT' => $vendorDir . '/predis/predis/src/Command/Redis/Json/JSONARRINSERT.php',
    'Predis\\Command\\Redis\\Json\\JSONARRLEN' => $vendorDir . '/predis/predis/src/Command/Redis/Json/JSONARRLEN.php',
    'Predis\\Command\\Redis\\Json\\JSONARRPOP' => $vendorDir . '/predis/predis/src/Command/Redis/Json/JSONARRPOP.php',
    'Predis\\Command\\Redis\\Json\\JSONARRTRIM' => $vendorDir . '/predis/predis/src/Command/Redis/Json/JSONARRTRIM.php',
    'Predis\\Command\\Redis\\Json\\JSONCLEAR' => $vendorDir . '/predis/predis/src/Command/Redis/Json/JSONCLEAR.php',
    'Predis\\Command\\Redis\\Json\\JSONDEBUG' => $vendorDir . '/predis/predis/src/Command/Redis/Json/JSONDEBUG.php',
    'Predis\\Command\\Redis\\Json\\JSONDEL' => $vendorDir . '/predis/predis/src/Command/Redis/Json/JSONDEL.php',
    'Predis\\Command\\Redis\\Json\\JSONFORGET' => $vendorDir . '/predis/predis/src/Command/Redis/Json/JSONFORGET.php',
    'Predis\\Command\\Redis\\Json\\JSONGET' => $vendorDir . '/predis/predis/src/Command/Redis/Json/JSONGET.php',
    'Predis\\Command\\Redis\\Json\\JSONMERGE' => $vendorDir . '/predis/predis/src/Command/Redis/Json/JSONMERGE.php',
    'Predis\\Command\\Redis\\Json\\JSONMGET' => $vendorDir . '/predis/predis/src/Command/Redis/Json/JSONMGET.php',
    'Predis\\Command\\Redis\\Json\\JSONMSET' => $vendorDir . '/predis/predis/src/Command/Redis/Json/JSONMSET.php',
    'Predis\\Command\\Redis\\Json\\JSONNUMINCRBY' => $vendorDir . '/predis/predis/src/Command/Redis/Json/JSONNUMINCRBY.php',
    'Predis\\Command\\Redis\\Json\\JSONOBJKEYS' => $vendorDir . '/predis/predis/src/Command/Redis/Json/JSONOBJKEYS.php',
    'Predis\\Command\\Redis\\Json\\JSONOBJLEN' => $vendorDir . '/predis/predis/src/Command/Redis/Json/JSONOBJLEN.php',
    'Predis\\Command\\Redis\\Json\\JSONRESP' => $vendorDir . '/predis/predis/src/Command/Redis/Json/JSONRESP.php',
    'Predis\\Command\\Redis\\Json\\JSONSET' => $vendorDir . '/predis/predis/src/Command/Redis/Json/JSONSET.php',
    'Predis\\Command\\Redis\\Json\\JSONSTRAPPEND' => $vendorDir . '/predis/predis/src/Command/Redis/Json/JSONSTRAPPEND.php',
    'Predis\\Command\\Redis\\Json\\JSONSTRLEN' => $vendorDir . '/predis/predis/src/Command/Redis/Json/JSONSTRLEN.php',
    'Predis\\Command\\Redis\\Json\\JSONTOGGLE' => $vendorDir . '/predis/predis/src/Command/Redis/Json/JSONTOGGLE.php',
    'Predis\\Command\\Redis\\Json\\JSONTYPE' => $vendorDir . '/predis/predis/src/Command/Redis/Json/JSONTYPE.php',
    'Predis\\Command\\Redis\\KEYS' => $vendorDir . '/predis/predis/src/Command/Redis/KEYS.php',
    'Predis\\Command\\Redis\\LASTSAVE' => $vendorDir . '/predis/predis/src/Command/Redis/LASTSAVE.php',
    'Predis\\Command\\Redis\\LCS' => $vendorDir . '/predis/predis/src/Command/Redis/LCS.php',
    'Predis\\Command\\Redis\\LINDEX' => $vendorDir . '/predis/predis/src/Command/Redis/LINDEX.php',
    'Predis\\Command\\Redis\\LINSERT' => $vendorDir . '/predis/predis/src/Command/Redis/LINSERT.php',
    'Predis\\Command\\Redis\\LLEN' => $vendorDir . '/predis/predis/src/Command/Redis/LLEN.php',
    'Predis\\Command\\Redis\\LMOVE' => $vendorDir . '/predis/predis/src/Command/Redis/LMOVE.php',
    'Predis\\Command\\Redis\\LMPOP' => $vendorDir . '/predis/predis/src/Command/Redis/LMPOP.php',
    'Predis\\Command\\Redis\\LPOP' => $vendorDir . '/predis/predis/src/Command/Redis/LPOP.php',
    'Predis\\Command\\Redis\\LPUSH' => $vendorDir . '/predis/predis/src/Command/Redis/LPUSH.php',
    'Predis\\Command\\Redis\\LPUSHX' => $vendorDir . '/predis/predis/src/Command/Redis/LPUSHX.php',
    'Predis\\Command\\Redis\\LRANGE' => $vendorDir . '/predis/predis/src/Command/Redis/LRANGE.php',
    'Predis\\Command\\Redis\\LREM' => $vendorDir . '/predis/predis/src/Command/Redis/LREM.php',
    'Predis\\Command\\Redis\\LSET' => $vendorDir . '/predis/predis/src/Command/Redis/LSET.php',
    'Predis\\Command\\Redis\\LTRIM' => $vendorDir . '/predis/predis/src/Command/Redis/LTRIM.php',
    'Predis\\Command\\Redis\\MGET' => $vendorDir . '/predis/predis/src/Command/Redis/MGET.php',
    'Predis\\Command\\Redis\\MIGRATE' => $vendorDir . '/predis/predis/src/Command/Redis/MIGRATE.php',
    'Predis\\Command\\Redis\\MONITOR' => $vendorDir . '/predis/predis/src/Command/Redis/MONITOR.php',
    'Predis\\Command\\Redis\\MOVE' => $vendorDir . '/predis/predis/src/Command/Redis/MOVE.php',
    'Predis\\Command\\Redis\\MSET' => $vendorDir . '/predis/predis/src/Command/Redis/MSET.php',
    'Predis\\Command\\Redis\\MSETNX' => $vendorDir . '/predis/predis/src/Command/Redis/MSETNX.php',
    'Predis\\Command\\Redis\\MULTI' => $vendorDir . '/predis/predis/src/Command/Redis/MULTI.php',
    'Predis\\Command\\Redis\\OBJECT_' => $vendorDir . '/predis/predis/src/Command/Redis/OBJECT_.php',
    'Predis\\Command\\Redis\\PERSIST' => $vendorDir . '/predis/predis/src/Command/Redis/PERSIST.php',
    'Predis\\Command\\Redis\\PEXPIRE' => $vendorDir . '/predis/predis/src/Command/Redis/PEXPIRE.php',
    'Predis\\Command\\Redis\\PEXPIREAT' => $vendorDir . '/predis/predis/src/Command/Redis/PEXPIREAT.php',
    'Predis\\Command\\Redis\\PEXPIRETIME' => $vendorDir . '/predis/predis/src/Command/Redis/PEXPIRETIME.php',
    'Predis\\Command\\Redis\\PFADD' => $vendorDir . '/predis/predis/src/Command/Redis/PFADD.php',
    'Predis\\Command\\Redis\\PFCOUNT' => $vendorDir . '/predis/predis/src/Command/Redis/PFCOUNT.php',
    'Predis\\Command\\Redis\\PFMERGE' => $vendorDir . '/predis/predis/src/Command/Redis/PFMERGE.php',
    'Predis\\Command\\Redis\\PING' => $vendorDir . '/predis/predis/src/Command/Redis/PING.php',
    'Predis\\Command\\Redis\\PSETEX' => $vendorDir . '/predis/predis/src/Command/Redis/PSETEX.php',
    'Predis\\Command\\Redis\\PSUBSCRIBE' => $vendorDir . '/predis/predis/src/Command/Redis/PSUBSCRIBE.php',
    'Predis\\Command\\Redis\\PTTL' => $vendorDir . '/predis/predis/src/Command/Redis/PTTL.php',
    'Predis\\Command\\Redis\\PUBLISH' => $vendorDir . '/predis/predis/src/Command/Redis/PUBLISH.php',
    'Predis\\Command\\Redis\\PUBSUB' => $vendorDir . '/predis/predis/src/Command/Redis/PUBSUB.php',
    'Predis\\Command\\Redis\\PUNSUBSCRIBE' => $vendorDir . '/predis/predis/src/Command/Redis/PUNSUBSCRIBE.php',
    'Predis\\Command\\Redis\\QUIT' => $vendorDir . '/predis/predis/src/Command/Redis/QUIT.php',
    'Predis\\Command\\Redis\\RANDOMKEY' => $vendorDir . '/predis/predis/src/Command/Redis/RANDOMKEY.php',
    'Predis\\Command\\Redis\\RENAME' => $vendorDir . '/predis/predis/src/Command/Redis/RENAME.php',
    'Predis\\Command\\Redis\\RENAMENX' => $vendorDir . '/predis/predis/src/Command/Redis/RENAMENX.php',
    'Predis\\Command\\Redis\\RESTORE' => $vendorDir . '/predis/predis/src/Command/Redis/RESTORE.php',
    'Predis\\Command\\Redis\\RPOP' => $vendorDir . '/predis/predis/src/Command/Redis/RPOP.php',
    'Predis\\Command\\Redis\\RPOPLPUSH' => $vendorDir . '/predis/predis/src/Command/Redis/RPOPLPUSH.php',
    'Predis\\Command\\Redis\\RPUSH' => $vendorDir . '/predis/predis/src/Command/Redis/RPUSH.php',
    'Predis\\Command\\Redis\\RPUSHX' => $vendorDir . '/predis/predis/src/Command/Redis/RPUSHX.php',
    'Predis\\Command\\Redis\\SADD' => $vendorDir . '/predis/predis/src/Command/Redis/SADD.php',
    'Predis\\Command\\Redis\\SAVE' => $vendorDir . '/predis/predis/src/Command/Redis/SAVE.php',
    'Predis\\Command\\Redis\\SCAN' => $vendorDir . '/predis/predis/src/Command/Redis/SCAN.php',
    'Predis\\Command\\Redis\\SCARD' => $vendorDir . '/predis/predis/src/Command/Redis/SCARD.php',
    'Predis\\Command\\Redis\\SCRIPT' => $vendorDir . '/predis/predis/src/Command/Redis/SCRIPT.php',
    'Predis\\Command\\Redis\\SDIFF' => $vendorDir . '/predis/predis/src/Command/Redis/SDIFF.php',
    'Predis\\Command\\Redis\\SDIFFSTORE' => $vendorDir . '/predis/predis/src/Command/Redis/SDIFFSTORE.php',
    'Predis\\Command\\Redis\\SELECT' => $vendorDir . '/predis/predis/src/Command/Redis/SELECT.php',
    'Predis\\Command\\Redis\\SENTINEL' => $vendorDir . '/predis/predis/src/Command/Redis/SENTINEL.php',
    'Predis\\Command\\Redis\\SET' => $vendorDir . '/predis/predis/src/Command/Redis/SET.php',
    'Predis\\Command\\Redis\\SETBIT' => $vendorDir . '/predis/predis/src/Command/Redis/SETBIT.php',
    'Predis\\Command\\Redis\\SETEX' => $vendorDir . '/predis/predis/src/Command/Redis/SETEX.php',
    'Predis\\Command\\Redis\\SETNX' => $vendorDir . '/predis/predis/src/Command/Redis/SETNX.php',
    'Predis\\Command\\Redis\\SETRANGE' => $vendorDir . '/predis/predis/src/Command/Redis/SETRANGE.php',
    'Predis\\Command\\Redis\\SHUTDOWN' => $vendorDir . '/predis/predis/src/Command/Redis/SHUTDOWN.php',
    'Predis\\Command\\Redis\\SINTER' => $vendorDir . '/predis/predis/src/Command/Redis/SINTER.php',
    'Predis\\Command\\Redis\\SINTERCARD' => $vendorDir . '/predis/predis/src/Command/Redis/SINTERCARD.php',
    'Predis\\Command\\Redis\\SINTERSTORE' => $vendorDir . '/predis/predis/src/Command/Redis/SINTERSTORE.php',
    'Predis\\Command\\Redis\\SISMEMBER' => $vendorDir . '/predis/predis/src/Command/Redis/SISMEMBER.php',
    'Predis\\Command\\Redis\\SLAVEOF' => $vendorDir . '/predis/predis/src/Command/Redis/SLAVEOF.php',
    'Predis\\Command\\Redis\\SLOWLOG' => $vendorDir . '/predis/predis/src/Command/Redis/SLOWLOG.php',
    'Predis\\Command\\Redis\\SMEMBERS' => $vendorDir . '/predis/predis/src/Command/Redis/SMEMBERS.php',
    'Predis\\Command\\Redis\\SMISMEMBER' => $vendorDir . '/predis/predis/src/Command/Redis/SMISMEMBER.php',
    'Predis\\Command\\Redis\\SMOVE' => $vendorDir . '/predis/predis/src/Command/Redis/SMOVE.php',
    'Predis\\Command\\Redis\\SORT' => $vendorDir . '/predis/predis/src/Command/Redis/SORT.php',
    'Predis\\Command\\Redis\\SORT_RO' => $vendorDir . '/predis/predis/src/Command/Redis/SORT_RO.php',
    'Predis\\Command\\Redis\\SPOP' => $vendorDir . '/predis/predis/src/Command/Redis/SPOP.php',
    'Predis\\Command\\Redis\\SRANDMEMBER' => $vendorDir . '/predis/predis/src/Command/Redis/SRANDMEMBER.php',
    'Predis\\Command\\Redis\\SREM' => $vendorDir . '/predis/predis/src/Command/Redis/SREM.php',
    'Predis\\Command\\Redis\\SSCAN' => $vendorDir . '/predis/predis/src/Command/Redis/SSCAN.php',
    'Predis\\Command\\Redis\\STRLEN' => $vendorDir . '/predis/predis/src/Command/Redis/STRLEN.php',
    'Predis\\Command\\Redis\\SUBSCRIBE' => $vendorDir . '/predis/predis/src/Command/Redis/SUBSCRIBE.php',
    'Predis\\Command\\Redis\\SUBSTR' => $vendorDir . '/predis/predis/src/Command/Redis/SUBSTR.php',
    'Predis\\Command\\Redis\\SUNION' => $vendorDir . '/predis/predis/src/Command/Redis/SUNION.php',
    'Predis\\Command\\Redis\\SUNIONSTORE' => $vendorDir . '/predis/predis/src/Command/Redis/SUNIONSTORE.php',
    'Predis\\Command\\Redis\\Search\\FTAGGREGATE' => $vendorDir . '/predis/predis/src/Command/Redis/Search/FTAGGREGATE.php',
    'Predis\\Command\\Redis\\Search\\FTALIASADD' => $vendorDir . '/predis/predis/src/Command/Redis/Search/FTALIASADD.php',
    'Predis\\Command\\Redis\\Search\\FTALIASDEL' => $vendorDir . '/predis/predis/src/Command/Redis/Search/FTALIASDEL.php',
    'Predis\\Command\\Redis\\Search\\FTALIASUPDATE' => $vendorDir . '/predis/predis/src/Command/Redis/Search/FTALIASUPDATE.php',
    'Predis\\Command\\Redis\\Search\\FTALTER' => $vendorDir . '/predis/predis/src/Command/Redis/Search/FTALTER.php',
    'Predis\\Command\\Redis\\Search\\FTCONFIG' => $vendorDir . '/predis/predis/src/Command/Redis/Search/FTCONFIG.php',
    'Predis\\Command\\Redis\\Search\\FTCREATE' => $vendorDir . '/predis/predis/src/Command/Redis/Search/FTCREATE.php',
    'Predis\\Command\\Redis\\Search\\FTCURSOR' => $vendorDir . '/predis/predis/src/Command/Redis/Search/FTCURSOR.php',
    'Predis\\Command\\Redis\\Search\\FTDICTADD' => $vendorDir . '/predis/predis/src/Command/Redis/Search/FTDICTADD.php',
    'Predis\\Command\\Redis\\Search\\FTDICTDEL' => $vendorDir . '/predis/predis/src/Command/Redis/Search/FTDICTDEL.php',
    'Predis\\Command\\Redis\\Search\\FTDICTDUMP' => $vendorDir . '/predis/predis/src/Command/Redis/Search/FTDICTDUMP.php',
    'Predis\\Command\\Redis\\Search\\FTDROPINDEX' => $vendorDir . '/predis/predis/src/Command/Redis/Search/FTDROPINDEX.php',
    'Predis\\Command\\Redis\\Search\\FTEXPLAIN' => $vendorDir . '/predis/predis/src/Command/Redis/Search/FTEXPLAIN.php',
    'Predis\\Command\\Redis\\Search\\FTINFO' => $vendorDir . '/predis/predis/src/Command/Redis/Search/FTINFO.php',
    'Predis\\Command\\Redis\\Search\\FTPROFILE' => $vendorDir . '/predis/predis/src/Command/Redis/Search/FTPROFILE.php',
    'Predis\\Command\\Redis\\Search\\FTSEARCH' => $vendorDir . '/predis/predis/src/Command/Redis/Search/FTSEARCH.php',
    'Predis\\Command\\Redis\\Search\\FTSPELLCHECK' => $vendorDir . '/predis/predis/src/Command/Redis/Search/FTSPELLCHECK.php',
    'Predis\\Command\\Redis\\Search\\FTSUGADD' => $vendorDir . '/predis/predis/src/Command/Redis/Search/FTSUGADD.php',
    'Predis\\Command\\Redis\\Search\\FTSUGDEL' => $vendorDir . '/predis/predis/src/Command/Redis/Search/FTSUGDEL.php',
    'Predis\\Command\\Redis\\Search\\FTSUGGET' => $vendorDir . '/predis/predis/src/Command/Redis/Search/FTSUGGET.php',
    'Predis\\Command\\Redis\\Search\\FTSUGLEN' => $vendorDir . '/predis/predis/src/Command/Redis/Search/FTSUGLEN.php',
    'Predis\\Command\\Redis\\Search\\FTSYNDUMP' => $vendorDir . '/predis/predis/src/Command/Redis/Search/FTSYNDUMP.php',
    'Predis\\Command\\Redis\\Search\\FTSYNUPDATE' => $vendorDir . '/predis/predis/src/Command/Redis/Search/FTSYNUPDATE.php',
    'Predis\\Command\\Redis\\Search\\FTTAGVALS' => $vendorDir . '/predis/predis/src/Command/Redis/Search/FTTAGVALS.php',
    'Predis\\Command\\Redis\\Search\\FT_LIST' => $vendorDir . '/predis/predis/src/Command/Redis/Search/FT_LIST.php',
    'Predis\\Command\\Redis\\TDigest\\TDIGESTADD' => $vendorDir . '/predis/predis/src/Command/Redis/TDigest/TDIGESTADD.php',
    'Predis\\Command\\Redis\\TDigest\\TDIGESTBYRANK' => $vendorDir . '/predis/predis/src/Command/Redis/TDigest/TDIGESTBYRANK.php',
    'Predis\\Command\\Redis\\TDigest\\TDIGESTBYREVRANK' => $vendorDir . '/predis/predis/src/Command/Redis/TDigest/TDIGESTBYREVRANK.php',
    'Predis\\Command\\Redis\\TDigest\\TDIGESTCDF' => $vendorDir . '/predis/predis/src/Command/Redis/TDigest/TDIGESTCDF.php',
    'Predis\\Command\\Redis\\TDigest\\TDIGESTCREATE' => $vendorDir . '/predis/predis/src/Command/Redis/TDigest/TDIGESTCREATE.php',
    'Predis\\Command\\Redis\\TDigest\\TDIGESTINFO' => $vendorDir . '/predis/predis/src/Command/Redis/TDigest/TDIGESTINFO.php',
    'Predis\\Command\\Redis\\TDigest\\TDIGESTMAX' => $vendorDir . '/predis/predis/src/Command/Redis/TDigest/TDIGESTMAX.php',
    'Predis\\Command\\Redis\\TDigest\\TDIGESTMERGE' => $vendorDir . '/predis/predis/src/Command/Redis/TDigest/TDIGESTMERGE.php',
    'Predis\\Command\\Redis\\TDigest\\TDIGESTMIN' => $vendorDir . '/predis/predis/src/Command/Redis/TDigest/TDIGESTMIN.php',
    'Predis\\Command\\Redis\\TDigest\\TDIGESTQUANTILE' => $vendorDir . '/predis/predis/src/Command/Redis/TDigest/TDIGESTQUANTILE.php',
    'Predis\\Command\\Redis\\TDigest\\TDIGESTRANK' => $vendorDir . '/predis/predis/src/Command/Redis/TDigest/TDIGESTRANK.php',
    'Predis\\Command\\Redis\\TDigest\\TDIGESTRESET' => $vendorDir . '/predis/predis/src/Command/Redis/TDigest/TDIGESTRESET.php',
    'Predis\\Command\\Redis\\TDigest\\TDIGESTREVRANK' => $vendorDir . '/predis/predis/src/Command/Redis/TDigest/TDIGESTREVRANK.php',
    'Predis\\Command\\Redis\\TDigest\\TDIGESTTRIMMED_MEAN' => $vendorDir . '/predis/predis/src/Command/Redis/TDigest/TDIGESTTRIMMED_MEAN.php',
    'Predis\\Command\\Redis\\TIME' => $vendorDir . '/predis/predis/src/Command/Redis/TIME.php',
    'Predis\\Command\\Redis\\TOUCH' => $vendorDir . '/predis/predis/src/Command/Redis/TOUCH.php',
    'Predis\\Command\\Redis\\TTL' => $vendorDir . '/predis/predis/src/Command/Redis/TTL.php',
    'Predis\\Command\\Redis\\TYPE' => $vendorDir . '/predis/predis/src/Command/Redis/TYPE.php',
    'Predis\\Command\\Redis\\TimeSeries\\TSADD' => $vendorDir . '/predis/predis/src/Command/Redis/TimeSeries/TSADD.php',
    'Predis\\Command\\Redis\\TimeSeries\\TSALTER' => $vendorDir . '/predis/predis/src/Command/Redis/TimeSeries/TSALTER.php',
    'Predis\\Command\\Redis\\TimeSeries\\TSCREATE' => $vendorDir . '/predis/predis/src/Command/Redis/TimeSeries/TSCREATE.php',
    'Predis\\Command\\Redis\\TimeSeries\\TSCREATERULE' => $vendorDir . '/predis/predis/src/Command/Redis/TimeSeries/TSCREATERULE.php',
    'Predis\\Command\\Redis\\TimeSeries\\TSDECRBY' => $vendorDir . '/predis/predis/src/Command/Redis/TimeSeries/TSDECRBY.php',
    'Predis\\Command\\Redis\\TimeSeries\\TSDEL' => $vendorDir . '/predis/predis/src/Command/Redis/TimeSeries/TSDEL.php',
    'Predis\\Command\\Redis\\TimeSeries\\TSDELETERULE' => $vendorDir . '/predis/predis/src/Command/Redis/TimeSeries/TSDELETERULE.php',
    'Predis\\Command\\Redis\\TimeSeries\\TSGET' => $vendorDir . '/predis/predis/src/Command/Redis/TimeSeries/TSGET.php',
    'Predis\\Command\\Redis\\TimeSeries\\TSINCRBY' => $vendorDir . '/predis/predis/src/Command/Redis/TimeSeries/TSINCRBY.php',
    'Predis\\Command\\Redis\\TimeSeries\\TSINFO' => $vendorDir . '/predis/predis/src/Command/Redis/TimeSeries/TSINFO.php',
    'Predis\\Command\\Redis\\TimeSeries\\TSMADD' => $vendorDir . '/predis/predis/src/Command/Redis/TimeSeries/TSMADD.php',
    'Predis\\Command\\Redis\\TimeSeries\\TSMGET' => $vendorDir . '/predis/predis/src/Command/Redis/TimeSeries/TSMGET.php',
    'Predis\\Command\\Redis\\TimeSeries\\TSMRANGE' => $vendorDir . '/predis/predis/src/Command/Redis/TimeSeries/TSMRANGE.php',
    'Predis\\Command\\Redis\\TimeSeries\\TSMREVRANGE' => $vendorDir . '/predis/predis/src/Command/Redis/TimeSeries/TSMREVRANGE.php',
    'Predis\\Command\\Redis\\TimeSeries\\TSQUERYINDEX' => $vendorDir . '/predis/predis/src/Command/Redis/TimeSeries/TSQUERYINDEX.php',
    'Predis\\Command\\Redis\\TimeSeries\\TSRANGE' => $vendorDir . '/predis/predis/src/Command/Redis/TimeSeries/TSRANGE.php',
    'Predis\\Command\\Redis\\TimeSeries\\TSREVRANGE' => $vendorDir . '/predis/predis/src/Command/Redis/TimeSeries/TSREVRANGE.php',
    'Predis\\Command\\Redis\\TopK\\TOPKADD' => $vendorDir . '/predis/predis/src/Command/Redis/TopK/TOPKADD.php',
    'Predis\\Command\\Redis\\TopK\\TOPKINCRBY' => $vendorDir . '/predis/predis/src/Command/Redis/TopK/TOPKINCRBY.php',
    'Predis\\Command\\Redis\\TopK\\TOPKINFO' => $vendorDir . '/predis/predis/src/Command/Redis/TopK/TOPKINFO.php',
    'Predis\\Command\\Redis\\TopK\\TOPKLIST' => $vendorDir . '/predis/predis/src/Command/Redis/TopK/TOPKLIST.php',
    'Predis\\Command\\Redis\\TopK\\TOPKQUERY' => $vendorDir . '/predis/predis/src/Command/Redis/TopK/TOPKQUERY.php',
    'Predis\\Command\\Redis\\TopK\\TOPKRESERVE' => $vendorDir . '/predis/predis/src/Command/Redis/TopK/TOPKRESERVE.php',
    'Predis\\Command\\Redis\\UNSUBSCRIBE' => $vendorDir . '/predis/predis/src/Command/Redis/UNSUBSCRIBE.php',
    'Predis\\Command\\Redis\\UNWATCH' => $vendorDir . '/predis/predis/src/Command/Redis/UNWATCH.php',
    'Predis\\Command\\Redis\\WAITAOF' => $vendorDir . '/predis/predis/src/Command/Redis/WAITAOF.php',
    'Predis\\Command\\Redis\\WATCH' => $vendorDir . '/predis/predis/src/Command/Redis/WATCH.php',
    'Predis\\Command\\Redis\\XADD' => $vendorDir . '/predis/predis/src/Command/Redis/XADD.php',
    'Predis\\Command\\Redis\\XDEL' => $vendorDir . '/predis/predis/src/Command/Redis/XDEL.php',
    'Predis\\Command\\Redis\\XLEN' => $vendorDir . '/predis/predis/src/Command/Redis/XLEN.php',
    'Predis\\Command\\Redis\\XRANGE' => $vendorDir . '/predis/predis/src/Command/Redis/XRANGE.php',
    'Predis\\Command\\Redis\\XREAD' => $vendorDir . '/predis/predis/src/Command/Redis/XREAD.php',
    'Predis\\Command\\Redis\\XREVRANGE' => $vendorDir . '/predis/predis/src/Command/Redis/XREVRANGE.php',
    'Predis\\Command\\Redis\\XTRIM' => $vendorDir . '/predis/predis/src/Command/Redis/XTRIM.php',
    'Predis\\Command\\Redis\\ZADD' => $vendorDir . '/predis/predis/src/Command/Redis/ZADD.php',
    'Predis\\Command\\Redis\\ZCARD' => $vendorDir . '/predis/predis/src/Command/Redis/ZCARD.php',
    'Predis\\Command\\Redis\\ZCOUNT' => $vendorDir . '/predis/predis/src/Command/Redis/ZCOUNT.php',
    'Predis\\Command\\Redis\\ZDIFF' => $vendorDir . '/predis/predis/src/Command/Redis/ZDIFF.php',
    'Predis\\Command\\Redis\\ZDIFFSTORE' => $vendorDir . '/predis/predis/src/Command/Redis/ZDIFFSTORE.php',
    'Predis\\Command\\Redis\\ZINCRBY' => $vendorDir . '/predis/predis/src/Command/Redis/ZINCRBY.php',
    'Predis\\Command\\Redis\\ZINTER' => $vendorDir . '/predis/predis/src/Command/Redis/ZINTER.php',
    'Predis\\Command\\Redis\\ZINTERCARD' => $vendorDir . '/predis/predis/src/Command/Redis/ZINTERCARD.php',
    'Predis\\Command\\Redis\\ZINTERSTORE' => $vendorDir . '/predis/predis/src/Command/Redis/ZINTERSTORE.php',
    'Predis\\Command\\Redis\\ZLEXCOUNT' => $vendorDir . '/predis/predis/src/Command/Redis/ZLEXCOUNT.php',
    'Predis\\Command\\Redis\\ZMPOP' => $vendorDir . '/predis/predis/src/Command/Redis/ZMPOP.php',
    'Predis\\Command\\Redis\\ZMSCORE' => $vendorDir . '/predis/predis/src/Command/Redis/ZMSCORE.php',
    'Predis\\Command\\Redis\\ZPOPMAX' => $vendorDir . '/predis/predis/src/Command/Redis/ZPOPMAX.php',
    'Predis\\Command\\Redis\\ZPOPMIN' => $vendorDir . '/predis/predis/src/Command/Redis/ZPOPMIN.php',
    'Predis\\Command\\Redis\\ZRANDMEMBER' => $vendorDir . '/predis/predis/src/Command/Redis/ZRANDMEMBER.php',
    'Predis\\Command\\Redis\\ZRANGE' => $vendorDir . '/predis/predis/src/Command/Redis/ZRANGE.php',
    'Predis\\Command\\Redis\\ZRANGEBYLEX' => $vendorDir . '/predis/predis/src/Command/Redis/ZRANGEBYLEX.php',
    'Predis\\Command\\Redis\\ZRANGEBYSCORE' => $vendorDir . '/predis/predis/src/Command/Redis/ZRANGEBYSCORE.php',
    'Predis\\Command\\Redis\\ZRANGESTORE' => $vendorDir . '/predis/predis/src/Command/Redis/ZRANGESTORE.php',
    'Predis\\Command\\Redis\\ZRANK' => $vendorDir . '/predis/predis/src/Command/Redis/ZRANK.php',
    'Predis\\Command\\Redis\\ZREM' => $vendorDir . '/predis/predis/src/Command/Redis/ZREM.php',
    'Predis\\Command\\Redis\\ZREMRANGEBYLEX' => $vendorDir . '/predis/predis/src/Command/Redis/ZREMRANGEBYLEX.php',
    'Predis\\Command\\Redis\\ZREMRANGEBYRANK' => $vendorDir . '/predis/predis/src/Command/Redis/ZREMRANGEBYRANK.php',
    'Predis\\Command\\Redis\\ZREMRANGEBYSCORE' => $vendorDir . '/predis/predis/src/Command/Redis/ZREMRANGEBYSCORE.php',
    'Predis\\Command\\Redis\\ZREVRANGE' => $vendorDir . '/predis/predis/src/Command/Redis/ZREVRANGE.php',
    'Predis\\Command\\Redis\\ZREVRANGEBYLEX' => $vendorDir . '/predis/predis/src/Command/Redis/ZREVRANGEBYLEX.php',
    'Predis\\Command\\Redis\\ZREVRANGEBYSCORE' => $vendorDir . '/predis/predis/src/Command/Redis/ZREVRANGEBYSCORE.php',
    'Predis\\Command\\Redis\\ZREVRANK' => $vendorDir . '/predis/predis/src/Command/Redis/ZREVRANK.php',
    'Predis\\Command\\Redis\\ZSCAN' => $vendorDir . '/predis/predis/src/Command/Redis/ZSCAN.php',
    'Predis\\Command\\Redis\\ZSCORE' => $vendorDir . '/predis/predis/src/Command/Redis/ZSCORE.php',
    'Predis\\Command\\Redis\\ZUNION' => $vendorDir . '/predis/predis/src/Command/Redis/ZUNION.php',
    'Predis\\Command\\Redis\\ZUNIONSTORE' => $vendorDir . '/predis/predis/src/Command/Redis/ZUNIONSTORE.php',
    'Predis\\Command\\ScriptCommand' => $vendorDir . '/predis/predis/src/Command/ScriptCommand.php',
    'Predis\\Command\\Strategy\\ContainerCommands\\Functions\\DeleteStrategy' => $vendorDir . '/predis/predis/src/Command/Strategy/ContainerCommands/Functions/DeleteStrategy.php',
    'Predis\\Command\\Strategy\\ContainerCommands\\Functions\\DumpStrategy' => $vendorDir . '/predis/predis/src/Command/Strategy/ContainerCommands/Functions/DumpStrategy.php',
    'Predis\\Command\\Strategy\\ContainerCommands\\Functions\\FlushStrategy' => $vendorDir . '/predis/predis/src/Command/Strategy/ContainerCommands/Functions/FlushStrategy.php',
    'Predis\\Command\\Strategy\\ContainerCommands\\Functions\\KillStrategy' => $vendorDir . '/predis/predis/src/Command/Strategy/ContainerCommands/Functions/KillStrategy.php',
    'Predis\\Command\\Strategy\\ContainerCommands\\Functions\\ListStrategy' => $vendorDir . '/predis/predis/src/Command/Strategy/ContainerCommands/Functions/ListStrategy.php',
    'Predis\\Command\\Strategy\\ContainerCommands\\Functions\\LoadStrategy' => $vendorDir . '/predis/predis/src/Command/Strategy/ContainerCommands/Functions/LoadStrategy.php',
    'Predis\\Command\\Strategy\\ContainerCommands\\Functions\\RestoreStrategy' => $vendorDir . '/predis/predis/src/Command/Strategy/ContainerCommands/Functions/RestoreStrategy.php',
    'Predis\\Command\\Strategy\\ContainerCommands\\Functions\\StatsStrategy' => $vendorDir . '/predis/predis/src/Command/Strategy/ContainerCommands/Functions/StatsStrategy.php',
    'Predis\\Command\\Strategy\\StrategyResolverInterface' => $vendorDir . '/predis/predis/src/Command/Strategy/StrategyResolverInterface.php',
    'Predis\\Command\\Strategy\\SubcommandStrategyInterface' => $vendorDir . '/predis/predis/src/Command/Strategy/SubcommandStrategyInterface.php',
    'Predis\\Command\\Strategy\\SubcommandStrategyResolver' => $vendorDir . '/predis/predis/src/Command/Strategy/SubcommandStrategyResolver.php',
    'Predis\\Command\\Traits\\Aggregate' => $vendorDir . '/predis/predis/src/Command/Traits/Aggregate.php',
    'Predis\\Command\\Traits\\BitByte' => $vendorDir . '/predis/predis/src/Command/Traits/BitByte.php',
    'Predis\\Command\\Traits\\BloomFilters\\BucketSize' => $vendorDir . '/predis/predis/src/Command/Traits/BloomFilters/BucketSize.php',
    'Predis\\Command\\Traits\\BloomFilters\\Capacity' => $vendorDir . '/predis/predis/src/Command/Traits/BloomFilters/Capacity.php',
    'Predis\\Command\\Traits\\BloomFilters\\Error' => $vendorDir . '/predis/predis/src/Command/Traits/BloomFilters/Error.php',
    'Predis\\Command\\Traits\\BloomFilters\\Expansion' => $vendorDir . '/predis/predis/src/Command/Traits/BloomFilters/Expansion.php',
    'Predis\\Command\\Traits\\BloomFilters\\Items' => $vendorDir . '/predis/predis/src/Command/Traits/BloomFilters/Items.php',
    'Predis\\Command\\Traits\\BloomFilters\\MaxIterations' => $vendorDir . '/predis/predis/src/Command/Traits/BloomFilters/MaxIterations.php',
    'Predis\\Command\\Traits\\BloomFilters\\NoCreate' => $vendorDir . '/predis/predis/src/Command/Traits/BloomFilters/NoCreate.php',
    'Predis\\Command\\Traits\\By\\ByArgument' => $vendorDir . '/predis/predis/src/Command/Traits/By/ByArgument.php',
    'Predis\\Command\\Traits\\By\\ByLexByScore' => $vendorDir . '/predis/predis/src/Command/Traits/By/ByLexByScore.php',
    'Predis\\Command\\Traits\\By\\GeoBy' => $vendorDir . '/predis/predis/src/Command/Traits/By/GeoBy.php',
    'Predis\\Command\\Traits\\Count' => $vendorDir . '/predis/predis/src/Command/Traits/Count.php',
    'Predis\\Command\\Traits\\DB' => $vendorDir . '/predis/predis/src/Command/Traits/DB.php',
    'Predis\\Command\\Traits\\Expire\\ExpireOptions' => $vendorDir . '/predis/predis/src/Command/Traits/Expire/ExpireOptions.php',
    'Predis\\Command\\Traits\\From\\GeoFrom' => $vendorDir . '/predis/predis/src/Command/Traits/From/GeoFrom.php',
    'Predis\\Command\\Traits\\Get\\Get' => $vendorDir . '/predis/predis/src/Command/Traits/Get/Get.php',
    'Predis\\Command\\Traits\\Json\\Indent' => $vendorDir . '/predis/predis/src/Command/Traits/Json/Indent.php',
    'Predis\\Command\\Traits\\Json\\Newline' => $vendorDir . '/predis/predis/src/Command/Traits/Json/Newline.php',
    'Predis\\Command\\Traits\\Json\\NxXxArgument' => $vendorDir . '/predis/predis/src/Command/Traits/Json/NxXxArgument.php',
    'Predis\\Command\\Traits\\Json\\Space' => $vendorDir . '/predis/predis/src/Command/Traits/Json/Space.php',
    'Predis\\Command\\Traits\\Keys' => $vendorDir . '/predis/predis/src/Command/Traits/Keys.php',
    'Predis\\Command\\Traits\\LeftRight' => $vendorDir . '/predis/predis/src/Command/Traits/LeftRight.php',
    'Predis\\Command\\Traits\\Limit\\Limit' => $vendorDir . '/predis/predis/src/Command/Traits/Limit/Limit.php',
    'Predis\\Command\\Traits\\Limit\\LimitObject' => $vendorDir . '/predis/predis/src/Command/Traits/Limit/LimitObject.php',
    'Predis\\Command\\Traits\\MinMaxModifier' => $vendorDir . '/predis/predis/src/Command/Traits/MinMaxModifier.php',
    'Predis\\Command\\Traits\\Replace' => $vendorDir . '/predis/predis/src/Command/Traits/Replace.php',
    'Predis\\Command\\Traits\\Rev' => $vendorDir . '/predis/predis/src/Command/Traits/Rev.php',
    'Predis\\Command\\Traits\\Sorting' => $vendorDir . '/predis/predis/src/Command/Traits/Sorting.php',
    'Predis\\Command\\Traits\\Storedist' => $vendorDir . '/predis/predis/src/Command/Traits/Storedist.php',
    'Predis\\Command\\Traits\\Timeout' => $vendorDir . '/predis/predis/src/Command/Traits/Timeout.php',
    'Predis\\Command\\Traits\\To\\ServerTo' => $vendorDir . '/predis/predis/src/Command/Traits/To/ServerTo.php',
    'Predis\\Command\\Traits\\Weights' => $vendorDir . '/predis/predis/src/Command/Traits/Weights.php',
    'Predis\\Command\\Traits\\With\\WithCoord' => $vendorDir . '/predis/predis/src/Command/Traits/With/WithCoord.php',
    'Predis\\Command\\Traits\\With\\WithDist' => $vendorDir . '/predis/predis/src/Command/Traits/With/WithDist.php',
    'Predis\\Command\\Traits\\With\\WithHash' => $vendorDir . '/predis/predis/src/Command/Traits/With/WithHash.php',
    'Predis\\Command\\Traits\\With\\WithScores' => $vendorDir . '/predis/predis/src/Command/Traits/With/WithScores.php',
    'Predis\\Command\\Traits\\With\\WithValues' => $vendorDir . '/predis/predis/src/Command/Traits/With/WithValues.php',
    'Predis\\CommunicationException' => $vendorDir . '/predis/predis/src/CommunicationException.php',
    'Predis\\Configuration\\OptionInterface' => $vendorDir . '/predis/predis/src/Configuration/OptionInterface.php',
    'Predis\\Configuration\\Option\\Aggregate' => $vendorDir . '/predis/predis/src/Configuration/Option/Aggregate.php',
    'Predis\\Configuration\\Option\\CRC16' => $vendorDir . '/predis/predis/src/Configuration/Option/CRC16.php',
    'Predis\\Configuration\\Option\\Cluster' => $vendorDir . '/predis/predis/src/Configuration/Option/Cluster.php',
    'Predis\\Configuration\\Option\\Commands' => $vendorDir . '/predis/predis/src/Configuration/Option/Commands.php',
    'Predis\\Configuration\\Option\\Connections' => $vendorDir . '/predis/predis/src/Configuration/Option/Connections.php',
    'Predis\\Configuration\\Option\\Exceptions' => $vendorDir . '/predis/predis/src/Configuration/Option/Exceptions.php',
    'Predis\\Configuration\\Option\\Prefix' => $vendorDir . '/predis/predis/src/Configuration/Option/Prefix.php',
    'Predis\\Configuration\\Option\\Replication' => $vendorDir . '/predis/predis/src/Configuration/Option/Replication.php',
    'Predis\\Configuration\\Options' => $vendorDir . '/predis/predis/src/Configuration/Options.php',
    'Predis\\Configuration\\OptionsInterface' => $vendorDir . '/predis/predis/src/Configuration/OptionsInterface.php',
    'Predis\\Connection\\AbstractConnection' => $vendorDir . '/predis/predis/src/Connection/AbstractConnection.php',
    'Predis\\Connection\\AggregateConnectionInterface' => $vendorDir . '/predis/predis/src/Connection/AggregateConnectionInterface.php',
    'Predis\\Connection\\Cluster\\ClusterInterface' => $vendorDir . '/predis/predis/src/Connection/Cluster/ClusterInterface.php',
    'Predis\\Connection\\Cluster\\PredisCluster' => $vendorDir . '/predis/predis/src/Connection/Cluster/PredisCluster.php',
    'Predis\\Connection\\Cluster\\RedisCluster' => $vendorDir . '/predis/predis/src/Connection/Cluster/RedisCluster.php',
    'Predis\\Connection\\CompositeConnectionInterface' => $vendorDir . '/predis/predis/src/Connection/CompositeConnectionInterface.php',
    'Predis\\Connection\\CompositeStreamConnection' => $vendorDir . '/predis/predis/src/Connection/CompositeStreamConnection.php',
    'Predis\\Connection\\ConnectionException' => $vendorDir . '/predis/predis/src/Connection/ConnectionException.php',
    'Predis\\Connection\\ConnectionInterface' => $vendorDir . '/predis/predis/src/Connection/ConnectionInterface.php',
    'Predis\\Connection\\Factory' => $vendorDir . '/predis/predis/src/Connection/Factory.php',
    'Predis\\Connection\\FactoryInterface' => $vendorDir . '/predis/predis/src/Connection/FactoryInterface.php',
    'Predis\\Connection\\NodeConnectionInterface' => $vendorDir . '/predis/predis/src/Connection/NodeConnectionInterface.php',
    'Predis\\Connection\\Parameters' => $vendorDir . '/predis/predis/src/Connection/Parameters.php',
    'Predis\\Connection\\ParametersInterface' => $vendorDir . '/predis/predis/src/Connection/ParametersInterface.php',
    'Predis\\Connection\\PhpiredisSocketConnection' => $vendorDir . '/predis/predis/src/Connection/PhpiredisSocketConnection.php',
    'Predis\\Connection\\PhpiredisStreamConnection' => $vendorDir . '/predis/predis/src/Connection/PhpiredisStreamConnection.php',
    'Predis\\Connection\\RelayConnection' => $vendorDir . '/predis/predis/src/Connection/RelayConnection.php',
    'Predis\\Connection\\RelayMethods' => $vendorDir . '/predis/predis/src/Connection/RelayMethods.php',
    'Predis\\Connection\\Replication\\MasterSlaveReplication' => $vendorDir . '/predis/predis/src/Connection/Replication/MasterSlaveReplication.php',
    'Predis\\Connection\\Replication\\ReplicationInterface' => $vendorDir . '/predis/predis/src/Connection/Replication/ReplicationInterface.php',
    'Predis\\Connection\\Replication\\SentinelReplication' => $vendorDir . '/predis/predis/src/Connection/Replication/SentinelReplication.php',
    'Predis\\Connection\\StreamConnection' => $vendorDir . '/predis/predis/src/Connection/StreamConnection.php',
    'Predis\\Connection\\WebdisConnection' => $vendorDir . '/predis/predis/src/Connection/WebdisConnection.php',
    'Predis\\Monitor\\Consumer' => $vendorDir . '/predis/predis/src/Monitor/Consumer.php',
    'Predis\\NotSupportedException' => $vendorDir . '/predis/predis/src/NotSupportedException.php',
    'Predis\\Pipeline\\Atomic' => $vendorDir . '/predis/predis/src/Pipeline/Atomic.php',
    'Predis\\Pipeline\\ConnectionErrorProof' => $vendorDir . '/predis/predis/src/Pipeline/ConnectionErrorProof.php',
    'Predis\\Pipeline\\FireAndForget' => $vendorDir . '/predis/predis/src/Pipeline/FireAndForget.php',
    'Predis\\Pipeline\\Pipeline' => $vendorDir . '/predis/predis/src/Pipeline/Pipeline.php',
    'Predis\\Pipeline\\RelayAtomic' => $vendorDir . '/predis/predis/src/Pipeline/RelayAtomic.php',
    'Predis\\Pipeline\\RelayPipeline' => $vendorDir . '/predis/predis/src/Pipeline/RelayPipeline.php',
    'Predis\\PredisException' => $vendorDir . '/predis/predis/src/PredisException.php',
    'Predis\\Protocol\\ProtocolException' => $vendorDir . '/predis/predis/src/Protocol/ProtocolException.php',
    'Predis\\Protocol\\ProtocolProcessorInterface' => $vendorDir . '/predis/predis/src/Protocol/ProtocolProcessorInterface.php',
    'Predis\\Protocol\\RequestSerializerInterface' => $vendorDir . '/predis/predis/src/Protocol/RequestSerializerInterface.php',
    'Predis\\Protocol\\ResponseReaderInterface' => $vendorDir . '/predis/predis/src/Protocol/ResponseReaderInterface.php',
    'Predis\\Protocol\\Text\\CompositeProtocolProcessor' => $vendorDir . '/predis/predis/src/Protocol/Text/CompositeProtocolProcessor.php',
    'Predis\\Protocol\\Text\\Handler\\BulkResponse' => $vendorDir . '/predis/predis/src/Protocol/Text/Handler/BulkResponse.php',
    'Predis\\Protocol\\Text\\Handler\\ErrorResponse' => $vendorDir . '/predis/predis/src/Protocol/Text/Handler/ErrorResponse.php',
    'Predis\\Protocol\\Text\\Handler\\IntegerResponse' => $vendorDir . '/predis/predis/src/Protocol/Text/Handler/IntegerResponse.php',
    'Predis\\Protocol\\Text\\Handler\\MultiBulkResponse' => $vendorDir . '/predis/predis/src/Protocol/Text/Handler/MultiBulkResponse.php',
    'Predis\\Protocol\\Text\\Handler\\ResponseHandlerInterface' => $vendorDir . '/predis/predis/src/Protocol/Text/Handler/ResponseHandlerInterface.php',
    'Predis\\Protocol\\Text\\Handler\\StatusResponse' => $vendorDir . '/predis/predis/src/Protocol/Text/Handler/StatusResponse.php',
    'Predis\\Protocol\\Text\\Handler\\StreamableMultiBulkResponse' => $vendorDir . '/predis/predis/src/Protocol/Text/Handler/StreamableMultiBulkResponse.php',
    'Predis\\Protocol\\Text\\ProtocolProcessor' => $vendorDir . '/predis/predis/src/Protocol/Text/ProtocolProcessor.php',
    'Predis\\Protocol\\Text\\RequestSerializer' => $vendorDir . '/predis/predis/src/Protocol/Text/RequestSerializer.php',
    'Predis\\Protocol\\Text\\ResponseReader' => $vendorDir . '/predis/predis/src/Protocol/Text/ResponseReader.php',
    'Predis\\PubSub\\AbstractConsumer' => $vendorDir . '/predis/predis/src/PubSub/AbstractConsumer.php',
    'Predis\\PubSub\\Consumer' => $vendorDir . '/predis/predis/src/PubSub/Consumer.php',
    'Predis\\PubSub\\DispatcherLoop' => $vendorDir . '/predis/predis/src/PubSub/DispatcherLoop.php',
    'Predis\\PubSub\\RelayConsumer' => $vendorDir . '/predis/predis/src/PubSub/RelayConsumer.php',
    'Predis\\Replication\\MissingMasterException' => $vendorDir . '/predis/predis/src/Replication/MissingMasterException.php',
    'Predis\\Replication\\ReplicationStrategy' => $vendorDir . '/predis/predis/src/Replication/ReplicationStrategy.php',
    'Predis\\Replication\\RoleException' => $vendorDir . '/predis/predis/src/Replication/RoleException.php',
    'Predis\\Response\\Error' => $vendorDir . '/predis/predis/src/Response/Error.php',
    'Predis\\Response\\ErrorInterface' => $vendorDir . '/predis/predis/src/Response/ErrorInterface.php',
    'Predis\\Response\\Iterator\\MultiBulk' => $vendorDir . '/predis/predis/src/Response/Iterator/MultiBulk.php',
    'Predis\\Response\\Iterator\\MultiBulkIterator' => $vendorDir . '/predis/predis/src/Response/Iterator/MultiBulkIterator.php',
    'Predis\\Response\\Iterator\\MultiBulkTuple' => $vendorDir . '/predis/predis/src/Response/Iterator/MultiBulkTuple.php',
    'Predis\\Response\\ResponseInterface' => $vendorDir . '/predis/predis/src/Response/ResponseInterface.php',
    'Predis\\Response\\ServerException' => $vendorDir . '/predis/predis/src/Response/ServerException.php',
    'Predis\\Response\\Status' => $vendorDir . '/predis/predis/src/Response/Status.php',
    'Predis\\Session\\Handler' => $vendorDir . '/predis/predis/src/Session/Handler.php',
    'Predis\\Transaction\\AbortedMultiExecException' => $vendorDir . '/predis/predis/src/Transaction/AbortedMultiExecException.php',
    'Predis\\Transaction\\MultiExec' => $vendorDir . '/predis/predis/src/Transaction/MultiExec.php',
    'Predis\\Transaction\\MultiExecState' => $vendorDir . '/predis/predis/src/Transaction/MultiExecState.php',
    'Psr\\Container\\ContainerExceptionInterface' => $vendorDir . '/psr/container/src/ContainerExceptionInterface.php',
    'Psr\\Container\\ContainerInterface' => $vendorDir . '/psr/container/src/ContainerInterface.php',
    'Psr\\Container\\NotFoundExceptionInterface' => $vendorDir . '/psr/container/src/NotFoundExceptionInterface.php',
    'Psr\\EventDispatcher\\EventDispatcherInterface' => $vendorDir . '/psr/event-dispatcher/src/EventDispatcherInterface.php',
    'Psr\\EventDispatcher\\ListenerProviderInterface' => $vendorDir . '/psr/event-dispatcher/src/ListenerProviderInterface.php',
    'Psr\\EventDispatcher\\StoppableEventInterface' => $vendorDir . '/psr/event-dispatcher/src/StoppableEventInterface.php',
    'Psr\\Log\\AbstractLogger' => $vendorDir . '/psr/log/src/AbstractLogger.php',
    'Psr\\Log\\InvalidArgumentException' => $vendorDir . '/psr/log/src/InvalidArgumentException.php',
    'Psr\\Log\\LogLevel' => $vendorDir . '/psr/log/src/LogLevel.php',
    'Psr\\Log\\LoggerAwareInterface' => $vendorDir . '/psr/log/src/LoggerAwareInterface.php',
    'Psr\\Log\\LoggerAwareTrait' => $vendorDir . '/psr/log/src/LoggerAwareTrait.php',
    'Psr\\Log\\LoggerInterface' => $vendorDir . '/psr/log/src/LoggerInterface.php',
    'Psr\\Log\\LoggerTrait' => $vendorDir . '/psr/log/src/LoggerTrait.php',
    'Psr\\Log\\NullLogger' => $vendorDir . '/psr/log/src/NullLogger.php',
    'React\\Cache\\ArrayCache' => $vendorDir . '/react/cache/src/ArrayCache.php',
    'React\\Cache\\CacheInterface' => $vendorDir . '/react/cache/src/CacheInterface.php',
    'React\\ChildProcess\\Process' => $vendorDir . '/react/child-process/src/Process.php',
    'React\\Dns\\BadServerException' => $vendorDir . '/react/dns/src/BadServerException.php',
    'React\\Dns\\Config\\Config' => $vendorDir . '/react/dns/src/Config/Config.php',
    'React\\Dns\\Config\\HostsFile' => $vendorDir . '/react/dns/src/Config/HostsFile.php',
    'React\\Dns\\Model\\Message' => $vendorDir . '/react/dns/src/Model/Message.php',
    'React\\Dns\\Model\\Record' => $vendorDir . '/react/dns/src/Model/Record.php',
    'React\\Dns\\Protocol\\BinaryDumper' => $vendorDir . '/react/dns/src/Protocol/BinaryDumper.php',
    'React\\Dns\\Protocol\\Parser' => $vendorDir . '/react/dns/src/Protocol/Parser.php',
    'React\\Dns\\Query\\CachingExecutor' => $vendorDir . '/react/dns/src/Query/CachingExecutor.php',
    'React\\Dns\\Query\\CancellationException' => $vendorDir . '/react/dns/src/Query/CancellationException.php',
    'React\\Dns\\Query\\CoopExecutor' => $vendorDir . '/react/dns/src/Query/CoopExecutor.php',
    'React\\Dns\\Query\\ExecutorInterface' => $vendorDir . '/react/dns/src/Query/ExecutorInterface.php',
    'React\\Dns\\Query\\FallbackExecutor' => $vendorDir . '/react/dns/src/Query/FallbackExecutor.php',
    'React\\Dns\\Query\\HostsFileExecutor' => $vendorDir . '/react/dns/src/Query/HostsFileExecutor.php',
    'React\\Dns\\Query\\Query' => $vendorDir . '/react/dns/src/Query/Query.php',
    'React\\Dns\\Query\\RetryExecutor' => $vendorDir . '/react/dns/src/Query/RetryExecutor.php',
    'React\\Dns\\Query\\SelectiveTransportExecutor' => $vendorDir . '/react/dns/src/Query/SelectiveTransportExecutor.php',
    'React\\Dns\\Query\\TcpTransportExecutor' => $vendorDir . '/react/dns/src/Query/TcpTransportExecutor.php',
    'React\\Dns\\Query\\TimeoutException' => $vendorDir . '/react/dns/src/Query/TimeoutException.php',
    'React\\Dns\\Query\\TimeoutExecutor' => $vendorDir . '/react/dns/src/Query/TimeoutExecutor.php',
    'React\\Dns\\Query\\UdpTransportExecutor' => $vendorDir . '/react/dns/src/Query/UdpTransportExecutor.php',
    'React\\Dns\\RecordNotFoundException' => $vendorDir . '/react/dns/src/RecordNotFoundException.php',
    'React\\Dns\\Resolver\\Factory' => $vendorDir . '/react/dns/src/Resolver/Factory.php',
    'React\\Dns\\Resolver\\Resolver' => $vendorDir . '/react/dns/src/Resolver/Resolver.php',
    'React\\Dns\\Resolver\\ResolverInterface' => $vendorDir . '/react/dns/src/Resolver/ResolverInterface.php',
    'React\\EventLoop\\ExtEvLoop' => $vendorDir . '/react/event-loop/src/ExtEvLoop.php',
    'React\\EventLoop\\ExtEventLoop' => $vendorDir . '/react/event-loop/src/ExtEventLoop.php',
    'React\\EventLoop\\ExtLibevLoop' => $vendorDir . '/react/event-loop/src/ExtLibevLoop.php',
    'React\\EventLoop\\ExtLibeventLoop' => $vendorDir . '/react/event-loop/src/ExtLibeventLoop.php',
    'React\\EventLoop\\ExtUvLoop' => $vendorDir . '/react/event-loop/src/ExtUvLoop.php',
    'React\\EventLoop\\Factory' => $vendorDir . '/react/event-loop/src/Factory.php',
    'React\\EventLoop\\Loop' => $vendorDir . '/react/event-loop/src/Loop.php',
    'React\\EventLoop\\LoopInterface' => $vendorDir . '/react/event-loop/src/LoopInterface.php',
    'React\\EventLoop\\SignalsHandler' => $vendorDir . '/react/event-loop/src/SignalsHandler.php',
    'React\\EventLoop\\StreamSelectLoop' => $vendorDir . '/react/event-loop/src/StreamSelectLoop.php',
    'React\\EventLoop\\Tick\\FutureTickQueue' => $vendorDir . '/react/event-loop/src/Tick/FutureTickQueue.php',
    'React\\EventLoop\\TimerInterface' => $vendorDir . '/react/event-loop/src/TimerInterface.php',
    'React\\EventLoop\\Timer\\Timer' => $vendorDir . '/react/event-loop/src/Timer/Timer.php',
    'React\\EventLoop\\Timer\\Timers' => $vendorDir . '/react/event-loop/src/Timer/Timers.php',
    'React\\Promise\\Deferred' => $vendorDir . '/react/promise/src/Deferred.php',
    'React\\Promise\\Exception\\CompositeException' => $vendorDir . '/react/promise/src/Exception/CompositeException.php',
    'React\\Promise\\Exception\\LengthException' => $vendorDir . '/react/promise/src/Exception/LengthException.php',
    'React\\Promise\\Internal\\CancellationQueue' => $vendorDir . '/react/promise/src/Internal/CancellationQueue.php',
    'React\\Promise\\Internal\\FulfilledPromise' => $vendorDir . '/react/promise/src/Internal/FulfilledPromise.php',
    'React\\Promise\\Internal\\RejectedPromise' => $vendorDir . '/react/promise/src/Internal/RejectedPromise.php',
    'React\\Promise\\Promise' => $vendorDir . '/react/promise/src/Promise.php',
    'React\\Promise\\PromiseInterface' => $vendorDir . '/react/promise/src/PromiseInterface.php',
    'React\\Socket\\Connection' => $vendorDir . '/react/socket/src/Connection.php',
    'React\\Socket\\ConnectionInterface' => $vendorDir . '/react/socket/src/ConnectionInterface.php',
    'React\\Socket\\Connector' => $vendorDir . '/react/socket/src/Connector.php',
    'React\\Socket\\ConnectorInterface' => $vendorDir . '/react/socket/src/ConnectorInterface.php',
    'React\\Socket\\DnsConnector' => $vendorDir . '/react/socket/src/DnsConnector.php',
    'React\\Socket\\FdServer' => $vendorDir . '/react/socket/src/FdServer.php',
    'React\\Socket\\FixedUriConnector' => $vendorDir . '/react/socket/src/FixedUriConnector.php',
    'React\\Socket\\HappyEyeBallsConnectionBuilder' => $vendorDir . '/react/socket/src/HappyEyeBallsConnectionBuilder.php',
    'React\\Socket\\HappyEyeBallsConnector' => $vendorDir . '/react/socket/src/HappyEyeBallsConnector.php',
    'React\\Socket\\LimitingServer' => $vendorDir . '/react/socket/src/LimitingServer.php',
    'React\\Socket\\SecureConnector' => $vendorDir . '/react/socket/src/SecureConnector.php',
    'React\\Socket\\SecureServer' => $vendorDir . '/react/socket/src/SecureServer.php',
    'React\\Socket\\Server' => $vendorDir . '/react/socket/src/Server.php',
    'React\\Socket\\ServerInterface' => $vendorDir . '/react/socket/src/ServerInterface.php',
    'React\\Socket\\SocketServer' => $vendorDir . '/react/socket/src/SocketServer.php',
    'React\\Socket\\StreamEncryption' => $vendorDir . '/react/socket/src/StreamEncryption.php',
    'React\\Socket\\TcpConnector' => $vendorDir . '/react/socket/src/TcpConnector.php',
    'React\\Socket\\TcpServer' => $vendorDir . '/react/socket/src/TcpServer.php',
    'React\\Socket\\TimeoutConnector' => $vendorDir . '/react/socket/src/TimeoutConnector.php',
    'React\\Socket\\UnixConnector' => $vendorDir . '/react/socket/src/UnixConnector.php',
    'React\\Socket\\UnixServer' => $vendorDir . '/react/socket/src/UnixServer.php',
    'React\\Stream\\CompositeStream' => $vendorDir . '/react/stream/src/CompositeStream.php',
    'React\\Stream\\DuplexResourceStream' => $vendorDir . '/react/stream/src/DuplexResourceStream.php',
    'React\\Stream\\DuplexStreamInterface' => $vendorDir . '/react/stream/src/DuplexStreamInterface.php',
    'React\\Stream\\ReadableResourceStream' => $vendorDir . '/react/stream/src/ReadableResourceStream.php',
    'React\\Stream\\ReadableStreamInterface' => $vendorDir . '/react/stream/src/ReadableStreamInterface.php',
    'React\\Stream\\ThroughStream' => $vendorDir . '/react/stream/src/ThroughStream.php',
    'React\\Stream\\Util' => $vendorDir . '/react/stream/src/Util.php',
    'React\\Stream\\WritableResourceStream' => $vendorDir . '/react/stream/src/WritableResourceStream.php',
    'React\\Stream\\WritableStreamInterface' => $vendorDir . '/react/stream/src/WritableStreamInterface.php',
    'ReturnTypeWillChange' => $vendorDir . '/symfony/polyfill-php81/Resources/stubs/ReturnTypeWillChange.php',
    'SebastianBergmann\\CliParser\\AmbiguousOptionException' => $vendorDir . '/sebastian/cli-parser/src/exceptions/AmbiguousOptionException.php',
    'SebastianBergmann\\CliParser\\Exception' => $vendorDir . '/sebastian/cli-parser/src/exceptions/Exception.php',
    'SebastianBergmann\\CliParser\\OptionDoesNotAllowArgumentException' => $vendorDir . '/sebastian/cli-parser/src/exceptions/OptionDoesNotAllowArgumentException.php',
    'SebastianBergmann\\CliParser\\Parser' => $vendorDir . '/sebastian/cli-parser/src/Parser.php',
    'SebastianBergmann\\CliParser\\RequiredOptionArgumentMissingException' => $vendorDir . '/sebastian/cli-parser/src/exceptions/RequiredOptionArgumentMissingException.php',
    'SebastianBergmann\\CliParser\\UnknownOptionException' => $vendorDir . '/sebastian/cli-parser/src/exceptions/UnknownOptionException.php',
    'SebastianBergmann\\CodeCoverage\\BranchAndPathCoverageNotSupportedException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/BranchAndPathCoverageNotSupportedException.php',
    'SebastianBergmann\\CodeCoverage\\CodeCoverage' => $vendorDir . '/phpunit/php-code-coverage/src/CodeCoverage.php',
    'SebastianBergmann\\CodeCoverage\\Data\\ProcessedCodeCoverageData' => $vendorDir . '/phpunit/php-code-coverage/src/Data/ProcessedCodeCoverageData.php',
    'SebastianBergmann\\CodeCoverage\\Data\\RawCodeCoverageData' => $vendorDir . '/phpunit/php-code-coverage/src/Data/RawCodeCoverageData.php',
    'SebastianBergmann\\CodeCoverage\\DeadCodeDetectionNotSupportedException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/DeadCodeDetectionNotSupportedException.php',
    'SebastianBergmann\\CodeCoverage\\Driver\\Driver' => $vendorDir . '/phpunit/php-code-coverage/src/Driver/Driver.php',
    'SebastianBergmann\\CodeCoverage\\Driver\\PathExistsButIsNotDirectoryException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/PathExistsButIsNotDirectoryException.php',
    'SebastianBergmann\\CodeCoverage\\Driver\\PcovDriver' => $vendorDir . '/phpunit/php-code-coverage/src/Driver/PcovDriver.php',
    'SebastianBergmann\\CodeCoverage\\Driver\\PcovNotAvailableException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/PcovNotAvailableException.php',
    'SebastianBergmann\\CodeCoverage\\Driver\\Selector' => $vendorDir . '/phpunit/php-code-coverage/src/Driver/Selector.php',
    'SebastianBergmann\\CodeCoverage\\Driver\\WriteOperationFailedException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/WriteOperationFailedException.php',
    'SebastianBergmann\\CodeCoverage\\Driver\\XdebugDriver' => $vendorDir . '/phpunit/php-code-coverage/src/Driver/XdebugDriver.php',
    'SebastianBergmann\\CodeCoverage\\Driver\\XdebugNotAvailableException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/XdebugNotAvailableException.php',
    'SebastianBergmann\\CodeCoverage\\Driver\\XdebugNotEnabledException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/XdebugNotEnabledException.php',
    'SebastianBergmann\\CodeCoverage\\Exception' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/Exception.php',
    'SebastianBergmann\\CodeCoverage\\FileCouldNotBeWrittenException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/FileCouldNotBeWrittenException.php',
    'SebastianBergmann\\CodeCoverage\\Filter' => $vendorDir . '/phpunit/php-code-coverage/src/Filter.php',
    'SebastianBergmann\\CodeCoverage\\InvalidArgumentException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/InvalidArgumentException.php',
    'SebastianBergmann\\CodeCoverage\\NoCodeCoverageDriverAvailableException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/NoCodeCoverageDriverAvailableException.php',
    'SebastianBergmann\\CodeCoverage\\NoCodeCoverageDriverWithPathCoverageSupportAvailableException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/NoCodeCoverageDriverWithPathCoverageSupportAvailableException.php',
    'SebastianBergmann\\CodeCoverage\\Node\\AbstractNode' => $vendorDir . '/phpunit/php-code-coverage/src/Node/AbstractNode.php',
    'SebastianBergmann\\CodeCoverage\\Node\\Builder' => $vendorDir . '/phpunit/php-code-coverage/src/Node/Builder.php',
    'SebastianBergmann\\CodeCoverage\\Node\\CrapIndex' => $vendorDir . '/phpunit/php-code-coverage/src/Node/CrapIndex.php',
    'SebastianBergmann\\CodeCoverage\\Node\\Directory' => $vendorDir . '/phpunit/php-code-coverage/src/Node/Directory.php',
    'SebastianBergmann\\CodeCoverage\\Node\\File' => $vendorDir . '/phpunit/php-code-coverage/src/Node/File.php',
    'SebastianBergmann\\CodeCoverage\\Node\\Iterator' => $vendorDir . '/phpunit/php-code-coverage/src/Node/Iterator.php',
    'SebastianBergmann\\CodeCoverage\\ParserException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/ParserException.php',
    'SebastianBergmann\\CodeCoverage\\ReflectionException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/ReflectionException.php',
    'SebastianBergmann\\CodeCoverage\\ReportAlreadyFinalizedException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/ReportAlreadyFinalizedException.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Clover' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Clover.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Cobertura' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Cobertura.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Crap4j' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Crap4j.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Html\\Colors' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Html/Colors.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Html\\CustomCssFile' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Html/CustomCssFile.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Html\\Dashboard' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Html/Renderer/Dashboard.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Html\\Directory' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Html/Renderer/Directory.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Html\\Facade' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Html/Facade.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Html\\File' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Html/Renderer/File.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Html\\Renderer' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Html/Renderer.php',
    'SebastianBergmann\\CodeCoverage\\Report\\PHP' => $vendorDir . '/phpunit/php-code-coverage/src/Report/PHP.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Text' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Text.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Thresholds' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Thresholds.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Xml\\BuildInformation' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Xml/BuildInformation.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Xml\\Coverage' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Xml/Coverage.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Xml\\Directory' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Xml/Directory.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Xml\\Facade' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Xml/Facade.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Xml\\File' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Xml/File.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Xml\\Method' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Xml/Method.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Xml\\Node' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Xml/Node.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Xml\\Project' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Xml/Project.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Xml\\Report' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Xml/Report.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Xml\\Source' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Xml/Source.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Xml\\Tests' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Xml/Tests.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Xml\\Totals' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Xml/Totals.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Xml\\Unit' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Xml/Unit.php',
    'SebastianBergmann\\CodeCoverage\\StaticAnalysisCacheNotConfiguredException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/StaticAnalysisCacheNotConfiguredException.php',
    'SebastianBergmann\\CodeCoverage\\StaticAnalysis\\CacheWarmer' => $vendorDir . '/phpunit/php-code-coverage/src/StaticAnalysis/CacheWarmer.php',
    'SebastianBergmann\\CodeCoverage\\StaticAnalysis\\CachingFileAnalyser' => $vendorDir . '/phpunit/php-code-coverage/src/StaticAnalysis/CachingFileAnalyser.php',
    'SebastianBergmann\\CodeCoverage\\StaticAnalysis\\CodeUnitFindingVisitor' => $vendorDir . '/phpunit/php-code-coverage/src/StaticAnalysis/CodeUnitFindingVisitor.php',
    'SebastianBergmann\\CodeCoverage\\StaticAnalysis\\ExecutableLinesFindingVisitor' => $vendorDir . '/phpunit/php-code-coverage/src/StaticAnalysis/ExecutableLinesFindingVisitor.php',
    'SebastianBergmann\\CodeCoverage\\StaticAnalysis\\FileAnalyser' => $vendorDir . '/phpunit/php-code-coverage/src/StaticAnalysis/FileAnalyser.php',
    'SebastianBergmann\\CodeCoverage\\StaticAnalysis\\IgnoredLinesFindingVisitor' => $vendorDir . '/phpunit/php-code-coverage/src/StaticAnalysis/IgnoredLinesFindingVisitor.php',
    'SebastianBergmann\\CodeCoverage\\StaticAnalysis\\ParsingFileAnalyser' => $vendorDir . '/phpunit/php-code-coverage/src/StaticAnalysis/ParsingFileAnalyser.php',
    'SebastianBergmann\\CodeCoverage\\TestIdMissingException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/TestIdMissingException.php',
    'SebastianBergmann\\CodeCoverage\\Test\\TestSize\\Known' => $vendorDir . '/phpunit/php-code-coverage/src/TestSize/Known.php',
    'SebastianBergmann\\CodeCoverage\\Test\\TestSize\\Large' => $vendorDir . '/phpunit/php-code-coverage/src/TestSize/Large.php',
    'SebastianBergmann\\CodeCoverage\\Test\\TestSize\\Medium' => $vendorDir . '/phpunit/php-code-coverage/src/TestSize/Medium.php',
    'SebastianBergmann\\CodeCoverage\\Test\\TestSize\\Small' => $vendorDir . '/phpunit/php-code-coverage/src/TestSize/Small.php',
    'SebastianBergmann\\CodeCoverage\\Test\\TestSize\\TestSize' => $vendorDir . '/phpunit/php-code-coverage/src/TestSize/TestSize.php',
    'SebastianBergmann\\CodeCoverage\\Test\\TestSize\\Unknown' => $vendorDir . '/phpunit/php-code-coverage/src/TestSize/Unknown.php',
    'SebastianBergmann\\CodeCoverage\\Test\\TestStatus\\Failure' => $vendorDir . '/phpunit/php-code-coverage/src/TestStatus/Failure.php',
    'SebastianBergmann\\CodeCoverage\\Test\\TestStatus\\Known' => $vendorDir . '/phpunit/php-code-coverage/src/TestStatus/Known.php',
    'SebastianBergmann\\CodeCoverage\\Test\\TestStatus\\Success' => $vendorDir . '/phpunit/php-code-coverage/src/TestStatus/Success.php',
    'SebastianBergmann\\CodeCoverage\\Test\\TestStatus\\TestStatus' => $vendorDir . '/phpunit/php-code-coverage/src/TestStatus/TestStatus.php',
    'SebastianBergmann\\CodeCoverage\\Test\\TestStatus\\Unknown' => $vendorDir . '/phpunit/php-code-coverage/src/TestStatus/Unknown.php',
    'SebastianBergmann\\CodeCoverage\\UnintentionallyCoveredCodeException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/UnintentionallyCoveredCodeException.php',
    'SebastianBergmann\\CodeCoverage\\Util\\DirectoryCouldNotBeCreatedException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/DirectoryCouldNotBeCreatedException.php',
    'SebastianBergmann\\CodeCoverage\\Util\\Filesystem' => $vendorDir . '/phpunit/php-code-coverage/src/Util/Filesystem.php',
    'SebastianBergmann\\CodeCoverage\\Util\\Percentage' => $vendorDir . '/phpunit/php-code-coverage/src/Util/Percentage.php',
    'SebastianBergmann\\CodeCoverage\\Version' => $vendorDir . '/phpunit/php-code-coverage/src/Version.php',
    'SebastianBergmann\\CodeCoverage\\XmlException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/XmlException.php',
    'SebastianBergmann\\CodeUnitReverseLookup\\Wizard' => $vendorDir . '/sebastian/code-unit-reverse-lookup/src/Wizard.php',
    'SebastianBergmann\\CodeUnit\\ClassMethodUnit' => $vendorDir . '/sebastian/code-unit/src/ClassMethodUnit.php',
    'SebastianBergmann\\CodeUnit\\ClassUnit' => $vendorDir . '/sebastian/code-unit/src/ClassUnit.php',
    'SebastianBergmann\\CodeUnit\\CodeUnit' => $vendorDir . '/sebastian/code-unit/src/CodeUnit.php',
    'SebastianBergmann\\CodeUnit\\CodeUnitCollection' => $vendorDir . '/sebastian/code-unit/src/CodeUnitCollection.php',
    'SebastianBergmann\\CodeUnit\\CodeUnitCollectionIterator' => $vendorDir . '/sebastian/code-unit/src/CodeUnitCollectionIterator.php',
    'SebastianBergmann\\CodeUnit\\Exception' => $vendorDir . '/sebastian/code-unit/src/exceptions/Exception.php',
    'SebastianBergmann\\CodeUnit\\FileUnit' => $vendorDir . '/sebastian/code-unit/src/FileUnit.php',
    'SebastianBergmann\\CodeUnit\\FunctionUnit' => $vendorDir . '/sebastian/code-unit/src/FunctionUnit.php',
    'SebastianBergmann\\CodeUnit\\InterfaceMethodUnit' => $vendorDir . '/sebastian/code-unit/src/InterfaceMethodUnit.php',
    'SebastianBergmann\\CodeUnit\\InterfaceUnit' => $vendorDir . '/sebastian/code-unit/src/InterfaceUnit.php',
    'SebastianBergmann\\CodeUnit\\InvalidCodeUnitException' => $vendorDir . '/sebastian/code-unit/src/exceptions/InvalidCodeUnitException.php',
    'SebastianBergmann\\CodeUnit\\Mapper' => $vendorDir . '/sebastian/code-unit/src/Mapper.php',
    'SebastianBergmann\\CodeUnit\\NoTraitException' => $vendorDir . '/sebastian/code-unit/src/exceptions/NoTraitException.php',
    'SebastianBergmann\\CodeUnit\\ReflectionException' => $vendorDir . '/sebastian/code-unit/src/exceptions/ReflectionException.php',
    'SebastianBergmann\\CodeUnit\\TraitMethodUnit' => $vendorDir . '/sebastian/code-unit/src/TraitMethodUnit.php',
    'SebastianBergmann\\CodeUnit\\TraitUnit' => $vendorDir . '/sebastian/code-unit/src/TraitUnit.php',
    'SebastianBergmann\\Comparator\\ArrayComparator' => $vendorDir . '/sebastian/comparator/src/ArrayComparator.php',
    'SebastianBergmann\\Comparator\\Comparator' => $vendorDir . '/sebastian/comparator/src/Comparator.php',
    'SebastianBergmann\\Comparator\\ComparisonFailure' => $vendorDir . '/sebastian/comparator/src/ComparisonFailure.php',
    'SebastianBergmann\\Comparator\\DOMNodeComparator' => $vendorDir . '/sebastian/comparator/src/DOMNodeComparator.php',
    'SebastianBergmann\\Comparator\\DateTimeComparator' => $vendorDir . '/sebastian/comparator/src/DateTimeComparator.php',
    'SebastianBergmann\\Comparator\\EnumerationComparator' => $vendorDir . '/sebastian/comparator/src/EnumerationComparator.php',
    'SebastianBergmann\\Comparator\\Exception' => $vendorDir . '/sebastian/comparator/src/exceptions/Exception.php',
    'SebastianBergmann\\Comparator\\ExceptionComparator' => $vendorDir . '/sebastian/comparator/src/ExceptionComparator.php',
    'SebastianBergmann\\Comparator\\Factory' => $vendorDir . '/sebastian/comparator/src/Factory.php',
    'SebastianBergmann\\Comparator\\MockObjectComparator' => $vendorDir . '/sebastian/comparator/src/MockObjectComparator.php',
    'SebastianBergmann\\Comparator\\NumberComparator' => $vendorDir . '/sebastian/comparator/src/NumberComparator.php',
    'SebastianBergmann\\Comparator\\NumericComparator' => $vendorDir . '/sebastian/comparator/src/NumericComparator.php',
    'SebastianBergmann\\Comparator\\ObjectComparator' => $vendorDir . '/sebastian/comparator/src/ObjectComparator.php',
    'SebastianBergmann\\Comparator\\ResourceComparator' => $vendorDir . '/sebastian/comparator/src/ResourceComparator.php',
    'SebastianBergmann\\Comparator\\RuntimeException' => $vendorDir . '/sebastian/comparator/src/exceptions/RuntimeException.php',
    'SebastianBergmann\\Comparator\\ScalarComparator' => $vendorDir . '/sebastian/comparator/src/ScalarComparator.php',
    'SebastianBergmann\\Comparator\\SplObjectStorageComparator' => $vendorDir . '/sebastian/comparator/src/SplObjectStorageComparator.php',
    'SebastianBergmann\\Comparator\\TypeComparator' => $vendorDir . '/sebastian/comparator/src/TypeComparator.php',
    'SebastianBergmann\\Complexity\\Calculator' => $vendorDir . '/sebastian/complexity/src/Calculator.php',
    'SebastianBergmann\\Complexity\\Complexity' => $vendorDir . '/sebastian/complexity/src/Complexity/Complexity.php',
    'SebastianBergmann\\Complexity\\ComplexityCalculatingVisitor' => $vendorDir . '/sebastian/complexity/src/Visitor/ComplexityCalculatingVisitor.php',
    'SebastianBergmann\\Complexity\\ComplexityCollection' => $vendorDir . '/sebastian/complexity/src/Complexity/ComplexityCollection.php',
    'SebastianBergmann\\Complexity\\ComplexityCollectionIterator' => $vendorDir . '/sebastian/complexity/src/Complexity/ComplexityCollectionIterator.php',
    'SebastianBergmann\\Complexity\\CyclomaticComplexityCalculatingVisitor' => $vendorDir . '/sebastian/complexity/src/Visitor/CyclomaticComplexityCalculatingVisitor.php',
    'SebastianBergmann\\Complexity\\Exception' => $vendorDir . '/sebastian/complexity/src/Exception/Exception.php',
    'SebastianBergmann\\Complexity\\RuntimeException' => $vendorDir . '/sebastian/complexity/src/Exception/RuntimeException.php',
    'SebastianBergmann\\Diff\\Chunk' => $vendorDir . '/sebastian/diff/src/Chunk.php',
    'SebastianBergmann\\Diff\\ConfigurationException' => $vendorDir . '/sebastian/diff/src/Exception/ConfigurationException.php',
    'SebastianBergmann\\Diff\\Diff' => $vendorDir . '/sebastian/diff/src/Diff.php',
    'SebastianBergmann\\Diff\\Differ' => $vendorDir . '/sebastian/diff/src/Differ.php',
    'SebastianBergmann\\Diff\\Exception' => $vendorDir . '/sebastian/diff/src/Exception/Exception.php',
    'SebastianBergmann\\Diff\\InvalidArgumentException' => $vendorDir . '/sebastian/diff/src/Exception/InvalidArgumentException.php',
    'SebastianBergmann\\Diff\\Line' => $vendorDir . '/sebastian/diff/src/Line.php',
    'SebastianBergmann\\Diff\\LongestCommonSubsequenceCalculator' => $vendorDir . '/sebastian/diff/src/LongestCommonSubsequenceCalculator.php',
    'SebastianBergmann\\Diff\\MemoryEfficientLongestCommonSubsequenceCalculator' => $vendorDir . '/sebastian/diff/src/MemoryEfficientLongestCommonSubsequenceCalculator.php',
    'SebastianBergmann\\Diff\\Output\\AbstractChunkOutputBuilder' => $vendorDir . '/sebastian/diff/src/Output/AbstractChunkOutputBuilder.php',
    'SebastianBergmann\\Diff\\Output\\DiffOnlyOutputBuilder' => $vendorDir . '/sebastian/diff/src/Output/DiffOnlyOutputBuilder.php',
    'SebastianBergmann\\Diff\\Output\\DiffOutputBuilderInterface' => $vendorDir . '/sebastian/diff/src/Output/DiffOutputBuilderInterface.php',
    'SebastianBergmann\\Diff\\Output\\StrictUnifiedDiffOutputBuilder' => $vendorDir . '/sebastian/diff/src/Output/StrictUnifiedDiffOutputBuilder.php',
    'SebastianBergmann\\Diff\\Output\\UnifiedDiffOutputBuilder' => $vendorDir . '/sebastian/diff/src/Output/UnifiedDiffOutputBuilder.php',
    'SebastianBergmann\\Diff\\Parser' => $vendorDir . '/sebastian/diff/src/Parser.php',
    'SebastianBergmann\\Diff\\TimeEfficientLongestCommonSubsequenceCalculator' => $vendorDir . '/sebastian/diff/src/TimeEfficientLongestCommonSubsequenceCalculator.php',
    'SebastianBergmann\\Environment\\Console' => $vendorDir . '/sebastian/environment/src/Console.php',
    'SebastianBergmann\\Environment\\Runtime' => $vendorDir . '/sebastian/environment/src/Runtime.php',
    'SebastianBergmann\\Exporter\\Exporter' => $vendorDir . '/sebastian/exporter/src/Exporter.php',
    'SebastianBergmann\\FileIterator\\ExcludeIterator' => $vendorDir . '/phpunit/php-file-iterator/src/ExcludeIterator.php',
    'SebastianBergmann\\FileIterator\\Facade' => $vendorDir . '/phpunit/php-file-iterator/src/Facade.php',
    'SebastianBergmann\\FileIterator\\Factory' => $vendorDir . '/phpunit/php-file-iterator/src/Factory.php',
    'SebastianBergmann\\FileIterator\\Iterator' => $vendorDir . '/phpunit/php-file-iterator/src/Iterator.php',
    'SebastianBergmann\\GlobalState\\CodeExporter' => $vendorDir . '/sebastian/global-state/src/CodeExporter.php',
    'SebastianBergmann\\GlobalState\\Exception' => $vendorDir . '/sebastian/global-state/src/exceptions/Exception.php',
    'SebastianBergmann\\GlobalState\\ExcludeList' => $vendorDir . '/sebastian/global-state/src/ExcludeList.php',
    'SebastianBergmann\\GlobalState\\Restorer' => $vendorDir . '/sebastian/global-state/src/Restorer.php',
    'SebastianBergmann\\GlobalState\\RuntimeException' => $vendorDir . '/sebastian/global-state/src/exceptions/RuntimeException.php',
    'SebastianBergmann\\GlobalState\\Snapshot' => $vendorDir . '/sebastian/global-state/src/Snapshot.php',
    'SebastianBergmann\\Invoker\\Exception' => $vendorDir . '/phpunit/php-invoker/src/exceptions/Exception.php',
    'SebastianBergmann\\Invoker\\Invoker' => $vendorDir . '/phpunit/php-invoker/src/Invoker.php',
    'SebastianBergmann\\Invoker\\ProcessControlExtensionNotLoadedException' => $vendorDir . '/phpunit/php-invoker/src/exceptions/ProcessControlExtensionNotLoadedException.php',
    'SebastianBergmann\\Invoker\\TimeoutException' => $vendorDir . '/phpunit/php-invoker/src/exceptions/TimeoutException.php',
    'SebastianBergmann\\LinesOfCode\\Counter' => $vendorDir . '/sebastian/lines-of-code/src/Counter.php',
    'SebastianBergmann\\LinesOfCode\\Exception' => $vendorDir . '/sebastian/lines-of-code/src/Exception/Exception.php',
    'SebastianBergmann\\LinesOfCode\\IllogicalValuesException' => $vendorDir . '/sebastian/lines-of-code/src/Exception/IllogicalValuesException.php',
    'SebastianBergmann\\LinesOfCode\\LineCountingVisitor' => $vendorDir . '/sebastian/lines-of-code/src/LineCountingVisitor.php',
    'SebastianBergmann\\LinesOfCode\\LinesOfCode' => $vendorDir . '/sebastian/lines-of-code/src/LinesOfCode.php',
    'SebastianBergmann\\LinesOfCode\\NegativeValueException' => $vendorDir . '/sebastian/lines-of-code/src/Exception/NegativeValueException.php',
    'SebastianBergmann\\LinesOfCode\\RuntimeException' => $vendorDir . '/sebastian/lines-of-code/src/Exception/RuntimeException.php',
    'SebastianBergmann\\ObjectEnumerator\\Enumerator' => $vendorDir . '/sebastian/object-enumerator/src/Enumerator.php',
    'SebastianBergmann\\ObjectReflector\\ObjectReflector' => $vendorDir . '/sebastian/object-reflector/src/ObjectReflector.php',
    'SebastianBergmann\\RecursionContext\\Context' => $vendorDir . '/sebastian/recursion-context/src/Context.php',
    'SebastianBergmann\\Template\\Exception' => $vendorDir . '/phpunit/php-text-template/src/exceptions/Exception.php',
    'SebastianBergmann\\Template\\InvalidArgumentException' => $vendorDir . '/phpunit/php-text-template/src/exceptions/InvalidArgumentException.php',
    'SebastianBergmann\\Template\\RuntimeException' => $vendorDir . '/phpunit/php-text-template/src/exceptions/RuntimeException.php',
    'SebastianBergmann\\Template\\Template' => $vendorDir . '/phpunit/php-text-template/src/Template.php',
    'SebastianBergmann\\Timer\\Duration' => $vendorDir . '/phpunit/php-timer/src/Duration.php',
    'SebastianBergmann\\Timer\\Exception' => $vendorDir . '/phpunit/php-timer/src/exceptions/Exception.php',
    'SebastianBergmann\\Timer\\NoActiveTimerException' => $vendorDir . '/phpunit/php-timer/src/exceptions/NoActiveTimerException.php',
    'SebastianBergmann\\Timer\\ResourceUsageFormatter' => $vendorDir . '/phpunit/php-timer/src/ResourceUsageFormatter.php',
    'SebastianBergmann\\Timer\\TimeSinceStartOfRequestNotAvailableException' => $vendorDir . '/phpunit/php-timer/src/exceptions/TimeSinceStartOfRequestNotAvailableException.php',
    'SebastianBergmann\\Timer\\Timer' => $vendorDir . '/phpunit/php-timer/src/Timer.php',
    'SebastianBergmann\\Type\\CallableType' => $vendorDir . '/sebastian/type/src/type/CallableType.php',
    'SebastianBergmann\\Type\\Exception' => $vendorDir . '/sebastian/type/src/exception/Exception.php',
    'SebastianBergmann\\Type\\FalseType' => $vendorDir . '/sebastian/type/src/type/FalseType.php',
    'SebastianBergmann\\Type\\GenericObjectType' => $vendorDir . '/sebastian/type/src/type/GenericObjectType.php',
    'SebastianBergmann\\Type\\IntersectionType' => $vendorDir . '/sebastian/type/src/type/IntersectionType.php',
    'SebastianBergmann\\Type\\IterableType' => $vendorDir . '/sebastian/type/src/type/IterableType.php',
    'SebastianBergmann\\Type\\MixedType' => $vendorDir . '/sebastian/type/src/type/MixedType.php',
    'SebastianBergmann\\Type\\NeverType' => $vendorDir . '/sebastian/type/src/type/NeverType.php',
    'SebastianBergmann\\Type\\NullType' => $vendorDir . '/sebastian/type/src/type/NullType.php',
    'SebastianBergmann\\Type\\ObjectType' => $vendorDir . '/sebastian/type/src/type/ObjectType.php',
    'SebastianBergmann\\Type\\Parameter' => $vendorDir . '/sebastian/type/src/Parameter.php',
    'SebastianBergmann\\Type\\ReflectionMapper' => $vendorDir . '/sebastian/type/src/ReflectionMapper.php',
    'SebastianBergmann\\Type\\RuntimeException' => $vendorDir . '/sebastian/type/src/exception/RuntimeException.php',
    'SebastianBergmann\\Type\\SimpleType' => $vendorDir . '/sebastian/type/src/type/SimpleType.php',
    'SebastianBergmann\\Type\\StaticType' => $vendorDir . '/sebastian/type/src/type/StaticType.php',
    'SebastianBergmann\\Type\\TrueType' => $vendorDir . '/sebastian/type/src/type/TrueType.php',
    'SebastianBergmann\\Type\\Type' => $vendorDir . '/sebastian/type/src/type/Type.php',
    'SebastianBergmann\\Type\\TypeName' => $vendorDir . '/sebastian/type/src/TypeName.php',
    'SebastianBergmann\\Type\\UnionType' => $vendorDir . '/sebastian/type/src/type/UnionType.php',
    'SebastianBergmann\\Type\\UnknownType' => $vendorDir . '/sebastian/type/src/type/UnknownType.php',
    'SebastianBergmann\\Type\\VoidType' => $vendorDir . '/sebastian/type/src/type/VoidType.php',
    'SebastianBergmann\\Version' => $vendorDir . '/sebastian/version/src/Version.php',
    'Stringable' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/Stringable.php',
    'Symfony\\Component\\Console\\Application' => $vendorDir . '/symfony/console/Application.php',
    'Symfony\\Component\\Console\\Attribute\\Argument' => $vendorDir . '/symfony/console/Attribute/Argument.php',
    'Symfony\\Component\\Console\\Attribute\\AsCommand' => $vendorDir . '/symfony/console/Attribute/AsCommand.php',
    'Symfony\\Component\\Console\\Attribute\\Option' => $vendorDir . '/symfony/console/Attribute/Option.php',
    'Symfony\\Component\\Console\\CI\\GithubActionReporter' => $vendorDir . '/symfony/console/CI/GithubActionReporter.php',
    'Symfony\\Component\\Console\\Color' => $vendorDir . '/symfony/console/Color.php',
    'Symfony\\Component\\Console\\CommandLoader\\CommandLoaderInterface' => $vendorDir . '/symfony/console/CommandLoader/CommandLoaderInterface.php',
    'Symfony\\Component\\Console\\CommandLoader\\ContainerCommandLoader' => $vendorDir . '/symfony/console/CommandLoader/ContainerCommandLoader.php',
    'Symfony\\Component\\Console\\CommandLoader\\FactoryCommandLoader' => $vendorDir . '/symfony/console/CommandLoader/FactoryCommandLoader.php',
    'Symfony\\Component\\Console\\Command\\Command' => $vendorDir . '/symfony/console/Command/Command.php',
    'Symfony\\Component\\Console\\Command\\CompleteCommand' => $vendorDir . '/symfony/console/Command/CompleteCommand.php',
    'Symfony\\Component\\Console\\Command\\DumpCompletionCommand' => $vendorDir . '/symfony/console/Command/DumpCompletionCommand.php',
    'Symfony\\Component\\Console\\Command\\HelpCommand' => $vendorDir . '/symfony/console/Command/HelpCommand.php',
    'Symfony\\Component\\Console\\Command\\InvokableCommand' => $vendorDir . '/symfony/console/Command/InvokableCommand.php',
    'Symfony\\Component\\Console\\Command\\LazyCommand' => $vendorDir . '/symfony/console/Command/LazyCommand.php',
    'Symfony\\Component\\Console\\Command\\ListCommand' => $vendorDir . '/symfony/console/Command/ListCommand.php',
    'Symfony\\Component\\Console\\Command\\LockableTrait' => $vendorDir . '/symfony/console/Command/LockableTrait.php',
    'Symfony\\Component\\Console\\Command\\SignalableCommandInterface' => $vendorDir . '/symfony/console/Command/SignalableCommandInterface.php',
    'Symfony\\Component\\Console\\Command\\TraceableCommand' => $vendorDir . '/symfony/console/Command/TraceableCommand.php',
    'Symfony\\Component\\Console\\Completion\\CompletionInput' => $vendorDir . '/symfony/console/Completion/CompletionInput.php',
    'Symfony\\Component\\Console\\Completion\\CompletionSuggestions' => $vendorDir . '/symfony/console/Completion/CompletionSuggestions.php',
    'Symfony\\Component\\Console\\Completion\\Output\\BashCompletionOutput' => $vendorDir . '/symfony/console/Completion/Output/BashCompletionOutput.php',
    'Symfony\\Component\\Console\\Completion\\Output\\CompletionOutputInterface' => $vendorDir . '/symfony/console/Completion/Output/CompletionOutputInterface.php',
    'Symfony\\Component\\Console\\Completion\\Output\\FishCompletionOutput' => $vendorDir . '/symfony/console/Completion/Output/FishCompletionOutput.php',
    'Symfony\\Component\\Console\\Completion\\Output\\ZshCompletionOutput' => $vendorDir . '/symfony/console/Completion/Output/ZshCompletionOutput.php',
    'Symfony\\Component\\Console\\Completion\\Suggestion' => $vendorDir . '/symfony/console/Completion/Suggestion.php',
    'Symfony\\Component\\Console\\ConsoleEvents' => $vendorDir . '/symfony/console/ConsoleEvents.php',
    'Symfony\\Component\\Console\\Cursor' => $vendorDir . '/symfony/console/Cursor.php',
    'Symfony\\Component\\Console\\DataCollector\\CommandDataCollector' => $vendorDir . '/symfony/console/DataCollector/CommandDataCollector.php',
    'Symfony\\Component\\Console\\Debug\\CliRequest' => $vendorDir . '/symfony/console/Debug/CliRequest.php',
    'Symfony\\Component\\Console\\DependencyInjection\\AddConsoleCommandPass' => $vendorDir . '/symfony/console/DependencyInjection/AddConsoleCommandPass.php',
    'Symfony\\Component\\Console\\Descriptor\\ApplicationDescription' => $vendorDir . '/symfony/console/Descriptor/ApplicationDescription.php',
    'Symfony\\Component\\Console\\Descriptor\\Descriptor' => $vendorDir . '/symfony/console/Descriptor/Descriptor.php',
    'Symfony\\Component\\Console\\Descriptor\\DescriptorInterface' => $vendorDir . '/symfony/console/Descriptor/DescriptorInterface.php',
    'Symfony\\Component\\Console\\Descriptor\\JsonDescriptor' => $vendorDir . '/symfony/console/Descriptor/JsonDescriptor.php',
    'Symfony\\Component\\Console\\Descriptor\\MarkdownDescriptor' => $vendorDir . '/symfony/console/Descriptor/MarkdownDescriptor.php',
    'Symfony\\Component\\Console\\Descriptor\\ReStructuredTextDescriptor' => $vendorDir . '/symfony/console/Descriptor/ReStructuredTextDescriptor.php',
    'Symfony\\Component\\Console\\Descriptor\\TextDescriptor' => $vendorDir . '/symfony/console/Descriptor/TextDescriptor.php',
    'Symfony\\Component\\Console\\Descriptor\\XmlDescriptor' => $vendorDir . '/symfony/console/Descriptor/XmlDescriptor.php',
    'Symfony\\Component\\Console\\EventListener\\ErrorListener' => $vendorDir . '/symfony/console/EventListener/ErrorListener.php',
    'Symfony\\Component\\Console\\Event\\ConsoleAlarmEvent' => $vendorDir . '/symfony/console/Event/ConsoleAlarmEvent.php',
    'Symfony\\Component\\Console\\Event\\ConsoleCommandEvent' => $vendorDir . '/symfony/console/Event/ConsoleCommandEvent.php',
    'Symfony\\Component\\Console\\Event\\ConsoleErrorEvent' => $vendorDir . '/symfony/console/Event/ConsoleErrorEvent.php',
    'Symfony\\Component\\Console\\Event\\ConsoleEvent' => $vendorDir . '/symfony/console/Event/ConsoleEvent.php',
    'Symfony\\Component\\Console\\Event\\ConsoleSignalEvent' => $vendorDir . '/symfony/console/Event/ConsoleSignalEvent.php',
    'Symfony\\Component\\Console\\Event\\ConsoleTerminateEvent' => $vendorDir . '/symfony/console/Event/ConsoleTerminateEvent.php',
    'Symfony\\Component\\Console\\Exception\\CommandNotFoundException' => $vendorDir . '/symfony/console/Exception/CommandNotFoundException.php',
    'Symfony\\Component\\Console\\Exception\\ExceptionInterface' => $vendorDir . '/symfony/console/Exception/ExceptionInterface.php',
    'Symfony\\Component\\Console\\Exception\\InvalidArgumentException' => $vendorDir . '/symfony/console/Exception/InvalidArgumentException.php',
    'Symfony\\Component\\Console\\Exception\\InvalidOptionException' => $vendorDir . '/symfony/console/Exception/InvalidOptionException.php',
    'Symfony\\Component\\Console\\Exception\\LogicException' => $vendorDir . '/symfony/console/Exception/LogicException.php',
    'Symfony\\Component\\Console\\Exception\\MissingInputException' => $vendorDir . '/symfony/console/Exception/MissingInputException.php',
    'Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException' => $vendorDir . '/symfony/console/Exception/NamespaceNotFoundException.php',
    'Symfony\\Component\\Console\\Exception\\RunCommandFailedException' => $vendorDir . '/symfony/console/Exception/RunCommandFailedException.php',
    'Symfony\\Component\\Console\\Exception\\RuntimeException' => $vendorDir . '/symfony/console/Exception/RuntimeException.php',
    'Symfony\\Component\\Console\\Formatter\\NullOutputFormatter' => $vendorDir . '/symfony/console/Formatter/NullOutputFormatter.php',
    'Symfony\\Component\\Console\\Formatter\\NullOutputFormatterStyle' => $vendorDir . '/symfony/console/Formatter/NullOutputFormatterStyle.php',
    'Symfony\\Component\\Console\\Formatter\\OutputFormatter' => $vendorDir . '/symfony/console/Formatter/OutputFormatter.php',
    'Symfony\\Component\\Console\\Formatter\\OutputFormatterInterface' => $vendorDir . '/symfony/console/Formatter/OutputFormatterInterface.php',
    'Symfony\\Component\\Console\\Formatter\\OutputFormatterStyle' => $vendorDir . '/symfony/console/Formatter/OutputFormatterStyle.php',
    'Symfony\\Component\\Console\\Formatter\\OutputFormatterStyleInterface' => $vendorDir . '/symfony/console/Formatter/OutputFormatterStyleInterface.php',
    'Symfony\\Component\\Console\\Formatter\\OutputFormatterStyleStack' => $vendorDir . '/symfony/console/Formatter/OutputFormatterStyleStack.php',
    'Symfony\\Component\\Console\\Formatter\\WrappableOutputFormatterInterface' => $vendorDir . '/symfony/console/Formatter/WrappableOutputFormatterInterface.php',
    'Symfony\\Component\\Console\\Helper\\DebugFormatterHelper' => $vendorDir . '/symfony/console/Helper/DebugFormatterHelper.php',
    'Symfony\\Component\\Console\\Helper\\DescriptorHelper' => $vendorDir . '/symfony/console/Helper/DescriptorHelper.php',
    'Symfony\\Component\\Console\\Helper\\Dumper' => $vendorDir . '/symfony/console/Helper/Dumper.php',
    'Symfony\\Component\\Console\\Helper\\FormatterHelper' => $vendorDir . '/symfony/console/Helper/FormatterHelper.php',
    'Symfony\\Component\\Console\\Helper\\Helper' => $vendorDir . '/symfony/console/Helper/Helper.php',
    'Symfony\\Component\\Console\\Helper\\HelperInterface' => $vendorDir . '/symfony/console/Helper/HelperInterface.php',
    'Symfony\\Component\\Console\\Helper\\HelperSet' => $vendorDir . '/symfony/console/Helper/HelperSet.php',
    'Symfony\\Component\\Console\\Helper\\InputAwareHelper' => $vendorDir . '/symfony/console/Helper/InputAwareHelper.php',
    'Symfony\\Component\\Console\\Helper\\OutputWrapper' => $vendorDir . '/symfony/console/Helper/OutputWrapper.php',
    'Symfony\\Component\\Console\\Helper\\ProcessHelper' => $vendorDir . '/symfony/console/Helper/ProcessHelper.php',
    'Symfony\\Component\\Console\\Helper\\ProgressBar' => $vendorDir . '/symfony/console/Helper/ProgressBar.php',
    'Symfony\\Component\\Console\\Helper\\ProgressIndicator' => $vendorDir . '/symfony/console/Helper/ProgressIndicator.php',
    'Symfony\\Component\\Console\\Helper\\QuestionHelper' => $vendorDir . '/symfony/console/Helper/QuestionHelper.php',
    'Symfony\\Component\\Console\\Helper\\SymfonyQuestionHelper' => $vendorDir . '/symfony/console/Helper/SymfonyQuestionHelper.php',
    'Symfony\\Component\\Console\\Helper\\Table' => $vendorDir . '/symfony/console/Helper/Table.php',
    'Symfony\\Component\\Console\\Helper\\TableCell' => $vendorDir . '/symfony/console/Helper/TableCell.php',
    'Symfony\\Component\\Console\\Helper\\TableCellStyle' => $vendorDir . '/symfony/console/Helper/TableCellStyle.php',
    'Symfony\\Component\\Console\\Helper\\TableRows' => $vendorDir . '/symfony/console/Helper/TableRows.php',
    'Symfony\\Component\\Console\\Helper\\TableSeparator' => $vendorDir . '/symfony/console/Helper/TableSeparator.php',
    'Symfony\\Component\\Console\\Helper\\TableStyle' => $vendorDir . '/symfony/console/Helper/TableStyle.php',
    'Symfony\\Component\\Console\\Helper\\TreeHelper' => $vendorDir . '/symfony/console/Helper/TreeHelper.php',
    'Symfony\\Component\\Console\\Helper\\TreeNode' => $vendorDir . '/symfony/console/Helper/TreeNode.php',
    'Symfony\\Component\\Console\\Helper\\TreeStyle' => $vendorDir . '/symfony/console/Helper/TreeStyle.php',
    'Symfony\\Component\\Console\\Input\\ArgvInput' => $vendorDir . '/symfony/console/Input/ArgvInput.php',
    'Symfony\\Component\\Console\\Input\\ArrayInput' => $vendorDir . '/symfony/console/Input/ArrayInput.php',
    'Symfony\\Component\\Console\\Input\\Input' => $vendorDir . '/symfony/console/Input/Input.php',
    'Symfony\\Component\\Console\\Input\\InputArgument' => $vendorDir . '/symfony/console/Input/InputArgument.php',
    'Symfony\\Component\\Console\\Input\\InputAwareInterface' => $vendorDir . '/symfony/console/Input/InputAwareInterface.php',
    'Symfony\\Component\\Console\\Input\\InputDefinition' => $vendorDir . '/symfony/console/Input/InputDefinition.php',
    'Symfony\\Component\\Console\\Input\\InputInterface' => $vendorDir . '/symfony/console/Input/InputInterface.php',
    'Symfony\\Component\\Console\\Input\\InputOption' => $vendorDir . '/symfony/console/Input/InputOption.php',
    'Symfony\\Component\\Console\\Input\\StreamableInputInterface' => $vendorDir . '/symfony/console/Input/StreamableInputInterface.php',
    'Symfony\\Component\\Console\\Input\\StringInput' => $vendorDir . '/symfony/console/Input/StringInput.php',
    'Symfony\\Component\\Console\\Logger\\ConsoleLogger' => $vendorDir . '/symfony/console/Logger/ConsoleLogger.php',
    'Symfony\\Component\\Console\\Messenger\\RunCommandContext' => $vendorDir . '/symfony/console/Messenger/RunCommandContext.php',
    'Symfony\\Component\\Console\\Messenger\\RunCommandMessage' => $vendorDir . '/symfony/console/Messenger/RunCommandMessage.php',
    'Symfony\\Component\\Console\\Messenger\\RunCommandMessageHandler' => $vendorDir . '/symfony/console/Messenger/RunCommandMessageHandler.php',
    'Symfony\\Component\\Console\\Output\\AnsiColorMode' => $vendorDir . '/symfony/console/Output/AnsiColorMode.php',
    'Symfony\\Component\\Console\\Output\\BufferedOutput' => $vendorDir . '/symfony/console/Output/BufferedOutput.php',
    'Symfony\\Component\\Console\\Output\\ConsoleOutput' => $vendorDir . '/symfony/console/Output/ConsoleOutput.php',
    'Symfony\\Component\\Console\\Output\\ConsoleOutputInterface' => $vendorDir . '/symfony/console/Output/ConsoleOutputInterface.php',
    'Symfony\\Component\\Console\\Output\\ConsoleSectionOutput' => $vendorDir . '/symfony/console/Output/ConsoleSectionOutput.php',
    'Symfony\\Component\\Console\\Output\\NullOutput' => $vendorDir . '/symfony/console/Output/NullOutput.php',
    'Symfony\\Component\\Console\\Output\\Output' => $vendorDir . '/symfony/console/Output/Output.php',
    'Symfony\\Component\\Console\\Output\\OutputInterface' => $vendorDir . '/symfony/console/Output/OutputInterface.php',
    'Symfony\\Component\\Console\\Output\\StreamOutput' => $vendorDir . '/symfony/console/Output/StreamOutput.php',
    'Symfony\\Component\\Console\\Output\\TrimmedBufferOutput' => $vendorDir . '/symfony/console/Output/TrimmedBufferOutput.php',
    'Symfony\\Component\\Console\\Question\\ChoiceQuestion' => $vendorDir . '/symfony/console/Question/ChoiceQuestion.php',
    'Symfony\\Component\\Console\\Question\\ConfirmationQuestion' => $vendorDir . '/symfony/console/Question/ConfirmationQuestion.php',
    'Symfony\\Component\\Console\\Question\\Question' => $vendorDir . '/symfony/console/Question/Question.php',
    'Symfony\\Component\\Console\\SignalRegistry\\SignalMap' => $vendorDir . '/symfony/console/SignalRegistry/SignalMap.php',
    'Symfony\\Component\\Console\\SignalRegistry\\SignalRegistry' => $vendorDir . '/symfony/console/SignalRegistry/SignalRegistry.php',
    'Symfony\\Component\\Console\\SingleCommandApplication' => $vendorDir . '/symfony/console/SingleCommandApplication.php',
    'Symfony\\Component\\Console\\Style\\OutputStyle' => $vendorDir . '/symfony/console/Style/OutputStyle.php',
    'Symfony\\Component\\Console\\Style\\StyleInterface' => $vendorDir . '/symfony/console/Style/StyleInterface.php',
    'Symfony\\Component\\Console\\Style\\SymfonyStyle' => $vendorDir . '/symfony/console/Style/SymfonyStyle.php',
    'Symfony\\Component\\Console\\Terminal' => $vendorDir . '/symfony/console/Terminal.php',
    'Symfony\\Component\\Console\\Tester\\ApplicationTester' => $vendorDir . '/symfony/console/Tester/ApplicationTester.php',
    'Symfony\\Component\\Console\\Tester\\CommandCompletionTester' => $vendorDir . '/symfony/console/Tester/CommandCompletionTester.php',
    'Symfony\\Component\\Console\\Tester\\CommandTester' => $vendorDir . '/symfony/console/Tester/CommandTester.php',
    'Symfony\\Component\\Console\\Tester\\Constraint\\CommandIsSuccessful' => $vendorDir . '/symfony/console/Tester/Constraint/CommandIsSuccessful.php',
    'Symfony\\Component\\Console\\Tester\\TesterTrait' => $vendorDir . '/symfony/console/Tester/TesterTrait.php',
    'Symfony\\Component\\EventDispatcher\\Attribute\\AsEventListener' => $vendorDir . '/symfony/event-dispatcher/Attribute/AsEventListener.php',
    'Symfony\\Component\\EventDispatcher\\Debug\\TraceableEventDispatcher' => $vendorDir . '/symfony/event-dispatcher/Debug/TraceableEventDispatcher.php',
    'Symfony\\Component\\EventDispatcher\\Debug\\WrappedListener' => $vendorDir . '/symfony/event-dispatcher/Debug/WrappedListener.php',
    'Symfony\\Component\\EventDispatcher\\DependencyInjection\\AddEventAliasesPass' => $vendorDir . '/symfony/event-dispatcher/DependencyInjection/AddEventAliasesPass.php',
    'Symfony\\Component\\EventDispatcher\\DependencyInjection\\RegisterListenersPass' => $vendorDir . '/symfony/event-dispatcher/DependencyInjection/RegisterListenersPass.php',
    'Symfony\\Component\\EventDispatcher\\EventDispatcher' => $vendorDir . '/symfony/event-dispatcher/EventDispatcher.php',
    'Symfony\\Component\\EventDispatcher\\EventDispatcherInterface' => $vendorDir . '/symfony/event-dispatcher/EventDispatcherInterface.php',
    'Symfony\\Component\\EventDispatcher\\EventSubscriberInterface' => $vendorDir . '/symfony/event-dispatcher/EventSubscriberInterface.php',
    'Symfony\\Component\\EventDispatcher\\GenericEvent' => $vendorDir . '/symfony/event-dispatcher/GenericEvent.php',
    'Symfony\\Component\\EventDispatcher\\ImmutableEventDispatcher' => $vendorDir . '/symfony/event-dispatcher/ImmutableEventDispatcher.php',
    'Symfony\\Component\\Filesystem\\Exception\\ExceptionInterface' => $vendorDir . '/symfony/filesystem/Exception/ExceptionInterface.php',
    'Symfony\\Component\\Filesystem\\Exception\\FileNotFoundException' => $vendorDir . '/symfony/filesystem/Exception/FileNotFoundException.php',
    'Symfony\\Component\\Filesystem\\Exception\\IOException' => $vendorDir . '/symfony/filesystem/Exception/IOException.php',
    'Symfony\\Component\\Filesystem\\Exception\\IOExceptionInterface' => $vendorDir . '/symfony/filesystem/Exception/IOExceptionInterface.php',
    'Symfony\\Component\\Filesystem\\Exception\\InvalidArgumentException' => $vendorDir . '/symfony/filesystem/Exception/InvalidArgumentException.php',
    'Symfony\\Component\\Filesystem\\Exception\\RuntimeException' => $vendorDir . '/symfony/filesystem/Exception/RuntimeException.php',
    'Symfony\\Component\\Filesystem\\Filesystem' => $vendorDir . '/symfony/filesystem/Filesystem.php',
    'Symfony\\Component\\Filesystem\\Path' => $vendorDir . '/symfony/filesystem/Path.php',
    'Symfony\\Component\\Finder\\Comparator\\Comparator' => $vendorDir . '/symfony/finder/Comparator/Comparator.php',
    'Symfony\\Component\\Finder\\Comparator\\DateComparator' => $vendorDir . '/symfony/finder/Comparator/DateComparator.php',
    'Symfony\\Component\\Finder\\Comparator\\NumberComparator' => $vendorDir . '/symfony/finder/Comparator/NumberComparator.php',
    'Symfony\\Component\\Finder\\Exception\\AccessDeniedException' => $vendorDir . '/symfony/finder/Exception/AccessDeniedException.php',
    'Symfony\\Component\\Finder\\Exception\\DirectoryNotFoundException' => $vendorDir . '/symfony/finder/Exception/DirectoryNotFoundException.php',
    'Symfony\\Component\\Finder\\Finder' => $vendorDir . '/symfony/finder/Finder.php',
    'Symfony\\Component\\Finder\\Gitignore' => $vendorDir . '/symfony/finder/Gitignore.php',
    'Symfony\\Component\\Finder\\Glob' => $vendorDir . '/symfony/finder/Glob.php',
    'Symfony\\Component\\Finder\\Iterator\\CustomFilterIterator' => $vendorDir . '/symfony/finder/Iterator/CustomFilterIterator.php',
    'Symfony\\Component\\Finder\\Iterator\\DateRangeFilterIterator' => $vendorDir . '/symfony/finder/Iterator/DateRangeFilterIterator.php',
    'Symfony\\Component\\Finder\\Iterator\\DepthRangeFilterIterator' => $vendorDir . '/symfony/finder/Iterator/DepthRangeFilterIterator.php',
    'Symfony\\Component\\Finder\\Iterator\\ExcludeDirectoryFilterIterator' => $vendorDir . '/symfony/finder/Iterator/ExcludeDirectoryFilterIterator.php',
    'Symfony\\Component\\Finder\\Iterator\\FileTypeFilterIterator' => $vendorDir . '/symfony/finder/Iterator/FileTypeFilterIterator.php',
    'Symfony\\Component\\Finder\\Iterator\\FilecontentFilterIterator' => $vendorDir . '/symfony/finder/Iterator/FilecontentFilterIterator.php',
    'Symfony\\Component\\Finder\\Iterator\\FilenameFilterIterator' => $vendorDir . '/symfony/finder/Iterator/FilenameFilterIterator.php',
    'Symfony\\Component\\Finder\\Iterator\\LazyIterator' => $vendorDir . '/symfony/finder/Iterator/LazyIterator.php',
    'Symfony\\Component\\Finder\\Iterator\\MultiplePcreFilterIterator' => $vendorDir . '/symfony/finder/Iterator/MultiplePcreFilterIterator.php',
    'Symfony\\Component\\Finder\\Iterator\\PathFilterIterator' => $vendorDir . '/symfony/finder/Iterator/PathFilterIterator.php',
    'Symfony\\Component\\Finder\\Iterator\\RecursiveDirectoryIterator' => $vendorDir . '/symfony/finder/Iterator/RecursiveDirectoryIterator.php',
    'Symfony\\Component\\Finder\\Iterator\\SizeRangeFilterIterator' => $vendorDir . '/symfony/finder/Iterator/SizeRangeFilterIterator.php',
    'Symfony\\Component\\Finder\\Iterator\\SortableIterator' => $vendorDir . '/symfony/finder/Iterator/SortableIterator.php',
    'Symfony\\Component\\Finder\\Iterator\\VcsIgnoredFilterIterator' => $vendorDir . '/symfony/finder/Iterator/VcsIgnoredFilterIterator.php',
    'Symfony\\Component\\Finder\\SplFileInfo' => $vendorDir . '/symfony/finder/SplFileInfo.php',
    'Symfony\\Component\\OptionsResolver\\Debug\\OptionsResolverIntrospector' => $vendorDir . '/symfony/options-resolver/Debug/OptionsResolverIntrospector.php',
    'Symfony\\Component\\OptionsResolver\\Exception\\AccessException' => $vendorDir . '/symfony/options-resolver/Exception/AccessException.php',
    'Symfony\\Component\\OptionsResolver\\Exception\\ExceptionInterface' => $vendorDir . '/symfony/options-resolver/Exception/ExceptionInterface.php',
    'Symfony\\Component\\OptionsResolver\\Exception\\InvalidArgumentException' => $vendorDir . '/symfony/options-resolver/Exception/InvalidArgumentException.php',
    'Symfony\\Component\\OptionsResolver\\Exception\\InvalidOptionsException' => $vendorDir . '/symfony/options-resolver/Exception/InvalidOptionsException.php',
    'Symfony\\Component\\OptionsResolver\\Exception\\MissingOptionsException' => $vendorDir . '/symfony/options-resolver/Exception/MissingOptionsException.php',
    'Symfony\\Component\\OptionsResolver\\Exception\\NoConfigurationException' => $vendorDir . '/symfony/options-resolver/Exception/NoConfigurationException.php',
    'Symfony\\Component\\OptionsResolver\\Exception\\NoSuchOptionException' => $vendorDir . '/symfony/options-resolver/Exception/NoSuchOptionException.php',
    'Symfony\\Component\\OptionsResolver\\Exception\\OptionDefinitionException' => $vendorDir . '/symfony/options-resolver/Exception/OptionDefinitionException.php',
    'Symfony\\Component\\OptionsResolver\\Exception\\UndefinedOptionsException' => $vendorDir . '/symfony/options-resolver/Exception/UndefinedOptionsException.php',
    'Symfony\\Component\\OptionsResolver\\OptionConfigurator' => $vendorDir . '/symfony/options-resolver/OptionConfigurator.php',
    'Symfony\\Component\\OptionsResolver\\Options' => $vendorDir . '/symfony/options-resolver/Options.php',
    'Symfony\\Component\\OptionsResolver\\OptionsResolver' => $vendorDir . '/symfony/options-resolver/OptionsResolver.php',
    'Symfony\\Component\\Process\\Exception\\ExceptionInterface' => $vendorDir . '/symfony/process/Exception/ExceptionInterface.php',
    'Symfony\\Component\\Process\\Exception\\InvalidArgumentException' => $vendorDir . '/symfony/process/Exception/InvalidArgumentException.php',
    'Symfony\\Component\\Process\\Exception\\LogicException' => $vendorDir . '/symfony/process/Exception/LogicException.php',
    'Symfony\\Component\\Process\\Exception\\ProcessFailedException' => $vendorDir . '/symfony/process/Exception/ProcessFailedException.php',
    'Symfony\\Component\\Process\\Exception\\ProcessSignaledException' => $vendorDir . '/symfony/process/Exception/ProcessSignaledException.php',
    'Symfony\\Component\\Process\\Exception\\ProcessStartFailedException' => $vendorDir . '/symfony/process/Exception/ProcessStartFailedException.php',
    'Symfony\\Component\\Process\\Exception\\ProcessTimedOutException' => $vendorDir . '/symfony/process/Exception/ProcessTimedOutException.php',
    'Symfony\\Component\\Process\\Exception\\RunProcessFailedException' => $vendorDir . '/symfony/process/Exception/RunProcessFailedException.php',
    'Symfony\\Component\\Process\\Exception\\RuntimeException' => $vendorDir . '/symfony/process/Exception/RuntimeException.php',
    'Symfony\\Component\\Process\\ExecutableFinder' => $vendorDir . '/symfony/process/ExecutableFinder.php',
    'Symfony\\Component\\Process\\InputStream' => $vendorDir . '/symfony/process/InputStream.php',
    'Symfony\\Component\\Process\\Messenger\\RunProcessContext' => $vendorDir . '/symfony/process/Messenger/RunProcessContext.php',
    'Symfony\\Component\\Process\\Messenger\\RunProcessMessage' => $vendorDir . '/symfony/process/Messenger/RunProcessMessage.php',
    'Symfony\\Component\\Process\\Messenger\\RunProcessMessageHandler' => $vendorDir . '/symfony/process/Messenger/RunProcessMessageHandler.php',
    'Symfony\\Component\\Process\\PhpExecutableFinder' => $vendorDir . '/symfony/process/PhpExecutableFinder.php',
    'Symfony\\Component\\Process\\PhpProcess' => $vendorDir . '/symfony/process/PhpProcess.php',
    'Symfony\\Component\\Process\\PhpSubprocess' => $vendorDir . '/symfony/process/PhpSubprocess.php',
    'Symfony\\Component\\Process\\Pipes\\AbstractPipes' => $vendorDir . '/symfony/process/Pipes/AbstractPipes.php',
    'Symfony\\Component\\Process\\Pipes\\PipesInterface' => $vendorDir . '/symfony/process/Pipes/PipesInterface.php',
    'Symfony\\Component\\Process\\Pipes\\UnixPipes' => $vendorDir . '/symfony/process/Pipes/UnixPipes.php',
    'Symfony\\Component\\Process\\Pipes\\WindowsPipes' => $vendorDir . '/symfony/process/Pipes/WindowsPipes.php',
    'Symfony\\Component\\Process\\Process' => $vendorDir . '/symfony/process/Process.php',
    'Symfony\\Component\\Process\\ProcessUtils' => $vendorDir . '/symfony/process/ProcessUtils.php',
    'Symfony\\Component\\Stopwatch\\Section' => $vendorDir . '/symfony/stopwatch/Section.php',
    'Symfony\\Component\\Stopwatch\\Stopwatch' => $vendorDir . '/symfony/stopwatch/Stopwatch.php',
    'Symfony\\Component\\Stopwatch\\StopwatchEvent' => $vendorDir . '/symfony/stopwatch/StopwatchEvent.php',
    'Symfony\\Component\\Stopwatch\\StopwatchPeriod' => $vendorDir . '/symfony/stopwatch/StopwatchPeriod.php',
    'Symfony\\Component\\String\\AbstractString' => $vendorDir . '/symfony/string/AbstractString.php',
    'Symfony\\Component\\String\\AbstractUnicodeString' => $vendorDir . '/symfony/string/AbstractUnicodeString.php',
    'Symfony\\Component\\String\\ByteString' => $vendorDir . '/symfony/string/ByteString.php',
    'Symfony\\Component\\String\\CodePointString' => $vendorDir . '/symfony/string/CodePointString.php',
    'Symfony\\Component\\String\\Exception\\ExceptionInterface' => $vendorDir . '/symfony/string/Exception/ExceptionInterface.php',
    'Symfony\\Component\\String\\Exception\\InvalidArgumentException' => $vendorDir . '/symfony/string/Exception/InvalidArgumentException.php',
    'Symfony\\Component\\String\\Exception\\RuntimeException' => $vendorDir . '/symfony/string/Exception/RuntimeException.php',
    'Symfony\\Component\\String\\Inflector\\EnglishInflector' => $vendorDir . '/symfony/string/Inflector/EnglishInflector.php',
    'Symfony\\Component\\String\\Inflector\\FrenchInflector' => $vendorDir . '/symfony/string/Inflector/FrenchInflector.php',
    'Symfony\\Component\\String\\Inflector\\InflectorInterface' => $vendorDir . '/symfony/string/Inflector/InflectorInterface.php',
    'Symfony\\Component\\String\\Inflector\\SpanishInflector' => $vendorDir . '/symfony/string/Inflector/SpanishInflector.php',
    'Symfony\\Component\\String\\LazyString' => $vendorDir . '/symfony/string/LazyString.php',
    'Symfony\\Component\\String\\Slugger\\AsciiSlugger' => $vendorDir . '/symfony/string/Slugger/AsciiSlugger.php',
    'Symfony\\Component\\String\\Slugger\\SluggerInterface' => $vendorDir . '/symfony/string/Slugger/SluggerInterface.php',
    'Symfony\\Component\\String\\TruncateMode' => $vendorDir . '/symfony/string/TruncateMode.php',
    'Symfony\\Component\\String\\UnicodeString' => $vendorDir . '/symfony/string/UnicodeString.php',
    'Symfony\\Contracts\\EventDispatcher\\Event' => $vendorDir . '/symfony/event-dispatcher-contracts/Event.php',
    'Symfony\\Contracts\\EventDispatcher\\EventDispatcherInterface' => $vendorDir . '/symfony/event-dispatcher-contracts/EventDispatcherInterface.php',
    'Symfony\\Contracts\\Service\\Attribute\\Required' => $vendorDir . '/symfony/service-contracts/Attribute/Required.php',
    'Symfony\\Contracts\\Service\\Attribute\\SubscribedService' => $vendorDir . '/symfony/service-contracts/Attribute/SubscribedService.php',
    'Symfony\\Contracts\\Service\\ResetInterface' => $vendorDir . '/symfony/service-contracts/ResetInterface.php',
    'Symfony\\Contracts\\Service\\ServiceCollectionInterface' => $vendorDir . '/symfony/service-contracts/ServiceCollectionInterface.php',
    'Symfony\\Contracts\\Service\\ServiceLocatorTrait' => $vendorDir . '/symfony/service-contracts/ServiceLocatorTrait.php',
    'Symfony\\Contracts\\Service\\ServiceMethodsSubscriberTrait' => $vendorDir . '/symfony/service-contracts/ServiceMethodsSubscriberTrait.php',
    'Symfony\\Contracts\\Service\\ServiceProviderInterface' => $vendorDir . '/symfony/service-contracts/ServiceProviderInterface.php',
    'Symfony\\Contracts\\Service\\ServiceSubscriberInterface' => $vendorDir . '/symfony/service-contracts/ServiceSubscriberInterface.php',
    'Symfony\\Contracts\\Service\\ServiceSubscriberTrait' => $vendorDir . '/symfony/service-contracts/ServiceSubscriberTrait.php',
    'Symfony\\Polyfill\\Ctype\\Ctype' => $vendorDir . '/symfony/polyfill-ctype/Ctype.php',
    'Symfony\\Polyfill\\Iconv\\Iconv' => $vendorDir . '/symfony/polyfill-iconv/Iconv.php',
    'Symfony\\Polyfill\\Intl\\Grapheme\\Grapheme' => $vendorDir . '/symfony/polyfill-intl-grapheme/Grapheme.php',
    'Symfony\\Polyfill\\Intl\\Idn\\Idn' => $vendorDir . '/symfony/polyfill-intl-idn/Idn.php',
    'Symfony\\Polyfill\\Intl\\Idn\\Info' => $vendorDir . '/symfony/polyfill-intl-idn/Info.php',
    'Symfony\\Polyfill\\Intl\\Idn\\Resources\\unidata\\DisallowedRanges' => $vendorDir . '/symfony/polyfill-intl-idn/Resources/unidata/DisallowedRanges.php',
    'Symfony\\Polyfill\\Intl\\Idn\\Resources\\unidata\\Regex' => $vendorDir . '/symfony/polyfill-intl-idn/Resources/unidata/Regex.php',
    'Symfony\\Polyfill\\Intl\\Normalizer\\Normalizer' => $vendorDir . '/symfony/polyfill-intl-normalizer/Normalizer.php',
    'Symfony\\Polyfill\\Mbstring\\Mbstring' => $vendorDir . '/symfony/polyfill-mbstring/Mbstring.php',
    'Symfony\\Polyfill\\Php80\\Php80' => $vendorDir . '/symfony/polyfill-php80/Php80.php',
    'Symfony\\Polyfill\\Php80\\PhpToken' => $vendorDir . '/symfony/polyfill-php80/PhpToken.php',
    'Symfony\\Polyfill\\Php81\\Php81' => $vendorDir . '/symfony/polyfill-php81/Php81.php',
    'TheSeer\\Tokenizer\\Exception' => $vendorDir . '/theseer/tokenizer/src/Exception.php',
    'TheSeer\\Tokenizer\\NamespaceUri' => $vendorDir . '/theseer/tokenizer/src/NamespaceUri.php',
    'TheSeer\\Tokenizer\\NamespaceUriException' => $vendorDir . '/theseer/tokenizer/src/NamespaceUriException.php',
    'TheSeer\\Tokenizer\\Token' => $vendorDir . '/theseer/tokenizer/src/Token.php',
    'TheSeer\\Tokenizer\\TokenCollection' => $vendorDir . '/theseer/tokenizer/src/TokenCollection.php',
    'TheSeer\\Tokenizer\\TokenCollectionException' => $vendorDir . '/theseer/tokenizer/src/TokenCollectionException.php',
    'TheSeer\\Tokenizer\\Tokenizer' => $vendorDir . '/theseer/tokenizer/src/Tokenizer.php',
    'TheSeer\\Tokenizer\\XMLSerializer' => $vendorDir . '/theseer/tokenizer/src/XMLSerializer.php',
    'UnhandledMatchError' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/UnhandledMatchError.php',
    'ValueError' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/ValueError.php',
    'staabm\\SideEffectsDetector\\SideEffect' => $vendorDir . '/staabm/side-effects-detector/lib/SideEffect.php',
    'staabm\\SideEffectsDetector\\SideEffectsDetector' => $vendorDir . '/staabm/side-effects-detector/lib/SideEffectsDetector.php',
);
