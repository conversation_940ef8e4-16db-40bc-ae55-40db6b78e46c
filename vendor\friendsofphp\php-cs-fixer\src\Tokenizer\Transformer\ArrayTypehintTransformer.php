<?php

declare(strict_types=1);

/*
 * This file is part of PHP CS Fixer.
 *
 * (c) F<PERSON><PERSON> <<EMAIL>>
 *     <PERSON><PERSON> <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled
 * with this source code in the file LICENSE.
 */

namespace Php<PERSON><PERSON>ixer\Tokenizer\Transformer;

use <PERSON>pCs<PERSON><PERSON><PERSON>\Tokenizer\AbstractTransformer;
use PhpCsFix<PERSON>\Tokenizer\CT;
use <PERSON>p<PERSON><PERSON><PERSON>er\Tokenizer\Token;
use <PERSON>pCsF<PERSON>er\Tokenizer\Tokens;

/**
 * Transform `array` typehint from T_ARRAY into CT::T_ARRAY_TYPEHINT.
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @internal
 */
final class ArrayTypehintTransformer extends AbstractTransformer
{
    public function getRequiredPhpVersionId(): int
    {
        return 5_00_00;
    }

    public function process(Tokens $tokens, Token $token, int $index): void
    {
        if (!$token->isGivenKind(T_ARRAY)) {
            return;
        }

        $nextIndex = $tokens->getNextMeaningfulToken($index);
        $nextToken = $tokens[$nextIndex];

        if (!$nextToken->equals('(')) {
            $tokens[$index] = new Token([CT::T_ARRAY_TYPEHINT, $token->getContent()]);
        }
    }

    public function getCustomTokens(): array
    {
        return [CT::T_ARRAY_TYPEHINT];
    }
}
