<?php

declare(strict_types=1);

/*
 * This file is part of PHP CS Fixer.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *     <PERSON><PERSON> <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled
 * with this source code in the file LICENSE.
 */

namespace PhpCsFixer\Differ;

use PhpCsFixer\Preg;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Diff\Differ;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Diff\Output\StrictUnifiedDiffOutputBuilder;

final class UnifiedDiffer implements DifferInterface
{
    public function diff(string $old, string $new, ?\SplFileInfo $file = null): string
    {
        if (null === $file) {
            $options = [
                'fromFile' => 'Original',
                'toFile' => 'New',
            ];
        } else {
            $filePath = $file->getRealPath();

            if (Preg::match('/\s/', $filePath)) {
                $filePath = '"'.$filePath.'"';
            }

            $options = [
                'fromFile' => $filePath,
                'toFile' => $filePath,
            ];
        }

        $differ = new Differ(new StrictUnifiedDiffOutputBuilder($options));

        return $differ->diff($old, $new);
    }
}
