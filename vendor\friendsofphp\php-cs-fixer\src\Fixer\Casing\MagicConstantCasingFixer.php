<?php

declare(strict_types=1);

/*
 * This file is part of PHP CS Fixer.
 *
 * (c) <PERSON><PERSON><PERSON>cier <<EMAIL>>
 *     <PERSON><PERSON> <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled
 * with this source code in the file LICENSE.
 */

namespace PhpCsFixer\Fixer\Casing;

use PhpCsF<PERSON>er\AbstractFixer;
use PhpCsFixer\FixerDefinition\CodeSample;
use PhpCsFixer\FixerDefinition\FixerDefinition;
use PhpCsFixer\FixerDefinition\FixerDefinitionInterface;
use PhpCsFixer\Tokenizer\CT;
use PhpCsFixer\Tokenizer\Token;
use Php<PERSON><PERSON>ixer\Tokenizer\Tokens;

/**
 * <AUTHOR>
 */
final class MagicConstantCasingFixer extends AbstractFixer
{
    public function getDefinition(): FixerDefinitionInterface
    {
        return new FixerDefinition(
            'Magic constants should be referred to using the correct casing.',
            [new CodeSample("<?php\necho __dir__;\n")]
        );
    }

    public function isCandidate(Tokens $tokens): bool
    {
        return $tokens->isAnyTokenKindsFound($this->getMagicConstantTokens());
    }

    protected function applyFix(\SplFileInfo $file, Tokens $tokens): void
    {
        $magicConstants = $this->getMagicConstants();
        $magicConstantTokens = $this->getMagicConstantTokens();

        foreach ($tokens as $index => $token) {
            if ($token->isGivenKind($magicConstantTokens)) {
                $tokens[$index] = new Token([$token->getId(), $magicConstants[$token->getId()]]);
            }
        }
    }

    /**
     * @return array<int, string>
     */
    private function getMagicConstants(): array
    {
        static $magicConstants = null;

        if (null === $magicConstants) {
            $magicConstants = [
                T_LINE => '__LINE__',
                T_FILE => '__FILE__',
                T_DIR => '__DIR__',
                T_FUNC_C => '__FUNCTION__',
                T_CLASS_C => '__CLASS__',
                T_METHOD_C => '__METHOD__',
                T_NS_C => '__NAMESPACE__',
                CT::T_CLASS_CONSTANT => 'class',
                T_TRAIT_C => '__TRAIT__',
            ];
        }

        return $magicConstants;
    }

    /**
     * @return list<int>
     */
    private function getMagicConstantTokens(): array
    {
        static $magicConstantTokens = null;

        if (null === $magicConstantTokens) {
            $magicConstantTokens = array_keys($this->getMagicConstants());
        }

        return $magicConstantTokens;
    }
}
