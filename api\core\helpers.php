<?php

declare(strict_types=1);

use EticSimple\Core\Application;

if (!function_exists('app')) {
    /**
     * Application instance'ını al
     */
    function app(?string $service = null): mixed
    {
        $app = Application::getInstance();
        
        if ($service === null) {
            return $app;
        }
        
        return $app->get($service);
    }
}

if (!function_exists('config')) {
    /**
     * Environment değişkenini al
     */
    function config(string $key, mixed $default = null): mixed
    {
        return $_ENV[$key] ?? $default;
    }
}

if (!function_exists('cache')) {
    /**
     * Cache manager'ı al
     */
    function cache(): \EticSimple\Core\Cache\CacheManager
    {
        return app('cache');
    }
}

if (!function_exists('db')) {
    /**
     * Database manager'ı al
     */
    function db(): \EticSimple\Core\Database\DatabaseManager
    {
        return app('db');
    }
}

if (!function_exists('theme')) {
    /**
     * Theme manager'ı al
     */
    function theme(): \EticSimple\Core\Theme\ThemeManager
    {
        return app('theme');
    }
}

if (!function_exists('logger')) {
    /**
     * Logger'ı al
     */
    function logger(): \Monolog\Logger
    {
        return app('logger');
    }
}

if (!function_exists('response')) {
    /**
     * JSON response oluştur
     */
    function response(mixed $data = null, string $message = 'İşlem başarılı', bool $success = true, int $status = 200): array
    {
        $response = [
            'success' => $success,
            'message' => $message,
        ];
        
        if ($data !== null) {
            $response['data'] = $data;
        }
        
        if ($status !== 200) {
            $response['status'] = $status;
        }
        
        return $response;
    }
}

if (!function_exists('error_response')) {
    /**
     * Error response oluştur
     */
    function error_response(string $message = 'Hata oluştu', array $errors = [], int $status = 400): array
    {
        $response = [
            'success' => false,
            'message' => $message,
            'status' => $status
        ];
        
        if (!empty($errors)) {
            $response['errors'] = $errors;
        }
        
        return $response;
    }
}

if (!function_exists('validate_tc_kimlik')) {
    /**
     * T.C. Kimlik No doğrulama
     */
    function validate_tc_kimlik(?string $tcKimlik): bool
    {
        if (!$tcKimlik || strlen($tcKimlik) !== 11) {
            return false;
        }
        
        // Sadece rakam kontrolü
        if (!ctype_digit($tcKimlik)) {
            return false;
        }
        
        // İlk rakam 0 olamaz
        if ($tcKimlik[0] === '0') {
            return false;
        }
        
        // T.C. Kimlik No algoritması
        $digits = str_split($tcKimlik);
        $sum1 = 0;
        $sum2 = 0;
        
        // İlk 10 rakamın toplamı
        for ($i = 0; $i < 10; $i++) {
            if ($i % 2 === 0) {
                $sum1 += (int) $digits[$i];
            } else {
                $sum2 += (int) $digits[$i];
            }
        }
        
        // 10. rakam kontrolü
        $check1 = ($sum1 * 7 - $sum2) % 10;
        if ($check1 !== (int) $digits[9]) {
            return false;
        }
        
        // 11. rakam kontrolü
        $check2 = ($sum1 + $sum2 + (int) $digits[9]) % 10;
        if ($check2 !== (int) $digits[10]) {
            return false;
        }
        
        return true;
    }
}

if (!function_exists('format_currency')) {
    /**
     * Para birimi formatla (Türk Lirası)
     */
    function format_currency(float $amount, string $currency = 'TRY'): string
    {
        $symbol = match ($currency) {
            'TRY' => '₺',
            'USD' => '$',
            'EUR' => '€',
            default => $currency
        };
        
        // Türkçe format: 1.234,56 ₺
        return number_format($amount, 2, ',', '.') . ' ' . $symbol;
    }
}

if (!function_exists('format_date')) {
    /**
     * Tarih formatla (Türkçe format)
     */
    function format_date(string|\DateTime $date, string $format = 'd.m.Y'): string
    {
        if (is_string($date)) {
            $date = new \DateTime($date);
        }
        
        return $date->format($format);
    }
}

if (!function_exists('format_phone')) {
    /**
     * Telefon numarası formatla (Türkiye formatı)
     */
    function format_phone(string $phone): string
    {
        // Sadece rakamları al
        $phone = preg_replace('/[^0-9]/', '', $phone);
        
        // Türkiye formatına çevir
        if (strlen($phone) === 11 && str_starts_with($phone, '0')) {
            // 05XX XXX XX XX formatı
            return substr($phone, 0, 4) . ' ' . substr($phone, 4, 3) . ' ' . substr($phone, 7, 2) . ' ' . substr($phone, 9, 2);
        } elseif (strlen($phone) === 10) {
            // +90 5XX XXX XX XX formatı
            return '+90 ' . substr($phone, 0, 3) . ' ' . substr($phone, 3, 3) . ' ' . substr($phone, 6, 2) . ' ' . substr($phone, 8, 2);
        }
        
        return $phone;
    }
}

if (!function_exists('sanitize_input')) {
    /**
     * Input'u temizle
     */
    function sanitize_input(string $input): string
    {
        return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }
}

if (!function_exists('generate_slug')) {
    /**
     * URL slug oluştur (Türkçe karakter desteği)
     */
    function generate_slug(string $text): string
    {
        // Türkçe karakterleri değiştir
        $turkish = ['ç', 'ğ', 'ı', 'ö', 'ş', 'ü', 'Ç', 'Ğ', 'I', 'İ', 'Ö', 'Ş', 'Ü'];
        $english = ['c', 'g', 'i', 'o', 's', 'u', 'c', 'g', 'i', 'i', 'o', 's', 'u'];
        $text = str_replace($turkish, $english, $text);
        
        // Küçük harfe çevir
        $text = strtolower($text);
        
        // Özel karakterleri kaldır
        $text = preg_replace('/[^a-z0-9\s-]/', '', $text);
        
        // Boşlukları tire ile değiştir
        $text = preg_replace('/[\s-]+/', '-', $text);
        
        // Başındaki ve sonundaki tireleri kaldır
        return trim($text, '-');
    }
}

if (!function_exists('is_valid_email')) {
    /**
     * Email doğrulama
     */
    function is_valid_email(string $email): bool
    {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }
}

if (!function_exists('generate_token')) {
    /**
     * Güvenli token oluştur
     */
    function generate_token(int $length = 32): string
    {
        return bin2hex(random_bytes($length / 2));
    }
}

if (!function_exists('hash_password')) {
    /**
     * Şifre hash'le
     */
    function hash_password(string $password): string
    {
        $cost = (int) config('BCRYPT_ROUNDS', 12);
        return password_hash($password, PASSWORD_BCRYPT, ['cost' => $cost]);
    }
}

if (!function_exists('verify_password')) {
    /**
     * Şifre doğrula
     */
    function verify_password(string $password, string $hash): bool
    {
        return password_verify($password, $hash);
    }
}

if (!function_exists('array_get')) {
    /**
     * Array'den güvenli değer al
     */
    function array_get(array $array, string $key, mixed $default = null): mixed
    {
        $keys = explode('.', $key);
        $value = $array;
        
        foreach ($keys as $k) {
            if (!is_array($value) || !array_key_exists($k, $value)) {
                return $default;
            }
            $value = $value[$k];
        }
        
        return $value;
    }
}

if (!function_exists('str_limit')) {
    /**
     * String'i belirli uzunlukta kes
     */
    function str_limit(string $value, int $limit = 100, string $end = '...'): string
    {
        if (mb_strlen($value, 'UTF-8') <= $limit) {
            return $value;
        }
        
        return mb_substr($value, 0, $limit, 'UTF-8') . $end;
    }
}
