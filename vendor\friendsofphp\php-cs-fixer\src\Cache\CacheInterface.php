<?php

declare(strict_types=1);

/*
 * This file is part of PHP CS Fixer.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *     <PERSON><PERSON> <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled
 * with this source code in the file LICENSE.
 */

namespace PhpCsFixer\Cache;

/**
 * <AUTHOR> <<EMAIL>>
 *
 * @internal
 */
interface CacheInterface
{
    public function getSignature(): SignatureInterface;

    public function has(string $file): bool;

    public function get(string $file): ?string;

    public function set(string $file, string $hash): void;

    public function clear(string $file): void;

    public function toJson(): string;
}
