<?php

declare(strict_types=1);

namespace EticSimple\Core\Router;

// Route sınıfını include et
require_once __DIR__ . '/Route.php';

/**
 * RESTful Router Sistemi
 *
 * HTTP isteklerini controller'lara yönlendirme
 */
class Router
{
    private array $routes = [];
    private array $middleware = [];
    private string $prefix = '';
    
    /**
     * GET route tanımla
     */
    public function get(string $path, callable|array $handler): Route
    {
        return $this->addRoute('GET', $path, $handler);
    }
    
    /**
     * POST route tanımla
     */
    public function post(string $path, callable|array $handler): Route
    {
        return $this->addRoute('POST', $path, $handler);
    }
    
    /**
     * PUT route tanımla
     */
    public function put(string $path, callable|array $handler): Route
    {
        return $this->addRoute('PUT', $path, $handler);
    }
    
    /**
     * DELETE route tanımla
     */
    public function delete(string $path, callable|array $handler): Route
    {
        return $this->addRoute('DELETE', $path, $handler);
    }
    
    /**
     * PATCH route tanımla
     */
    public function patch(string $path, callable|array $handler): Route
    {
        return $this->addRoute('PATCH', $path, $handler);
    }
    
    /**
     * OPTIONS route tanımla
     */
    public function options(string $path, callable|array $handler): Route
    {
        return $this->addRoute('OPTIONS', $path, $handler);
    }
    
    /**
     * Route grubu tanımla
     */
    public function group(array $attributes, callable $callback): void
    {
        $previousPrefix = $this->prefix;
        $previousMiddleware = $this->middleware;
        
        // Prefix ekle
        if (isset($attributes['prefix'])) {
            $this->prefix = rtrim($previousPrefix, '/') . '/' . trim($attributes['prefix'], '/');
        }
        
        // Middleware ekle
        if (isset($attributes['middleware'])) {
            $this->middleware = array_merge($this->middleware, (array) $attributes['middleware']);
        }
        
        // Callback'i çalıştır
        $callback($this);
        
        // Önceki değerleri geri yükle
        $this->prefix = $previousPrefix;
        $this->middleware = $previousMiddleware;
    }
    
    /**
     * Route ekle
     */
    private function addRoute(string $method, string $path, callable|array $handler): Route
    {
        $path = $this->prefix . $path;
        $route = new Route($method, $path, $handler);
        $route->middleware($this->middleware);
        
        $this->routes[] = $route;
        
        return $route;
    }
    
    /**
     * İsteği işle ve route'u bul
     */
    public function dispatch(): array
    {
        $method = $_SERVER['REQUEST_METHOD'];
        $uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        
        // Route'ları kontrol et
        foreach ($this->routes as $route) {
            if ($route->matches($method, $uri)) {
                return $this->handleRoute($route, $uri);
            }
        }
        
        // 404 Not Found
        return $this->notFound();
    }
    
    /**
     * Route'u işle
     */
    private function handleRoute(Route $route, string $uri): array
    {
        try {
            // Middleware'leri çalıştır
            $middlewareResponse = $this->runMiddleware($route->getMiddleware());
            if ($middlewareResponse !== null) {
                return $middlewareResponse;
            }
            
            // Route parametrelerini al
            $params = $route->getParameters($uri);
            
            // Handler'ı çalıştır
            $handler = $route->getHandler();
            
            if (is_callable($handler)) {
                $response = $handler(...array_values($params));
            } elseif (is_array($handler) && count($handler) === 2) {
                [$controllerClass, $method] = $handler;
                
                if (!class_exists($controllerClass)) {
                    throw new \RuntimeException("Controller class '{$controllerClass}' not found");
                }
                
                $controller = new $controllerClass();
                
                if (!method_exists($controller, $method)) {
                    throw new \RuntimeException("Method '{$method}' not found in controller '{$controllerClass}'");
                }
                
                $response = $controller->$method(...array_values($params));
            } else {
                throw new \RuntimeException('Invalid route handler');
            }
            
            // Response formatla
            return $this->formatResponse($response);
            
        } catch (\Throwable $e) {
            return $this->handleError($e);
        }
    }
    
    /**
     * Middleware'leri çalıştır
     */
    private function runMiddleware(array $middleware): ?array
    {
        foreach ($middleware as $middlewareClass) {
            if (!class_exists($middlewareClass)) {
                continue;
            }
            
            $middlewareInstance = new $middlewareClass();
            
            if (method_exists($middlewareInstance, 'handle')) {
                $result = $middlewareInstance->handle();
                
                // Middleware false döndürürse isteği durdur
                if ($result === false) {
                    return [
                        'success' => false,
                        'message' => 'Unauthorized',
                        'status' => 401
                    ];
                }
                
                // Middleware array döndürürse response olarak kullan
                if (is_array($result)) {
                    return $result;
                }
            }
        }
        
        return null;
    }
    
    /**
     * Response formatla
     */
    private function formatResponse(mixed $response): array
    {
        if (is_array($response)) {
            // Zaten formatlanmış response
            if (isset($response['success']) || isset($response['data']) || isset($response['message'])) {
                return $response;
            }
            
            // Data olarak sarmalama
            return [
                'success' => true,
                'data' => $response,
                'message' => 'İşlem başarılı'
            ];
        }
        
        if (is_string($response)) {
            return [
                'success' => true,
                'message' => $response
            ];
        }
        
        if (is_null($response)) {
            return [
                'success' => true,
                'message' => 'İşlem başarılı'
            ];
        }
        
        return [
            'success' => true,
            'data' => $response,
            'message' => 'İşlem başarılı'
        ];
    }
    
    /**
     * 404 Not Found response
     */
    private function notFound(): array
    {
        return [
            'success' => false,
            'message' => 'Endpoint bulunamadı',
            'status' => 404
        ];
    }
    
    /**
     * Hata response'u
     */
    private function handleError(\Throwable $e): array
    {
        return [
            'success' => false,
            'message' => $_ENV['APP_DEBUG'] === 'true' ? $e->getMessage() : 'Sunucu hatası',
            'status' => 500,
            'debug' => $_ENV['APP_DEBUG'] === 'true' ? [
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ] : null
        ];
    }
    
    /**
     * Tüm route'ları al
     */
    public function getRoutes(): array
    {
        return $this->routes;
    }
}
