<?php

declare(strict_types=1);

/*
 * This file is part of PHP CS Fixer.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *     <PERSON><PERSON> <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled
 * with this source code in the file LICENSE.
 */

namespace PhpCsFixer\Linter;

/**
 * Handle PHP code linting process.
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @readonly
 *
 * @internal
 */
final class Linter implements LinterInterface
{
    private LinterInterface $subLinter;

    public function __construct()
    {
        $this->subLinter = new TokenizerLinter();
    }

    public function isAsync(): bool
    {
        return $this->subLinter->isAsync();
    }

    public function lintFile(string $path): LintingResultInterface
    {
        return $this->subLinter->lintFile($path);
    }

    public function lintSource(string $source): LintingResultInterface
    {
        return $this->subLinter->lintSource($source);
    }
}
