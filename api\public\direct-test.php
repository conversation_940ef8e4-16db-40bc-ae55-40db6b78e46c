<?php

declare(strict_types=1);

// Direct response test
header('Content-Type: application/json; charset=utf-8');

// Composer autoloader
require_once dirname(__DIR__, 2) . '/vendor/autoload.php';

// Helper fonksi<PERSON><PERSON><PERSON> yü<PERSON>
require_once dirname(__DIR__) . '/core/helpers.php';

try {
    // Direkt response döndür
    $response = response([
        'status' => 'OK',
        'timestamp' => date('c'),
        'version' => '1.0.0'
    ], 'Sistem çalışıyor');
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    
} catch (\Throwable $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Direct test hatası: ' . $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ], JSON_UNESCAPED_UNICODE);
}
