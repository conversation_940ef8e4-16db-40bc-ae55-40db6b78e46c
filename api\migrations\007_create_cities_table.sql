-- Türkiye İlleri tablosu
CREATE TABLE cities (
    id TINYINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    plate_code TINYINT UNSIGNED UNIQUE NOT NULL,
    region ENUM('Mar<PERSON>', 'Ege', 'Akdeniz', 'İç Anadolu', 'Karadeniz', 'Do<PERSON><PERSON> Anadolu', 'Güneydoğu Anadolu') NOT NULL,
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    population INT UNSIGNED,
    area_km2 DECIMAL(10, 2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_name (name),
    INDEX idx_plate_code (plate_code),
    INDEX idx_region (region)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
