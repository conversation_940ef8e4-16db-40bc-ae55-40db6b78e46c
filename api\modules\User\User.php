<?php

declare(strict_types=1);

namespace EticSimple\Modules\User;

use DateTime;

/**
 * User Model
 * 
 * Kullanıcı veri modeli
 */
class User
{
    private ?int $id = null;
    private string $email;
    private string $passwordHash;
    private string $firstName;
    private string $lastName;
    private ?string $phone = null;
    private ?string $tcKimlikNo = null;
    private ?DateTime $birthDate = null;
    private ?string $gender = null;
    private string $status = 'active';
    private ?DateTime $emailVerifiedAt = null;
    private ?DateTime $phoneVerifiedAt = null;
    private ?DateTime $lastLoginAt = null;
    private int $loginCount = 0;
    private int $failedLoginAttempts = 0;
    private ?DateTime $lockedUntil = null;
    private string $preferredLanguage = 'tr';
    private string $preferredTheme = 'light';
    private bool $newsletterSubscribed = false;
    private bool $marketingEmails = true;
    private bool $twoFactorEnabled = false;
    private ?string $twoFactorSecret = null;
    private ?string $rememberToken = null;
    private ?DateTime $createdAt = null;
    private ?DateTime $updatedAt = null;
    
    public function __construct(array $data = [])
    {
        if (!empty($data)) {
            $this->fill($data);
        }
    }
    
    /**
     * Array'den model doldur
     */
    public function fill(array $data): self
    {
        foreach ($data as $key => $value) {
            $method = 'set' . str_replace('_', '', ucwords($key, '_'));
            if (method_exists($this, $method)) {
                $this->$method($value);
            }
        }
        
        return $this;
    }
    
    /**
     * Model'i array'e çevir
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'email' => $this->email,
            'first_name' => $this->firstName,
            'last_name' => $this->lastName,
            'phone' => $this->phone,
            'tc_kimlik_no' => $this->tcKimlikNo,
            'birth_date' => $this->birthDate?->format('Y-m-d'),
            'gender' => $this->gender,
            'status' => $this->status,
            'email_verified_at' => $this->emailVerifiedAt?->format('Y-m-d H:i:s'),
            'phone_verified_at' => $this->phoneVerifiedAt?->format('Y-m-d H:i:s'),
            'last_login_at' => $this->lastLoginAt?->format('Y-m-d H:i:s'),
            'login_count' => $this->loginCount,
            'failed_login_attempts' => $this->failedLoginAttempts,
            'locked_until' => $this->lockedUntil?->format('Y-m-d H:i:s'),
            'preferred_language' => $this->preferredLanguage,
            'preferred_theme' => $this->preferredTheme,
            'newsletter_subscribed' => $this->newsletterSubscribed,
            'marketing_emails' => $this->marketingEmails,
            'two_factor_enabled' => $this->twoFactorEnabled,
            'created_at' => $this->createdAt?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updatedAt?->format('Y-m-d H:i:s'),
        ];
    }
    
    /**
     * Public data (şifre ve hassas bilgiler hariç)
     */
    public function toPublicArray(): array
    {
        $data = $this->toArray();
        
        // Hassas bilgileri kaldır
        unset(
            $data['failed_login_attempts'],
            $data['locked_until'],
            $data['two_factor_secret'],
            $data['remember_token']
        );
        
        return $data;
    }
    
    /**
     * Kullanıcı aktif mi?
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }
    
    /**
     * Kullanıcı kilitli mi?
     */
    public function isLocked(): bool
    {
        return $this->lockedUntil !== null && $this->lockedUntil > new DateTime();
    }
    
    /**
     * Email doğrulanmış mı?
     */
    public function isEmailVerified(): bool
    {
        return $this->emailVerifiedAt !== null;
    }
    
    /**
     * Telefon doğrulanmış mı?
     */
    public function isPhoneVerified(): bool
    {
        return $this->phoneVerifiedAt !== null;
    }
    
    /**
     * Tam ad
     */
    public function getFullName(): string
    {
        return trim($this->firstName . ' ' . $this->lastName);
    }
    
    /**
     * Yaş hesapla
     */
    public function getAge(): ?int
    {
        if ($this->birthDate === null) {
            return null;
        }
        
        return (new DateTime())->diff($this->birthDate)->y;
    }
    
    // Getter ve Setter metodları
    
    public function getId(): ?int
    {
        return $this->id;
    }
    
    public function setId(?int $id): self
    {
        $this->id = $id;
        return $this;
    }
    
    public function getEmail(): string
    {
        return $this->email;
    }
    
    public function setEmail(string $email): self
    {
        $this->email = strtolower(trim($email));
        return $this;
    }
    
    public function getPasswordHash(): string
    {
        return $this->passwordHash;
    }
    
    public function setPasswordHash(string $passwordHash): self
    {
        $this->passwordHash = $passwordHash;
        return $this;
    }
    
    public function getFirstName(): string
    {
        return $this->firstName;
    }
    
    public function setFirstName(string $firstName): self
    {
        $this->firstName = trim($firstName);
        return $this;
    }
    
    public function getLastName(): string
    {
        return $this->lastName;
    }
    
    public function setLastName(string $lastName): self
    {
        $this->lastName = trim($lastName);
        return $this;
    }
    
    public function getPhone(): ?string
    {
        return $this->phone;
    }
    
    public function setPhone(?string $phone): self
    {
        $this->phone = $phone ? trim($phone) : null;
        return $this;
    }
    
    public function getTcKimlikNo(): ?string
    {
        return $this->tcKimlikNo;
    }
    
    public function setTcKimlikNo(?string $tcKimlikNo): self
    {
        $this->tcKimlikNo = $tcKimlikNo ? trim($tcKimlikNo) : null;
        return $this;
    }
    
    public function getBirthDate(): ?DateTime
    {
        return $this->birthDate;
    }
    
    public function setBirthDate($birthDate): self
    {
        if (is_string($birthDate)) {
            $this->birthDate = new DateTime($birthDate);
        } elseif ($birthDate instanceof DateTime) {
            $this->birthDate = $birthDate;
        } else {
            $this->birthDate = null;
        }
        return $this;
    }
    
    public function getGender(): ?string
    {
        return $this->gender;
    }
    
    public function setGender(?string $gender): self
    {
        $this->gender = $gender;
        return $this;
    }
    
    public function getStatus(): string
    {
        return $this->status;
    }
    
    public function setStatus(string $status): self
    {
        $this->status = $status;
        return $this;
    }
    
    public function getPreferredLanguage(): string
    {
        return $this->preferredLanguage;
    }
    
    public function setPreferredLanguage(string $preferredLanguage): self
    {
        $this->preferredLanguage = $preferredLanguage;
        return $this;
    }
    
    public function getPreferredTheme(): string
    {
        return $this->preferredTheme;
    }
    
    public function setPreferredTheme(string $preferredTheme): self
    {
        $this->preferredTheme = $preferredTheme;
        return $this;
    }
    
    public function isNewsletterSubscribed(): bool
    {
        return $this->newsletterSubscribed;
    }
    
    public function setNewsletterSubscribed(bool $newsletterSubscribed): self
    {
        $this->newsletterSubscribed = $newsletterSubscribed;
        return $this;
    }
    
    public function isMarketingEmails(): bool
    {
        return $this->marketingEmails;
    }
    
    public function setMarketingEmails(bool $marketingEmails): self
    {
        $this->marketingEmails = $marketingEmails;
        return $this;
    }
    
    public function isTwoFactorEnabled(): bool
    {
        return $this->twoFactorEnabled;
    }
    
    public function setTwoFactorEnabled(bool $twoFactorEnabled): self
    {
        $this->twoFactorEnabled = $twoFactorEnabled;
        return $this;
    }
    
    public function getCreatedAt(): ?DateTime
    {
        return $this->createdAt;
    }
    
    public function setCreatedAt($createdAt): self
    {
        if (is_string($createdAt)) {
            $this->createdAt = new DateTime($createdAt);
        } elseif ($createdAt instanceof DateTime) {
            $this->createdAt = $createdAt;
        }
        return $this;
    }
    
    public function getUpdatedAt(): ?DateTime
    {
        return $this->updatedAt;
    }
    
    public function setUpdatedAt($updatedAt): self
    {
        if (is_string($updatedAt)) {
            $this->updatedAt = new DateTime($updatedAt);
        } elseif ($updatedAt instanceof DateTime) {
            $this->updatedAt = $updatedAt;
        }
        return $this;
    }
}
