<?php

declare(strict_types=1);

/*
 * This file is part of PHP CS Fixer.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *     <PERSON><PERSON> <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled
 * with this source code in the file LICENSE.
 */

namespace PhpCsFixer\FixerDefinition;

/**
 * <AUTHOR> <<EMAIL>>
 */
interface FixerDefinitionInterface
{
    public function getSummary(): string;

    public function getDescription(): ?string;

    /**
     * @return null|string null for non-risky fixer
     */
    public function getRiskyDescription(): ?string;

    /**
     * Array of samples, where single sample is [code, configuration].
     *
     * @return list<CodeSampleInterface>
     */
    public function getCodeSamples(): array;
}
