/**
 * Türkiye İl/İlçe Seçici Bileşeni
 * 
 * Cascade dropdown ile dinamik il/ilçe seçimi
 */
class LocationSelector {
    constructor(options = {}) {
        this.options = {
            citySelectId: 'city-select',
            districtSelectId: 'district-select',
            apiBaseUrl: 'http://localhost:8001/api/locations',
            onCityChange: null,
            onDistrictChange: null,
            placeholder: {
                city: 'İl seçiniz...',
                district: 'İlçe seçiniz...'
            },
            ...options
        };
        
        this.citySelect = null;
        this.districtSelect = null;
        this.cities = [];
        this.districts = [];
        
        this.init();
    }
    
    /**
     * Bileşeni başlat
     */
    async init() {
        this.citySelect = document.getElementById(this.options.citySelectId);
        this.districtSelect = document.getElementById(this.options.districtSelectId);
        
        if (!this.citySelect || !this.districtSelect) {
            console.error('LocationSelector: İl veya ilçe select elementleri bulunamadı');
            return;
        }
        
        // Event listener'ları ekle
        this.citySelect.addEventListener('change', (e) => this.onCityChange(e));
        this.districtSelect.addEventListener('change', (e) => this.onDistrictChange(e));
        
        // İlleri yükle
        await this.loadCities();
        
        // İlçe select'ini devre dışı bırak
        this.districtSelect.disabled = true;
    }
    
    /**
     * İlleri API'den yükle
     */
    async loadCities() {
        try {
            const response = await fetch(`${this.options.apiBaseUrl}/cities`);
            const data = await response.json();
            
            if (data.success) {
                this.cities = data.data;
                this.populateCitySelect();
            } else {
                console.error('İller yüklenemedi:', data.message);
            }
        } catch (error) {
            console.error('İller yüklenirken hata:', error);
        }
    }
    
    /**
     * İl select'ini doldur
     */
    populateCitySelect() {
        // Mevcut seçenekleri temizle
        this.citySelect.innerHTML = '';
        
        // Placeholder ekle
        const placeholderOption = document.createElement('option');
        placeholderOption.value = '';
        placeholderOption.textContent = this.options.placeholder.city;
        placeholderOption.disabled = true;
        placeholderOption.selected = true;
        this.citySelect.appendChild(placeholderOption);
        
        // İlleri ekle
        this.cities.forEach(city => {
            const option = document.createElement('option');
            option.value = city.id;
            option.textContent = `${city.name} (${city.plate_code})`;
            option.dataset.cityName = city.name;
            option.dataset.plateCode = city.plate_code;
            this.citySelect.appendChild(option);
        });
    }
    
    /**
     * İlçe select'ini doldur
     */
    populateDistrictSelect() {
        // Mevcut seçenekleri temizle
        this.districtSelect.innerHTML = '';
        
        // Placeholder ekle
        const placeholderOption = document.createElement('option');
        placeholderOption.value = '';
        placeholderOption.textContent = this.options.placeholder.district;
        placeholderOption.disabled = true;
        placeholderOption.selected = true;
        this.districtSelect.appendChild(placeholderOption);
        
        // İlçeleri ekle
        this.districts.forEach(district => {
            const option = document.createElement('option');
            option.value = district.id;
            option.textContent = district.name;
            option.dataset.districtName = district.name;
            this.districtSelect.appendChild(option);
        });
        
        // İlçe select'ini aktif et
        this.districtSelect.disabled = false;
    }
    
    /**
     * İl değiştiğinde çalışır
     */
    async onCityChange(event) {
        const cityId = event.target.value;
        
        if (!cityId) {
            this.districtSelect.disabled = true;
            this.districtSelect.innerHTML = '';
            return;
        }
        
        // İlçeleri yükle
        await this.loadDistricts(cityId);
        
        // Callback çağır
        if (this.options.onCityChange) {
            const selectedCity = this.cities.find(city => city.id == cityId);
            this.options.onCityChange(selectedCity, event);
        }
    }
    
    /**
     * İlçe değiştiğinde çalışır
     */
    onDistrictChange(event) {
        const districtId = event.target.value;
        
        // Callback çağır
        if (this.options.onDistrictChange && districtId) {
            const selectedDistrict = this.districts.find(district => district.id == districtId);
            this.options.onDistrictChange(selectedDistrict, event);
        }
    }
    
    /**
     * Belirli bir ile ait ilçeleri yükle
     */
    async loadDistricts(cityId) {
        try {
            // Loading göster
            this.districtSelect.disabled = true;
            this.districtSelect.innerHTML = '<option>Yükleniyor...</option>';

            // Şimdilik sadece İstanbul (34) ve Ankara (6) için test endpoint'leri var
            let endpoint;
            if (cityId == 34) {
                endpoint = `${this.options.apiBaseUrl}/istanbul-districts`;
            } else if (cityId == 6) {
                endpoint = `${this.options.apiBaseUrl}/ankara-districts`;
            } else {
                // Diğer iller için henüz veri yok
                this.districtSelect.innerHTML = '<option>Bu il için ilçe verisi henüz eklenmemiş</option>';
                return;
            }

            const response = await fetch(endpoint);
            const data = await response.json();

            if (data.success) {
                this.districts = data.data.districts;
                this.populateDistrictSelect();
            } else {
                console.error('İlçeler yüklenemedi:', data.message);
                this.districtSelect.innerHTML = '<option>İlçeler yüklenemedi</option>';
            }
        } catch (error) {
            console.error('İlçeler yüklenirken hata:', error);
            this.districtSelect.innerHTML = '<option>Hata oluştu</option>';
        }
    }
    
    /**
     * Seçili değerleri getir
     */
    getSelectedValues() {
        const cityId = this.citySelect.value;
        const districtId = this.districtSelect.value;
        
        const selectedCity = cityId ? this.cities.find(city => city.id == cityId) : null;
        const selectedDistrict = districtId ? this.districts.find(district => district.id == districtId) : null;
        
        return {
            city: selectedCity,
            district: selectedDistrict,
            cityId: cityId || null,
            districtId: districtId || null
        };
    }
    
    /**
     * Değerleri programatik olarak ayarla
     */
    async setValues(cityId, districtId = null) {
        if (cityId) {
            this.citySelect.value = cityId;
            await this.loadDistricts(cityId);
            
            if (districtId) {
                this.districtSelect.value = districtId;
            }
        }
    }
    
    /**
     * Seçimleri temizle
     */
    clear() {
        this.citySelect.value = '';
        this.districtSelect.innerHTML = '';
        this.districtSelect.disabled = true;
        this.districts = [];
    }
    
    /**
     * İl arama
     */
    searchCities(query) {
        const filteredCities = this.cities.filter(city => 
            city.name.toLowerCase().includes(query.toLowerCase()) ||
            city.plate_code.toString().includes(query)
        );
        
        return filteredCities;
    }
    
    /**
     * İlçe arama
     */
    searchDistricts(query) {
        const filteredDistricts = this.districts.filter(district => 
            district.name.toLowerCase().includes(query.toLowerCase())
        );
        
        return filteredDistricts;
    }
    
    /**
     * Validation
     */
    validate() {
        const errors = [];
        
        if (!this.citySelect.value) {
            errors.push('İl seçimi zorunludur');
        }
        
        if (!this.districtSelect.value) {
            errors.push('İlçe seçimi zorunludur');
        }
        
        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }
}

/**
 * Adres formu için özel LocationSelector
 */
class AddressFormLocationSelector extends LocationSelector {
    constructor(options = {}) {
        super({
            citySelectId: 'address-city',
            districtSelectId: 'address-district',
            onCityChange: (city) => this.updatePostalCodeHint(city),
            onDistrictChange: (district) => this.updatePostalCodeHint(null, district),
            ...options
        });
        
        this.postalCodeInput = null;
        this.postalCodeHint = null;
    }
    
    async init() {
        await super.init();
        
        // Posta kodu alanlarını bul
        this.postalCodeInput = document.getElementById('address-postal-code');
        this.postalCodeHint = document.getElementById('postal-code-hint');
    }
    
    /**
     * Posta kodu ipucu güncelle
     */
    updatePostalCodeHint(city = null, district = null) {
        if (!this.postalCodeHint) return;
        
        if (district && district.postal_code_start) {
            this.postalCodeHint.textContent = 
                `Bu ilçe için posta kodu: ${district.postal_code_start} - ${district.postal_code_end}`;
            this.postalCodeHint.style.display = 'block';
        } else if (city) {
            this.postalCodeHint.textContent = 'İlçe seçtikten sonra posta kodu aralığı gösterilecek';
            this.postalCodeHint.style.display = 'block';
        } else {
            this.postalCodeHint.style.display = 'none';
        }
    }
    
    /**
     * Form verilerini getir
     */
    getFormData() {
        const selected = this.getSelectedValues();
        
        return {
            city: selected.city?.name || '',
            district: selected.district?.name || '',
            city_id: selected.cityId,
            district_id: selected.districtId,
            postal_code: this.postalCodeInput?.value || ''
        };
    }
}

// Global olarak kullanılabilir hale getir
window.LocationSelector = LocationSelector;
window.AddressFormLocationSelector = AddressFormLocationSelector;
