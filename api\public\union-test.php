<?php

declare(strict_types=1);

// Union type test
header('Content-Type: application/json; charset=utf-8');

try {
    class TestClass {
        private string|int $value;
        
        public function __construct(string|int $value) {
            $this->value = $value;
        }
        
        public function getValue(): string|int {
            return $this->value;
        }
    }
    
    $test = new TestClass("test");
    
    echo json_encode([
        'success' => true,
        'message' => 'Union types çalışıyor',
        'value' => $test->getValue(),
        'php_version' => PHP_VERSION
    ], JSON_UNESCAPED_UNICODE);
    
} catch (\Throwable $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Union type hatası: ' . $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ], JSON_UNESCAPED_UNICODE);
}
