<?php

declare(strict_types=1);

namespace EticSimple\Modules\Product;

use EticSimple\Core\Application;

/**
 * Category Controller
 * 
 * Kategori API endpoint'leri
 */
class CategoryController
{
    private CategoryRepository $categoryRepository;
    
    public function __construct()
    {
        $this->categoryRepository = new CategoryRepository();
    }
    
    /**
     * Kategori listesi
     */
    public function getCategories(): array
    {
        try {
            $activeOnly = filter_var($_GET['active_only'] ?? true, FILTER_VALIDATE_BOOLEAN);
            
            $categories = $this->categoryRepository->findAll($activeOnly);
            
            $categoriesArray = [];
            foreach ($categories as $category) {
                $categoriesArray[] = $category->toArray();
            }
            
            return response($categoriesArray, 'Kategoriler listelendi');
            
        } catch (\Exception $e) {
            Application::getInstance()->get('logger')->error('Categories listing failed', [
                'error' => $e->getMessage()
            ]);
            
            return error_response('<PERSON>goriler listelenemedi', [], 500);
        }
    }
    
    /**
     * Hiyerarşik kategori ağacı
     */
    public function getCategoryTree(): array
    {
        try {
            $activeOnly = filter_var($_GET['active_only'] ?? true, FILTER_VALIDATE_BOOLEAN);
            
            $rootCategories = $this->categoryRepository->buildTree($activeOnly);
            
            $treeArray = [];
            foreach ($rootCategories as $category) {
                $treeArray[] = $category->toHierarchicalArray();
            }
            
            return response($treeArray, 'Kategori ağacı');
            
        } catch (\Exception $e) {
            return error_response('Kategori ağacı alınamadı', [], 500);
        }
    }
    
    /**
     * Ana kategoriler
     */
    public function getRootCategories(): array
    {
        try {
            $activeOnly = filter_var($_GET['active_only'] ?? true, FILTER_VALIDATE_BOOLEAN);
            
            $categories = $this->categoryRepository->findRootCategories($activeOnly);
            
            $categoriesArray = [];
            foreach ($categories as $category) {
                $productCount = $this->categoryRepository->getProductCount($category->getId());
                $categoryData = $category->toArray();
                $categoryData['product_count'] = $productCount;
                $categoriesArray[] = $categoryData;
            }
            
            return response($categoriesArray, 'Ana kategoriler');
            
        } catch (\Exception $e) {
            return error_response('Ana kategoriler alınamadı', [], 500);
        }
    }
    
    /**
     * Alt kategoriler
     */
    public function getSubCategories(): array
    {
        try {
            $parentId = (int) ($_GET['parent_id'] ?? 0);
            
            if (!$parentId) {
                return error_response('Parent ID gerekli', [], 400);
            }
            
            $activeOnly = filter_var($_GET['active_only'] ?? true, FILTER_VALIDATE_BOOLEAN);
            
            $categories = $this->categoryRepository->findChildren($parentId, $activeOnly);
            
            $categoriesArray = [];
            foreach ($categories as $category) {
                $productCount = $this->categoryRepository->getProductCount($category->getId());
                $categoryData = $category->toArray();
                $categoryData['product_count'] = $productCount;
                $categoriesArray[] = $categoryData;
            }
            
            return response($categoriesArray, 'Alt kategoriler');
            
        } catch (\Exception $e) {
            return error_response('Alt kategoriler alınamadı', [], 500);
        }
    }
    
    /**
     * Öne çıkan kategoriler
     */
    public function getFeaturedCategories(): array
    {
        try {
            $limit = min((int) ($_GET['limit'] ?? 10), 20);
            
            $categories = $this->categoryRepository->findFeatured($limit);
            
            $categoriesArray = [];
            foreach ($categories as $category) {
                $productCount = $this->categoryRepository->getProductCount($category->getId());
                $categoryData = $category->toArray();
                $categoryData['product_count'] = $productCount;
                $categoriesArray[] = $categoryData;
            }
            
            return response($categoriesArray, 'Öne çıkan kategoriler');
            
        } catch (\Exception $e) {
            return error_response('Öne çıkan kategoriler alınamadı', [], 500);
        }
    }
    
    /**
     * Kategori detayı
     */
    public function getCategory(): array
    {
        try {
            $categoryId = (int) ($_GET['id'] ?? 0);
            $slug = $_GET['slug'] ?? '';
            
            if ($categoryId) {
                $category = $this->categoryRepository->findById($categoryId);
            } elseif ($slug) {
                $category = $this->categoryRepository->findBySlug($slug);
            } else {
                return error_response('Kategori ID veya slug gerekli', [], 400);
            }
            
            if (!$category) {
                return error_response('Kategori bulunamadı', [], 404);
            }
            
            $categoryData = $category->toArray();
            
            // Ürün sayısını ekle
            $categoryData['product_count'] = $this->categoryRepository->getProductCount($category->getId());
            
            // Alt kategorileri ekle
            $children = $this->categoryRepository->findChildren($category->getId(), true);
            $childrenArray = [];
            foreach ($children as $child) {
                $childData = $child->toArray();
                $childData['product_count'] = $this->categoryRepository->getProductCount($child->getId());
                $childrenArray[] = $childData;
            }
            $categoryData['children'] = $childrenArray;
            
            // Breadcrumb ekle
            $categoryData['breadcrumb'] = $this->categoryRepository->getBreadcrumb($category->getId());
            
            return response($categoryData, 'Kategori detayları');
            
        } catch (\Exception $e) {
            return error_response('Kategori detayları alınamadı', [], 500);
        }
    }
    
    /**
     * Kategori arama
     */
    public function searchCategories(): array
    {
        try {
            $query = trim($_GET['q'] ?? '');
            
            if (empty($query)) {
                return error_response('Arama terimi gerekli', [], 400);
            }
            
            if (strlen($query) < 2) {
                return error_response('Arama terimi en az 2 karakter olmalıdır', [], 400);
            }
            
            $limit = min((int) ($_GET['limit'] ?? 20), 50);
            
            $categories = $this->categoryRepository->search($query, $limit);
            
            $categoriesArray = [];
            foreach ($categories as $category) {
                $productCount = $this->categoryRepository->getProductCount($category->getId());
                $categoryData = $category->toArray();
                $categoryData['product_count'] = $productCount;
                $categoriesArray[] = $categoryData;
            }
            
            return response([
                'query' => $query,
                'categories' => $categoriesArray,
                'count' => count($categoriesArray)
            ], 'Kategori arama sonuçları');
            
        } catch (\Exception $e) {
            return error_response('Kategori arama başarısız', [], 500);
        }
    }
    
    /**
     * Kategori breadcrumb
     */
    public function getCategoryBreadcrumb(): array
    {
        try {
            $categoryId = (int) ($_GET['category_id'] ?? 0);
            
            if (!$categoryId) {
                return error_response('Kategori ID gerekli', [], 400);
            }
            
            $breadcrumb = $this->categoryRepository->getBreadcrumb($categoryId);
            
            return response($breadcrumb, 'Kategori breadcrumb');
            
        } catch (\Exception $e) {
            return error_response('Kategori breadcrumb alınamadı', [], 500);
        }
    }
    
    /**
     * Kategori istatistikleri
     */
    public function getCategoryStatistics(): array
    {
        try {
            // Toplam kategori sayısı
            $totalCategories = count($this->categoryRepository->findAll(false));
            $activeCategories = count($this->categoryRepository->findAll(true));
            
            // Ana kategori sayısı
            $rootCategories = count($this->categoryRepository->findRootCategories(false));
            
            // Öne çıkan kategori sayısı
            $featuredCategories = count($this->categoryRepository->findFeatured(100));
            
            return response([
                'total_categories' => $totalCategories,
                'active_categories' => $activeCategories,
                'root_categories' => $rootCategories,
                'featured_categories' => $featuredCategories
            ], 'Kategori istatistikleri');
            
        } catch (\Exception $e) {
            return error_response('Kategori istatistikleri alınamadı', [], 500);
        }
    }
}
