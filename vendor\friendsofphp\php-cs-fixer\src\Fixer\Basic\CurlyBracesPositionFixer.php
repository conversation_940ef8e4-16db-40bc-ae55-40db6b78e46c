<?php

declare(strict_types=1);

/*
 * This file is part of PHP CS Fixer.
 *
 * (c) <PERSON><PERSON><PERSON>cier <<EMAIL>>
 *     <PERSON><PERSON> <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled
 * with this source code in the file LICENSE.
 */

namespace PhpCsFixer\Fixer\Basic;

use PhpCs<PERSON><PERSON><PERSON>\AbstractProxyFixer;
use PhpCsFixer\Fixer\ConfigurableFixerInterface;
use PhpCsFixer\Fixer\ConfigurableFixerTrait;
use PhpCsFixer\Fixer\DeprecatedFixerInterface;
use Php<PERSON>F<PERSON><PERSON>\Fixer\Indentation;
use PhpCsFixer\Fixer\WhitespacesAwareFixerInterface;
use PhpCsFixer\FixerConfiguration\FixerConfigurationResolverInterface;
use PhpCsFixer\FixerDefinition\FixerDefinition;
use <PERSON>p<PERSON>F<PERSON><PERSON>\FixerDefinition\FixerDefinitionInterface;

/**
 * @deprecated
 *
 * @implements ConfigurableFixerInterface<_AutogeneratedInputConfiguration, _AutogeneratedComputedConfiguration>
 *
 * @phpstan-type _AutogeneratedInputConfiguration array{
 *  allow_single_line_anonymous_functions?: bool,
 *  allow_single_line_empty_anonymous_classes?: bool,
 *  anonymous_classes_opening_brace?: 'next_line_unless_newline_at_signature_end'|'same_line',
 *  anonymous_functions_opening_brace?: 'next_line_unless_newline_at_signature_end'|'same_line',
 *  classes_opening_brace?: 'next_line_unless_newline_at_signature_end'|'same_line',
 *  control_structures_opening_brace?: 'next_line_unless_newline_at_signature_end'|'same_line',
 *  functions_opening_brace?: 'next_line_unless_newline_at_signature_end'|'same_line',
 * }
 * @phpstan-type _AutogeneratedComputedConfiguration array{
 *  allow_single_line_anonymous_functions: bool,
 *  allow_single_line_empty_anonymous_classes: bool,
 *  anonymous_classes_opening_brace: 'next_line_unless_newline_at_signature_end'|'same_line',
 *  anonymous_functions_opening_brace: 'next_line_unless_newline_at_signature_end'|'same_line',
 *  classes_opening_brace: 'next_line_unless_newline_at_signature_end'|'same_line',
 *  control_structures_opening_brace: 'next_line_unless_newline_at_signature_end'|'same_line',
 *  functions_opening_brace: 'next_line_unless_newline_at_signature_end'|'same_line',
 * }
 */
final class CurlyBracesPositionFixer extends AbstractProxyFixer implements ConfigurableFixerInterface, DeprecatedFixerInterface, WhitespacesAwareFixerInterface
{
    /** @use ConfigurableFixerTrait<_AutogeneratedInputConfiguration, _AutogeneratedComputedConfiguration> */
    use ConfigurableFixerTrait;

    use Indentation;

    /**
     * @internal
     */
    public const NEXT_LINE_UNLESS_NEWLINE_AT_SIGNATURE_END = 'next_line_unless_newline_at_signature_end';

    /**
     * @internal
     */
    public const SAME_LINE = 'same_line';

    private BracesPositionFixer $bracesPositionFixer;

    public function __construct()
    {
        $this->bracesPositionFixer = new BracesPositionFixer();

        parent::__construct();
    }

    public function getDefinition(): FixerDefinitionInterface
    {
        $fixerDefinition = $this->bracesPositionFixer->getDefinition();

        return new FixerDefinition(
            'Curly braces must be placed as configured.',
            $fixerDefinition->getCodeSamples(),
            $fixerDefinition->getDescription(),
            $fixerDefinition->getRiskyDescription()
        );
    }

    /**
     * {@inheritdoc}
     *
     * Must run before SingleLineEmptyBodyFixer, StatementIndentationFixer.
     * Must run after ControlStructureBracesFixer, NoMultipleStatementsPerLineFixer.
     */
    public function getPriority(): int
    {
        return $this->bracesPositionFixer->getPriority();
    }

    public function getSuccessorsNames(): array
    {
        return [
            $this->bracesPositionFixer->getName(),
        ];
    }

    /**
     * @param _AutogeneratedInputConfiguration $configuration
     */
    protected function configurePreNormalisation(array $configuration): void
    {
        $this->bracesPositionFixer->configure($configuration);
    }

    protected function createProxyFixers(): array
    {
        return [
            $this->bracesPositionFixer,
        ];
    }

    protected function createConfigurationDefinition(): FixerConfigurationResolverInterface
    {
        return $this->bracesPositionFixer->createConfigurationDefinition();
    }
}
