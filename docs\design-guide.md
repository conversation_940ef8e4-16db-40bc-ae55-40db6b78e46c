# EticSimple E-Ticaret Sistemi - Tasarım Rehberi

## 🎨 Genel Tasarım Felsefesi
EticSimple, modern, kullanıcı dostu ve Türkiye pazarına özel tasarlanmış bir e-ticaret platformudur. Tasarım yaklaşımımız:
- **Minimalist ve temiz** görünüm
- **Mobile-first** responsive tasarım
- **Accessibility** (erişilebilirlik) odaklı
- **Türk kullanıcı alışkanlıklarına** uygun
- **Global standartlara** uyumlu

---

## 🎯 Renk Paleti

### Ana Renk <PERSON>leti
```css
:root {
  /* Primary Colors */
  --primary-50: #f0f9ff;
  --primary-100: #e0f2fe;
  --primary-200: #bae6fd;
  --primary-300: #7dd3fc;
  --primary-400: #38bdf8;
  --primary-500: #0ea5e9;  /* Ana marka rengi */
  --primary-600: #0284c7;
  --primary-700: #0369a1;
  --primary-800: #075985;
  --primary-900: #0c4a6e;

  /* Secondary Colors */
  --secondary-50: #fdf4ff;
  --secondary-100: #fae8ff;
  --secondary-200: #f5d0fe;
  --secondary-300: #f0abfc;
  --secondary-400: #e879f9;
  --secondary-500: #d946ef;  /* İkincil renk */
  --secondary-600: #c026d3;
  --secondary-700: #a21caf;
  --secondary-800: #86198f;
  --secondary-900: #701a75;
}
```

### Nötr Renk Paleti
```css
:root {
  /* Gray Scale */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* White & Black */
  --white: #ffffff;
  --black: #000000;
}
```

### Durum Renkleri
```css
:root {
  /* Success */
  --success-50: #f0fdf4;
  --success-500: #22c55e;
  --success-600: #16a34a;

  /* Warning */
  --warning-50: #fffbeb;
  --warning-500: #f59e0b;
  --warning-600: #d97706;

  /* Error */
  --error-50: #fef2f2;
  --error-500: #ef4444;
  --error-600: #dc2626;

  /* Info */
  --info-50: #eff6ff;
  --info-500: #3b82f6;
  --info-600: #2563eb;
}
```

---

## 📝 Tipografi

### Font Aileleri
```css
:root {
  /* Ana font - Türkçe karakterler için optimize */
  --font-primary: 'Inter', 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  
  /* Başlıklar için */
  --font-heading: 'Poppins', 'Inter', sans-serif;
  
  /* Kod ve monospace */
  --font-mono: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;
  
  /* Türkçe özel karakterler için fallback */
  --font-turkish: 'Inter', 'Source Sans Pro', 'Noto Sans', sans-serif;
}
```

### Font Boyutları ve Ağırlıkları
```css
:root {
  /* Font Sizes */
  --text-xs: 0.75rem;    /* 12px */
  --text-sm: 0.875rem;   /* 14px */
  --text-base: 1rem;     /* 16px */
  --text-lg: 1.125rem;   /* 18px */
  --text-xl: 1.25rem;    /* 20px */
  --text-2xl: 1.5rem;    /* 24px */
  --text-3xl: 1.875rem;  /* 30px */
  --text-4xl: 2.25rem;   /* 36px */
  --text-5xl: 3rem;      /* 48px */

  /* Font Weights */
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;

  /* Line Heights */
  --leading-tight: 1.25;
  --leading-normal: 1.5;
  --leading-relaxed: 1.75;
}
```

---

## 📐 Spacing ve Layout

### Spacing Sistemi
```css
:root {
  /* Spacing Scale (8px base) */
  --space-1: 0.25rem;   /* 4px */
  --space-2: 0.5rem;    /* 8px */
  --space-3: 0.75rem;   /* 12px */
  --space-4: 1rem;      /* 16px */
  --space-5: 1.25rem;   /* 20px */
  --space-6: 1.5rem;    /* 24px */
  --space-8: 2rem;      /* 32px */
  --space-10: 2.5rem;   /* 40px */
  --space-12: 3rem;     /* 48px */
  --space-16: 4rem;     /* 64px */
  --space-20: 5rem;     /* 80px */
  --space-24: 6rem;     /* 96px */
}
```

### Container ve Grid
```css
:root {
  /* Container Sizes */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
  --container-2xl: 1536px;

  /* Grid Columns */
  --grid-cols-12: repeat(12, minmax(0, 1fr));
  --grid-cols-6: repeat(6, minmax(0, 1fr));
  --grid-cols-4: repeat(4, minmax(0, 1fr));
  --grid-cols-3: repeat(3, minmax(0, 1fr));
  --grid-cols-2: repeat(2, minmax(0, 1fr));
}
```

---

## 🎭 Bileşen Tasarım Sistemi

### Butonlar
```css
/* Primary Button */
.btn-primary {
  background: var(--primary-500);
  color: var(--white);
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-md);
  font-weight: var(--font-medium);
  transition: all 0.2s ease;
}

.btn-primary:hover {
  background: var(--primary-600);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(14, 165, 233, 0.3);
}

/* Secondary Button */
.btn-secondary {
  background: transparent;
  color: var(--primary-500);
  border: 2px solid var(--primary-500);
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-md);
}
```

### Kartlar
```css
.card {
  background: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: var(--space-6);
  transition: all 0.3s ease;
}

.card:hover {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}
```

### Form Elemanları
```css
.input {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-md);
  font-size: var(--text-base);
  transition: border-color 0.2s ease;
}

.input:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}
```

---

## 📱 Responsive Breakpoints

```css
:root {
  /* Mobile First Breakpoints */
  --bp-sm: 640px;   /* Küçük tablet */
  --bp-md: 768px;   /* Tablet */
  --bp-lg: 1024px;  /* Küçük desktop */
  --bp-xl: 1280px;  /* Desktop */
  --bp-2xl: 1536px; /* Büyük desktop */
}

/* Media Query Mixins */
@media (min-width: 640px) { /* sm */ }
@media (min-width: 768px) { /* md */ }
@media (min-width: 1024px) { /* lg */ }
@media (min-width: 1280px) { /* xl */ }
@media (min-width: 1536px) { /* 2xl */ }
```

---

## 🎨 Tema Sistemi

### Light Theme (Varsayılan)
```css
[data-theme="light"] {
  --bg-primary: var(--white);
  --bg-secondary: var(--gray-50);
  --text-primary: var(--gray-900);
  --text-secondary: var(--gray-600);
  --border-color: var(--gray-200);
}
```

### Dark Theme
```css
[data-theme="dark"] {
  --bg-primary: var(--gray-900);
  --bg-secondary: var(--gray-800);
  --text-primary: var(--white);
  --text-secondary: var(--gray-300);
  --border-color: var(--gray-700);
}
```

---

## 🌐 Türkiye Özel Tasarım Kılavuzları

### Para Birimi Gösterimi
- Türk Lirası sembolü: **₺** (Unicode: U+20BA)
- Format: `1.234,56 ₺` (Türkçe format)
- Binlik ayırıcı: nokta (.)
- Ondalık ayırıcı: virgül (,)

### Tarih ve Saat Formatları
- Tarih: `DD.MM.YYYY` (örn: 15.06.2025)
- Saat: `HH:MM` (24 saat formatı)
- Tam format: `DD.MM.YYYY HH:MM`

### Telefon Numarası Formatı
- Format: `+90 (5XX) XXX XX XX`
- Placeholder: `+90 (5XX) XXX XX XX`

---

## ♿ Erişilebilirlik (Accessibility)

### Renk Kontrastı
- Normal metin: minimum 4.5:1 kontrast oranı
- Büyük metin: minimum 3:1 kontrast oranı
- UI bileşenleri: minimum 3:1 kontrast oranı

### Klavye Navigasyonu
- Tüm interaktif elemanlar Tab ile erişilebilir
- Focus göstergeleri açık ve belirgin
- Skip links ana içeriğe geçiş için

### Screen Reader Desteği
- Semantic HTML kullanımı
- ARIA labels ve descriptions
- Alt text tüm görseller için

---

*Bu tasarım rehberi proje gelişimi sürecinde güncellenecektir.*
