{"name": "friendsofphp/php-cs-fixer", "description": "A tool to automatically fix PHP code style", "license": "MIT", "type": "application", "keywords": ["fixer", "standards", "static analysis", "static code analysis"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": "^7.4 || ^8.0", "ext-filter": "*", "ext-hash": "*", "ext-json": "*", "ext-tokenizer": "*", "clue/ndjson-react": "^1.0", "composer/semver": "^3.4", "composer/xdebug-handler": "^3.0.3", "fidry/cpu-core-counter": "^1.2", "react/child-process": "^0.6.5", "react/event-loop": "^1.0", "react/promise": "^2.0 || ^3.0", "react/socket": "^1.0", "react/stream": "^1.0", "sebastian/diff": "^4.0 || ^5.1 || ^6.0 || ^7.0", "symfony/console": "^5.4 || ^6.4 || ^7.0", "symfony/event-dispatcher": "^5.4 || ^6.4 || ^7.0", "symfony/filesystem": "^5.4 || ^6.4 || ^7.0", "symfony/finder": "^5.4 || ^6.4 || ^7.0", "symfony/options-resolver": "^5.4 || ^6.4 || ^7.0", "symfony/polyfill-mbstring": "^1.31", "symfony/polyfill-php80": "^1.31", "symfony/polyfill-php81": "^1.31", "symfony/process": "^5.4 || ^6.4 || ^7.2", "symfony/stopwatch": "^5.4 || ^6.4 || ^7.0"}, "require-dev": {"facile-it/paraunit": "^1.3.1 || ^2.6", "infection/infection": "^0.29.14", "justinrainbow/json-schema": "^5.3 || ^6.2", "keradus/cli-executor": "^2.1", "mikey179/vfsstream": "^1.6.12", "php-coveralls/php-coveralls": "^2.7", "php-cs-fixer/accessible-object": "^1.1", "php-cs-fixer/phpunit-constraint-isidenticalstring": "^1.6", "php-cs-fixer/phpunit-constraint-xmlmatchesxsd": "^1.6", "phpunit/phpunit": "^9.6.22 || ^10.5.45 || ^11.5.12", "symfony/var-dumper": "^5.4.48 || ^6.4.18 || ^7.2.3", "symfony/yaml": "^5.4.45 || ^6.4.18 || ^7.2.3"}, "suggest": {"ext-dom": "For handling output formats in XML", "ext-mbstring": "For handling non-UTF8 characters."}, "autoload": {"psr-4": {"PhpCsFixer\\": "src/"}, "exclude-from-classmap": ["src/Fixer/Internal/*"]}, "autoload-dev": {"psr-4": {"PhpCsFixer\\PHPStan\\": "dev-tools/phpstan/src/", "PhpCsFixer\\Tests\\": "tests/"}, "exclude-from-classmap": ["tests/Fixtures/"]}, "bin": ["php-cs-fixer"], "config": {"allow-plugins": {"ergebnis/composer-normalize": true, "infection/extension-installer": false}, "prefer-stable": true, "sort-packages": true}, "scripts": {"post-autoload-dump": ["@install-tools"], "auto-review": ["Composer\\Config::disableProcessTimeout", "paraunit run --testsuite auto-review"], "cs:check": "@php php-cs-fixer check --verbose --diff", "cs:fix": "@php php-cs-fixer fix", "cs:fix:parallel": ["echo '⚠️ This script is deprecated! Utilise built-in parallelisation instead.';", "@cs:fix"], "docs": "@php dev-tools/doc.php", "infection": "@test:mutation", "install-tools": "@composer --working-dir=dev-tools install", "mess-detector": "@php dev-tools/vendor/bin/phpmd . ansi dev-tools/mess-detector/phpmd.xml --exclude vendor/*,dev-tools/vendor/*,dev-tools/phpstan/*,tests/Fixtures/*", "normalize": ["@composer normalize --working-dir=dev-tools --dry-run ../composer.json", "@composer normalize --working-dir=dev-tools --dry-run composer.json"], "normalize:fix": ["@composer normalize --working-dir=dev-tools ../composer.json", "@composer normalize --working-dir=dev-tools composer.json"], "php-compatibility": "@php dev-tools/vendor/bin/phpcs -p --standard=dev-tools/php-compatibility/phpcs-php-compatibility.xml", "phpstan": "@php -d memory_limit=512M dev-tools/vendor/bin/phpstan analyse", "phpstan:baseline": "@php -d memory_limit=512M dev-tools/vendor/bin/phpstan analyse --generate-baseline=./dev-tools/phpstan/baseline.php", "qa": "@quality-assurance", "quality-assurance": ["Composer\\Config::disableProcessTimeout", "@install-tools --quiet", "@self-check", "@static-analysis", "@test"], "require-checker": "@php dev-tools/vendor/bin/composer-require-checker check composer.json --config-file .composer-require-checker.json", "sa": "@static-analysis", "self-check": ["./dev-tools/check_file_permissions.sh", "./dev-tools/check_trailing_spaces.sh", "@composer dump-autoload --dry-run --optimize --strict-psr", "@normalize", "@unused-deps", "@require-checker", "@auto-review"], "static-analysis": ["@cs:check", "@phpstan", "@mess-detector"], "test": "@test:all", "test:all": ["@test:unit", "@test:integration"], "test:coverage": ["Composer\\Config::disableProcessTimeout", "@composer show facile-it/paraunit ^2 && (paraunit coverage --testsuite unit --pass-through=--exclude-group=covers-nothing) || (paraunit coverage --testsuite unit --exclude-group covers-nothing)"], "test:integration": ["Composer\\Config::disableProcessTimeout", "paraunit run --testsuite integration"], "test:mutation": ["Composer\\Config::disableProcessTimeout", "infection --threads=max --only-covered --min-covered-msi=80"], "test:short-open-tag": ["Composer\\Config::disableProcessTimeout", "@php -d short_open_tag=1 ./vendor/bin/phpunit --do-not-cache-result --testsuite short-open-tag"], "test:smoke": ["Composer\\Config::disableProcessTimeout", "paraunit run --testsuite smoke"], "test:unit": ["Composer\\Config::disableProcessTimeout", "paraunit run --testsuite unit"], "unused-deps": "@php dev-tools/vendor/bin/composer-unused --excludePackage=composer/xdebug-handler"}, "scripts-descriptions": {"auto-review": "Execute Auto-review", "cs:check": "Check coding standards", "cs:fix": "Fix coding standards", "cs:fix:parallel": "⚠️DEPRECATED! Use cs:fix with proper parallel config", "docs": "Regenerate docs", "infection": "Alias for 'test:mutation'", "install-tools": "Install DEV tools", "mess-detector": "Analyse code with Mess Detector", "normalize": "Check normalization for composer.json files", "normalize:fix": "Run normalization for composer.json files", "php-compatibility": "Check compatibility with all supported PHP versions", "phpstan": "Run PHPStan analysis", "phpstan:baseline": "Dump PHPStan baseline file - use only for updating, do not add new errors when possible", "post-autoload-dump": "Run additional tasks after installing/updating main dependencies", "qa": "Alias for 'quality-assurance'", "quality-assurance": "Run QA suite", "require-checker": "Verifies if codebase does not contain soft dependencies", "sa": "Alias for 'static-analysis'", "self-check": "Run set of self-checks ensuring repository's validity", "static-analysis": "Run static analysis", "test": "<PERSON><PERSON> for 'test:all'", "test:all": "Run Unit and Integration tests (but *NOT* Smoke tests)", "test:coverage": "Run tests that provide code coverage", "test:integration": "Run Integration tests", "test:mutation": "Run mutation tests", "test:short-open-tag": "Run tests with \"short_open_tag\" enabled", "test:smoke": "Run Smoke tests", "test:unit": "Run Unit tests", "unused-deps": "Verifies if app has dependencies that are not used"}}