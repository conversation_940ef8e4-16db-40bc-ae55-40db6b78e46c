<?php

declare(strict_types=1);

namespace EticSimple\Modules\User;

use DateTime;

/**
 * UserAddress Model
 * 
 * Kullanıcı adres veri modeli
 */
class UserAddress
{
    private ?int $id = null;
    private int $userId;
    private string $title;
    private string $firstName;
    private string $lastName;
    private ?string $company = null;
    private string $addressLine1;
    private ?string $addressLine2 = null;
    private string $city;
    private string $district;
    private ?string $neighborhood = null;
    private string $postalCode;
    private string $country = 'TR';
    private ?string $phone = null;
    private bool $isDefault = false;
    private string $type = 'both';
    private ?DateTime $createdAt = null;
    private ?DateTime $updatedAt = null;
    
    public function __construct(array $data = [])
    {
        if (!empty($data)) {
            $this->fill($data);
        }
    }
    
    /**
     * Array'den model doldur
     */
    public function fill(array $data): self
    {
        foreach ($data as $key => $value) {
            $method = 'set' . str_replace('_', '', ucwords($key, '_'));
            if (method_exists($this, $method)) {
                $this->$method($value);
            }
        }
        
        return $this;
    }
    
    /**
     * Model'i array'e çevir
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'user_id' => $this->userId,
            'title' => $this->title,
            'first_name' => $this->firstName,
            'last_name' => $this->lastName,
            'company' => $this->company,
            'address_line_1' => $this->addressLine1,
            'address_line_2' => $this->addressLine2,
            'city' => $this->city,
            'district' => $this->district,
            'neighborhood' => $this->neighborhood,
            'postal_code' => $this->postalCode,
            'country' => $this->country,
            'phone' => $this->phone,
            'is_default' => $this->isDefault,
            'type' => $this->type,
            'created_at' => $this->createdAt?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updatedAt?->format('Y-m-d H:i:s'),
        ];
    }
    
    /**
     * Tam ad
     */
    public function getFullName(): string
    {
        return trim($this->firstName . ' ' . $this->lastName);
    }
    
    /**
     * Tam adres
     */
    public function getFullAddress(): string
    {
        $address = $this->addressLine1;
        
        if ($this->addressLine2) {
            $address .= ', ' . $this->addressLine2;
        }
        
        if ($this->neighborhood) {
            $address .= ', ' . $this->neighborhood;
        }
        
        $address .= ', ' . $this->district . '/' . $this->city;
        
        if ($this->postalCode) {
            $address .= ' ' . $this->postalCode;
        }
        
        return $address;
    }
    
    /**
     * Adres formatlanmış (Türkiye formatı)
     */
    public function getFormattedAddress(): array
    {
        return [
            'name' => $this->getFullName(),
            'company' => $this->company,
            'address_line_1' => $this->addressLine1,
            'address_line_2' => $this->addressLine2,
            'neighborhood' => $this->neighborhood,
            'district' => $this->district,
            'city' => $this->city,
            'postal_code' => $this->postalCode,
            'country' => $this->country,
            'phone' => $this->phone,
        ];
    }
    
    // Getter ve Setter metodları
    
    public function getId(): ?int
    {
        return $this->id;
    }
    
    public function setId(?int $id): self
    {
        $this->id = $id;
        return $this;
    }
    
    public function getUserId(): int
    {
        return $this->userId;
    }
    
    public function setUserId(int $userId): self
    {
        $this->userId = $userId;
        return $this;
    }
    
    public function getTitle(): string
    {
        return $this->title;
    }
    
    public function setTitle(string $title): self
    {
        $this->title = trim($title);
        return $this;
    }
    
    public function getFirstName(): string
    {
        return $this->firstName;
    }
    
    public function setFirstName(string $firstName): self
    {
        $this->firstName = trim($firstName);
        return $this;
    }
    
    public function getLastName(): string
    {
        return $this->lastName;
    }
    
    public function setLastName(string $lastName): self
    {
        $this->lastName = trim($lastName);
        return $this;
    }
    
    public function getCompany(): ?string
    {
        return $this->company;
    }
    
    public function setCompany(?string $company): self
    {
        $this->company = $company ? trim($company) : null;
        return $this;
    }
    
    public function getAddressLine1(): string
    {
        return $this->addressLine1;
    }
    
    public function setAddressLine1(string $addressLine1): self
    {
        $this->addressLine1 = trim($addressLine1);
        return $this;
    }
    
    public function getAddressLine2(): ?string
    {
        return $this->addressLine2;
    }
    
    public function setAddressLine2(?string $addressLine2): self
    {
        $this->addressLine2 = $addressLine2 ? trim($addressLine2) : null;
        return $this;
    }
    
    public function getCity(): string
    {
        return $this->city;
    }
    
    public function setCity(string $city): self
    {
        $this->city = trim($city);
        return $this;
    }
    
    public function getDistrict(): string
    {
        return $this->district;
    }
    
    public function setDistrict(string $district): self
    {
        $this->district = trim($district);
        return $this;
    }
    
    public function getNeighborhood(): ?string
    {
        return $this->neighborhood;
    }
    
    public function setNeighborhood(?string $neighborhood): self
    {
        $this->neighborhood = $neighborhood ? trim($neighborhood) : null;
        return $this;
    }
    
    public function getPostalCode(): string
    {
        return $this->postalCode;
    }
    
    public function setPostalCode(string $postalCode): self
    {
        $this->postalCode = trim($postalCode);
        return $this;
    }
    
    public function getCountry(): string
    {
        return $this->country;
    }
    
    public function setCountry(string $country): self
    {
        $this->country = strtoupper(trim($country));
        return $this;
    }
    
    public function getPhone(): ?string
    {
        return $this->phone;
    }
    
    public function setPhone(?string $phone): self
    {
        $this->phone = $phone ? trim($phone) : null;
        return $this;
    }
    
    public function isDefault(): bool
    {
        return $this->isDefault;
    }
    
    public function setIsDefault(bool $isDefault): self
    {
        $this->isDefault = $isDefault;
        return $this;
    }
    
    public function getType(): string
    {
        return $this->type;
    }
    
    public function setType(string $type): self
    {
        $this->type = $type;
        return $this;
    }
    
    public function getCreatedAt(): ?DateTime
    {
        return $this->createdAt;
    }
    
    public function setCreatedAt($createdAt): self
    {
        if (is_string($createdAt)) {
            $this->createdAt = new DateTime($createdAt);
        } elseif ($createdAt instanceof DateTime) {
            $this->createdAt = $createdAt;
        }
        return $this;
    }
    
    public function getUpdatedAt(): ?DateTime
    {
        return $this->updatedAt;
    }
    
    public function setUpdatedAt($updatedAt): self
    {
        if (is_string($updatedAt)) {
            $this->updatedAt = new DateTime($updatedAt);
        } elseif ($updatedAt instanceof DateTime) {
            $this->updatedAt = $updatedAt;
        }
        return $this;
    }
}
