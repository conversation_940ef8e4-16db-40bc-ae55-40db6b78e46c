-- Türkiye İlçeleri tablosu
CREATE TABLE districts (
    id SMALLINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    city_id TINYINT UNSIGNED NOT NULL,
    name VARCHAR(100) NOT NULL,
    population INT UNSIGNED,
    area_km2 DECIMAL(10, 2),
    postal_code_start VARCHAR(5),
    postal_code_end VARCHAR(5),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (city_id) REFERENCES cities(id) ON DELETE CASCADE,
    INDEX idx_city_id (city_id),
    INDEX idx_name (name),
    INDEX idx_city_name (city_id, name),
    INDEX idx_postal_code (postal_code_start, postal_code_end)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
