<?php

declare(strict_types=1);

// Basit test endpoint
header('Content-Type: application/json; charset=utf-8');

try {
    // Composer autoloader
    require_once dirname(__DIR__, 2) . '/vendor/autoload.php';
    
    echo json_encode([
        'success' => true,
        'message' => 'Test endpoint çalışıyor',
        'data' => [
            'php_version' => PHP_VERSION,
            'timestamp' => date('c'),
            'autoloader' => 'OK'
        ]
    ], JSON_UNESCAPED_UNICODE);
    
} catch (\Throwable $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Hata: ' . $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ], JSON_UNESCAPED_UNICODE);
}
