<?php

declare(strict_types=1);

namespace EticSimple\Core\Database;

use PDO;
use PDOException;
use PDOStatement;

/**
 * Veritabanı Yönetim Sistemi
 * 
 * PDO tabanlı veritabanı bağlantı ve sorgu yönetimi
 */
class DatabaseManager
{
    private ?PDO $connection = null;
    private array $config;
    private array $queryLog = [];
    
    public function __construct(array $config)
    {
        $this->config = $config;
    }
    
    /**
     * Veritabanı bağlantısı al
     */
    public function getConnection(): PDO
    {
        if ($this->connection === null) {
            $this->connect();
        }
        
        return $this->connection;
    }
    
    /**
     * Veritabanına bağlan
     */
    private function connect(): void
    {
        try {
            $dsn = sprintf(
                'mysql:host=%s;port=%d;dbname=%s;charset=%s',
                $this->config['host'],
                $this->config['port'],
                $this->config['database'],
                $this->config['charset']
            );
            
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$this->config['charset']} COLLATE {$this->config['collation']}"
            ];
            
            $this->connection = new PDO(
                $dsn,
                $this->config['username'],
                $this->config['password'],
                $options
            );
            
        } catch (PDOException $e) {
            throw new \RuntimeException('Database connection failed: ' . $e->getMessage());
        }
    }
    
    /**
     * SQL sorgusu çalıştır
     */
    public function execute(string $sql, array $params = []): bool
    {
        $startTime = microtime(true);
        
        try {
            $stmt = $this->getConnection()->prepare($sql);
            $result = $stmt->execute($params);
            
            $this->logQuery($sql, $params, microtime(true) - $startTime);
            
            return $result;
            
        } catch (PDOException $e) {
            $this->logQuery($sql, $params, microtime(true) - $startTime, $e->getMessage());
            throw new \RuntimeException('Query execution failed: ' . $e->getMessage());
        }
    }
    
    /**
     * Tek satır getir
     */
    public function fetchOne(string $sql, array $params = []): ?array
    {
        $startTime = microtime(true);
        
        try {
            $stmt = $this->getConnection()->prepare($sql);
            $stmt->execute($params);
            $result = $stmt->fetch();
            
            $this->logQuery($sql, $params, microtime(true) - $startTime);
            
            return $result ?: null;
            
        } catch (PDOException $e) {
            $this->logQuery($sql, $params, microtime(true) - $startTime, $e->getMessage());
            throw new \RuntimeException('Query execution failed: ' . $e->getMessage());
        }
    }
    
    /**
     * Çoklu satır getir
     */
    public function fetchAll(string $sql, array $params = []): array
    {
        $startTime = microtime(true);
        
        try {
            $stmt = $this->getConnection()->prepare($sql);
            $stmt->execute($params);
            $result = $stmt->fetchAll();
            
            $this->logQuery($sql, $params, microtime(true) - $startTime);
            
            return $result;
            
        } catch (PDOException $e) {
            $this->logQuery($sql, $params, microtime(true) - $startTime, $e->getMessage());
            throw new \RuntimeException('Query execution failed: ' . $e->getMessage());
        }
    }
    
    /**
     * Tek değer getir
     */
    public function fetchColumn(string $sql, array $params = []): mixed
    {
        $startTime = microtime(true);
        
        try {
            $stmt = $this->getConnection()->prepare($sql);
            $stmt->execute($params);
            $result = $stmt->fetchColumn();
            
            $this->logQuery($sql, $params, microtime(true) - $startTime);
            
            return $result;
            
        } catch (PDOException $e) {
            $this->logQuery($sql, $params, microtime(true) - $startTime, $e->getMessage());
            throw new \RuntimeException('Query execution failed: ' . $e->getMessage());
        }
    }
    
    /**
     * Insert işlemi ve son eklenen ID'yi döndür
     */
    public function insert(string $table, array $data): int
    {
        $columns = array_keys($data);
        $placeholders = array_fill(0, count($columns), '?');
        
        $sql = sprintf(
            'INSERT INTO %s (%s) VALUES (%s)',
            $table,
            implode(', ', $columns),
            implode(', ', $placeholders)
        );
        
        $this->execute($sql, array_values($data));
        
        return (int) $this->getConnection()->lastInsertId();
    }
    
    /**
     * Update işlemi
     */
    public function update(string $table, array $data, array $where): int
    {
        $setParts = [];
        $params = [];
        
        foreach ($data as $column => $value) {
            $setParts[] = "{$column} = ?";
            $params[] = $value;
        }
        
        $whereParts = [];
        foreach ($where as $column => $value) {
            $whereParts[] = "{$column} = ?";
            $params[] = $value;
        }
        
        $sql = sprintf(
            'UPDATE %s SET %s WHERE %s',
            $table,
            implode(', ', $setParts),
            implode(' AND ', $whereParts)
        );
        
        $stmt = $this->getConnection()->prepare($sql);
        $stmt->execute($params);
        
        return $stmt->rowCount();
    }
    
    /**
     * Delete işlemi
     */
    public function delete(string $table, array $where): int
    {
        $whereParts = [];
        $params = [];
        
        foreach ($where as $column => $value) {
            $whereParts[] = "{$column} = ?";
            $params[] = $value;
        }
        
        $sql = sprintf(
            'DELETE FROM %s WHERE %s',
            $table,
            implode(' AND ', $whereParts)
        );
        
        $stmt = $this->getConnection()->prepare($sql);
        $stmt->execute($params);
        
        return $stmt->rowCount();
    }
    
    /**
     * Transaction başlat
     */
    public function beginTransaction(): bool
    {
        return $this->getConnection()->beginTransaction();
    }
    
    /**
     * Transaction commit
     */
    public function commit(): bool
    {
        return $this->getConnection()->commit();
    }
    
    /**
     * Transaction rollback
     */
    public function rollback(): bool
    {
        return $this->getConnection()->rollBack();
    }
    
    /**
     * Transaction içinde işlem yap
     */
    public function transaction(callable $callback): mixed
    {
        $this->beginTransaction();
        
        try {
            $result = $callback($this);
            $this->commit();
            return $result;
            
        } catch (\Throwable $e) {
            $this->rollback();
            throw $e;
        }
    }
    
    /**
     * Sorgu logla
     */
    private function logQuery(string $sql, array $params, float $executionTime, ?string $error = null): void
    {
        if (!($_ENV['QUERY_LOG_ENABLED'] ?? false)) {
            return;
        }
        
        $this->queryLog[] = [
            'sql' => $sql,
            'params' => $params,
            'execution_time' => $executionTime,
            'error' => $error,
            'timestamp' => microtime(true)
        ];
    }
    
    /**
     * Sorgu logunu al
     */
    public function getQueryLog(): array
    {
        return $this->queryLog;
    }
    
    /**
     * Sorgu logunu temizle
     */
    public function clearQueryLog(): void
    {
        $this->queryLog = [];
    }
}
