<?php

declare(strict_types=1);

namespace EticSimple\Modules\Product;

use DateTime;

/**
 * Category Model
 * 
 * Ürün kategorisi veri modeli
 */
class Category
{
    private ?int $id = null;
    private ?int $parentId = null;
    private string $name;
    private string $slug;
    private ?string $description = null;
    private ?string $imageUrl = null;
    private ?string $icon = null;
    private int $sortOrder = 0;
    private bool $isActive = true;
    private bool $isFeatured = false;
    private ?string $metaTitle = null;
    private ?string $metaDescription = null;
    private ?string $metaKeywords = null;
    private ?DateTime $createdAt = null;
    private ?DateTime $updatedAt = null;
    
    // İlişkili veriler
    private ?Category $parent = null;
    private array $children = [];
    private int $productCount = 0;
    
    public function __construct(array $data = [])
    {
        if (!empty($data)) {
            $this->fill($data);
        }
    }
    
    /**
     * Array'den model doldur
     */
    public function fill(array $data): self
    {
        foreach ($data as $key => $value) {
            $method = 'set' . str_replace('_', '', ucwords($key, '_'));
            if (method_exists($this, $method)) {
                $this->$method($value);
            }
        }
        
        return $this;
    }
    
    /**
     * Model'i array'e çevir
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'parent_id' => $this->parentId,
            'name' => $this->name,
            'slug' => $this->slug,
            'description' => $this->description,
            'image_url' => $this->imageUrl,
            'icon' => $this->icon,
            'sort_order' => $this->sortOrder,
            'is_active' => $this->isActive,
            'is_featured' => $this->isFeatured,
            'meta_title' => $this->metaTitle,
            'meta_description' => $this->metaDescription,
            'meta_keywords' => $this->metaKeywords,
            'created_at' => $this->createdAt?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updatedAt?->format('Y-m-d H:i:s'),
            'product_count' => $this->productCount,
        ];
    }
    
    /**
     * Hiyerarşik array (alt kategoriler dahil)
     */
    public function toHierarchicalArray(): array
    {
        $data = $this->toArray();
        
        if (!empty($this->children)) {
            $data['children'] = [];
            foreach ($this->children as $child) {
                $data['children'][] = $child->toHierarchicalArray();
            }
        }
        
        return $data;
    }
    
    /**
     * Breadcrumb array
     */
    public function getBreadcrumb(): array
    {
        $breadcrumb = [];
        
        if ($this->parent) {
            $breadcrumb = $this->parent->getBreadcrumb();
        }
        
        $breadcrumb[] = [
            'id' => $this->id,
            'name' => $this->name,
            'slug' => $this->slug
        ];
        
        return $breadcrumb;
    }
    
    /**
     * Kategori seviyesi (0 = ana kategori)
     */
    public function getLevel(): int
    {
        if (!$this->parent) {
            return 0;
        }
        
        return $this->parent->getLevel() + 1;
    }
    
    /**
     * Alt kategori var mı?
     */
    public function hasChildren(): bool
    {
        return !empty($this->children);
    }
    
    /**
     * Ana kategori mi?
     */
    public function isRoot(): bool
    {
        return $this->parentId === null;
    }
    
    /**
     * URL oluştur
     */
    public function getUrl(): string
    {
        $breadcrumb = $this->getBreadcrumb();
        $slugs = array_column($breadcrumb, 'slug');
        
        return '/kategori/' . implode('/', $slugs);
    }
    
    /**
     * SEO başlığı
     */
    public function getSeoTitle(): string
    {
        return $this->metaTitle ?: $this->name;
    }
    
    /**
     * SEO açıklaması
     */
    public function getSeoDescription(): string
    {
        return $this->metaDescription ?: $this->description ?: '';
    }
    
    // Getter ve Setter metodları
    
    public function getId(): ?int
    {
        return $this->id;
    }
    
    public function setId(?int $id): self
    {
        $this->id = $id;
        return $this;
    }
    
    public function getParentId(): ?int
    {
        return $this->parentId;
    }
    
    public function setParentId(?int $parentId): self
    {
        $this->parentId = $parentId;
        return $this;
    }
    
    public function getName(): string
    {
        return $this->name;
    }
    
    public function setName(string $name): self
    {
        $this->name = trim($name);
        return $this;
    }
    
    public function getSlug(): string
    {
        return $this->slug;
    }
    
    public function setSlug(string $slug): self
    {
        $this->slug = trim($slug);
        return $this;
    }
    
    public function getDescription(): ?string
    {
        return $this->description;
    }
    
    public function setDescription(?string $description): self
    {
        $this->description = $description ? trim($description) : null;
        return $this;
    }
    
    public function getImageUrl(): ?string
    {
        return $this->imageUrl;
    }
    
    public function setImageUrl(?string $imageUrl): self
    {
        $this->imageUrl = $imageUrl ? trim($imageUrl) : null;
        return $this;
    }
    
    public function getIcon(): ?string
    {
        return $this->icon;
    }
    
    public function setIcon(?string $icon): self
    {
        $this->icon = $icon ? trim($icon) : null;
        return $this;
    }
    
    public function getSortOrder(): int
    {
        return $this->sortOrder;
    }
    
    public function setSortOrder(int $sortOrder): self
    {
        $this->sortOrder = $sortOrder;
        return $this;
    }
    
    public function isActive(): bool
    {
        return $this->isActive;
    }
    
    public function setIsActive(bool $isActive): self
    {
        $this->isActive = $isActive;
        return $this;
    }
    
    public function isFeatured(): bool
    {
        return $this->isFeatured;
    }
    
    public function setIsFeatured(bool $isFeatured): self
    {
        $this->isFeatured = $isFeatured;
        return $this;
    }
    
    public function getParent(): ?Category
    {
        return $this->parent;
    }
    
    public function setParent(?Category $parent): self
    {
        $this->parent = $parent;
        return $this;
    }
    
    public function getChildren(): array
    {
        return $this->children;
    }
    
    public function setChildren(array $children): self
    {
        $this->children = $children;
        return $this;
    }
    
    public function addChild(Category $child): self
    {
        $this->children[] = $child;
        $child->setParent($this);
        return $this;
    }
    
    public function getProductCount(): int
    {
        return $this->productCount;
    }
    
    public function setProductCount(int $productCount): self
    {
        $this->productCount = $productCount;
        return $this;
    }
    
    public function getCreatedAt(): ?DateTime
    {
        return $this->createdAt;
    }
    
    public function setCreatedAt($createdAt): self
    {
        if (is_string($createdAt)) {
            $this->createdAt = new DateTime($createdAt);
        } elseif ($createdAt instanceof DateTime) {
            $this->createdAt = $createdAt;
        }
        return $this;
    }
    
    public function getUpdatedAt(): ?DateTime
    {
        return $this->updatedAt;
    }
    
    public function setUpdatedAt($updatedAt): self
    {
        if (is_string($updatedAt)) {
            $this->updatedAt = new DateTime($updatedAt);
        } elseif ($updatedAt instanceof DateTime) {
            $this->updatedAt = $updatedAt;
        }
        return $this;
    }
}
