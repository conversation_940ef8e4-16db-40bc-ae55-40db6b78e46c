<?php

declare(strict_types=1);

// Minimal test - sadece temel sınıfları test et
header('Content-Type: application/json; charset=utf-8');

// Composer autoloader
require_once dirname(__DIR__, 2) . '/vendor/autoload.php';

try {
    // Environment yükle
    $dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__, 2));
    $dotenv->load();
    
    echo json_encode([
        'success' => true,
        'message' => 'Environment yüklendi',
        'data' => [
            'app_env' => $_ENV['APP_ENV'] ?? 'unknown',
            'app_debug' => $_ENV['APP_DEBUG'] ?? 'unknown',
            'cache_driver' => $_ENV['CACHE_DRIVER'] ?? 'unknown'
        ]
    ], JSON_UNESCAPED_UNICODE);
    
} catch (\Throwable $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Minimal test hatası: ' . $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ], JSON_UNESCAPED_UNICODE);
}
