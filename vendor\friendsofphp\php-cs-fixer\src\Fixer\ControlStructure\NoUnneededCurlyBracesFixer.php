<?php

declare(strict_types=1);

/*
 * This file is part of PHP CS Fixer.
 *
 * (c) <PERSON><PERSON><PERSON> Potencier <<EMAIL>>
 *     <PERSON><PERSON> <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled
 * with this source code in the file LICENSE.
 */

namespace PhpCsFixer\Fixer\ControlStructure;

use PhpCs<PERSON><PERSON>er\AbstractProxyFixer;
use PhpCsFixer\Fixer\ConfigurableFixerInterface;
use PhpCsFixer\Fixer\ConfigurableFixerTrait;
use PhpCsFixer\Fixer\DeprecatedFixerInterface;
use PhpCsFixer\FixerConfiguration\FixerConfigurationResolver;
use PhpCsFixer\FixerConfiguration\FixerConfigurationResolverInterface;
use PhpCsFixer\FixerConfiguration\FixerOptionBuilder;
use PhpCsFix<PERSON>\FixerDefinition\FixerDefinition;
use <PERSON>p<PERSON><PERSON><PERSON><PERSON>\FixerDefinition\FixerDefinitionInterface;

/**
 * @deprecated
 *
 * @implements ConfigurableFixerInterface<_AutogeneratedInputConfiguration, _AutogeneratedComputedConfiguration>
 *
 * @phpstan-type _AutogeneratedInputConfiguration array{
 *  namespaces?: bool,
 * }
 * @phpstan-type _AutogeneratedComputedConfiguration array{
 *  namespaces: bool,
 * }
 */
final class NoUnneededCurlyBracesFixer extends AbstractProxyFixer implements ConfigurableFixerInterface, DeprecatedFixerInterface
{
    /** @use ConfigurableFixerTrait<_AutogeneratedInputConfiguration, _AutogeneratedComputedConfiguration> */
    use ConfigurableFixerTrait;

    private NoUnneededBracesFixer $noUnneededBracesFixer;

    public function __construct()
    {
        $this->noUnneededBracesFixer = new NoUnneededBracesFixer();

        parent::__construct();
    }

    public function getDefinition(): FixerDefinitionInterface
    {
        $fixerDefinition = $this->noUnneededBracesFixer->getDefinition();

        return new FixerDefinition(
            'Removes unneeded curly braces that are superfluous and aren\'t part of a control structure\'s body.',
            $fixerDefinition->getCodeSamples(),
            $fixerDefinition->getDescription(),
            $fixerDefinition->getRiskyDescription()
        );
    }

    /**
     * {@inheritdoc}
     *
     * Must run before NoUselessElseFixer, NoUselessReturnFixer, ReturnAssignmentFixer, SimplifiedIfReturnFixer.
     */
    public function getPriority(): int
    {
        return $this->noUnneededBracesFixer->getPriority();
    }

    public function getSuccessorsNames(): array
    {
        return [
            $this->noUnneededBracesFixer->getName(),
        ];
    }

    /**
     * @param _AutogeneratedInputConfiguration $configuration
     */
    protected function configurePreNormalisation(array $configuration): void
    {
        $this->noUnneededBracesFixer->configure($configuration);
    }

    protected function createConfigurationDefinition(): FixerConfigurationResolverInterface
    {
        return new FixerConfigurationResolver([
            (new FixerOptionBuilder('namespaces', 'Remove unneeded curly braces from bracketed namespaces.'))
                ->setAllowedTypes(['bool'])
                ->setDefault(false)
                ->getOption(),
        ]);
    }

    protected function createProxyFixers(): array
    {
        return [
            $this->noUnneededBracesFixer,
        ];
    }
}
