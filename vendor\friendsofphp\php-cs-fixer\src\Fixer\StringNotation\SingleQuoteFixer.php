<?php

declare(strict_types=1);

/*
 * This file is part of PHP CS Fixer.
 *
 * (c) Fabi<PERSON> Potencier <<EMAIL>>
 *     <PERSON><PERSON> <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled
 * with this source code in the file LICENSE.
 */

namespace Php<PERSON><PERSON>ixer\Fixer\StringNotation;

use PhpCs<PERSON><PERSON><PERSON>\AbstractFixer;
use PhpCsFixer\Fixer\ConfigurableFixerInterface;
use PhpCsFixer\Fixer\ConfigurableFixerTrait;
use PhpCsFixer\FixerConfiguration\FixerConfigurationResolver;
use PhpCsFixer\FixerConfiguration\FixerConfigurationResolverInterface;
use PhpCsFixer\FixerConfiguration\FixerOptionBuilder;
use PhpCsFixer\FixerDefinition\CodeSample;
use PhpCsFixer\FixerDefinition\FixerDefinition;
use PhpCsFix<PERSON>\FixerDefinition\FixerDefinitionInterface;
use <PERSON>p<PERSON><PERSON><PERSON><PERSON>\Preg;
use PhpCsFixer\Tokenizer\Token;
use PhpCs<PERSON><PERSON><PERSON>\Tokenizer\Tokens;

/**
 * <AUTHOR> Harlan <<EMAIL>>
 *
 * @implements ConfigurableFixerInterface<_AutogeneratedInputConfiguration, _AutogeneratedComputedConfiguration>
 *
 * @phpstan-type _AutogeneratedInputConfiguration array{
 *  strings_containing_single_quote_chars?: bool,
 * }
 * @phpstan-type _AutogeneratedComputedConfiguration array{
 *  strings_containing_single_quote_chars: bool,
 * }
 */
final class SingleQuoteFixer extends AbstractFixer implements ConfigurableFixerInterface
{
    /** @use ConfigurableFixerTrait<_AutogeneratedInputConfiguration, _AutogeneratedComputedConfiguration> */
    use ConfigurableFixerTrait;

    public function getDefinition(): FixerDefinitionInterface
    {
        $codeSample = <<<'EOF'
            <?php

            $a = "sample";
            $b = "sample with 'single-quotes'";

            EOF;

        return new FixerDefinition(
            'Convert double quotes to single quotes for simple strings.',
            [
                new CodeSample($codeSample),
                new CodeSample(
                    $codeSample,
                    ['strings_containing_single_quote_chars' => true]
                ),
            ]
        );
    }

    /**
     * {@inheritdoc}
     *
     * Must run before NoUselessConcatOperatorFixer.
     * Must run after BacktickToShellExecFixer, EscapeImplicitBackslashesFixer, StringImplicitBackslashesFixer.
     */
    public function getPriority(): int
    {
        return 10;
    }

    public function isCandidate(Tokens $tokens): bool
    {
        return $tokens->isTokenKindFound(T_CONSTANT_ENCAPSED_STRING);
    }

    protected function applyFix(\SplFileInfo $file, Tokens $tokens): void
    {
        foreach ($tokens as $index => $token) {
            if (!$token->isGivenKind(T_CONSTANT_ENCAPSED_STRING)) {
                continue;
            }

            $content = $token->getContent();
            $prefix = '';

            if ('b' === strtolower($content[0])) {
                $prefix = $content[0];
                $content = substr($content, 1);
            }

            if (
                '"' === $content[0]
                && (true === $this->configuration['strings_containing_single_quote_chars'] || !str_contains($content, "'"))
                // regex: odd number of backslashes, not followed by double quote or dollar
                && !Preg::match('/(?<!\\\)(?:\\\{2})*\\\(?!["$\\\])/', $content)
            ) {
                $content = substr($content, 1, -1);
                $content = str_replace(['\"', '\$', '\''], ['"', '$', '\\\''], $content);
                $tokens[$index] = new Token([T_CONSTANT_ENCAPSED_STRING, $prefix.'\''.$content.'\'']);
            }
        }
    }

    protected function createConfigurationDefinition(): FixerConfigurationResolverInterface
    {
        return new FixerConfigurationResolver([
            (new FixerOptionBuilder('strings_containing_single_quote_chars', 'Whether to fix double-quoted strings that contains single-quotes.'))
                ->setAllowedTypes(['bool'])
                ->setDefault(false)
                ->getOption(),
        ]);
    }
}
